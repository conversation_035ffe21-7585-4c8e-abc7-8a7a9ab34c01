/**
 * Circuit Breaker Pattern Implementation
 * 
 * Provides fault tolerance by monitoring failures and preventing calls to failing services:
 * - CLOSED: Normal operation, calls pass through
 * - OPEN: Service is failing, calls are rejected immediately
 * - HALF_OPEN: Testing if service has recovered
 */

export enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

export interface CircuitBreakerConfig {
  /** Number of consecutive failures to trigger OPEN state (default: 5) */
  failureThreshold: number;
  
  /** Time in milliseconds to wait before transitioning to HALF_OPEN (default: 30000) */
  recoveryTimeout: number;
  
  /** Number of successful calls in <PERSON><PERSON>F_OPEN to transition to CLOSED (default: 2) */
  successThreshold: number;
  
  /** Time window in milliseconds for failure counting (default: 60000) */
  monitoringWindow: number;
  
  /** Custom function to determine if an error should count as a failure */
  isFailure?: (error: Error) => boolean;
  
  /** Callback when state changes */
  onStateChange?: (oldState: CircuitBreakerState, newState: CircuitBreakerState, serviceName: string) => void;
}

export interface CircuitBreakerMetrics {
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  stateChangedAt: Date;
  totalCalls: number;
  totalFailures: number;
  totalSuccesses: number;
}

export class CircuitBreakerError extends Error {
  constructor(serviceName: string, state: CircuitBreakerState) {
    super(`Circuit breaker is ${state} for service: ${serviceName}`);
    this.name = 'CircuitBreakerError';
  }
}

export class CircuitBreaker {
  private config: CircuitBreakerConfig;
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private stateChangedAt: Date = new Date();
  private totalCalls: number = 0;
  private totalFailures: number = 0;
  private totalSuccesses: number = 0;
  private serviceName: string;

  constructor(serviceName: string, config: Partial<CircuitBreakerConfig> = {}) {
    this.serviceName = serviceName;
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      successThreshold: 2,
      monitoringWindow: 60000,
      ...config
    };
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    this.totalCalls++;

    // Check if circuit breaker should allow the call
    if (!this.canExecute()) {
      throw new CircuitBreakerError(this.serviceName, this.state);
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Check if the circuit breaker allows execution
   */
  private canExecute(): boolean {
    switch (this.state) {
      case CircuitBreakerState.CLOSED:
        return true;
      
      case CircuitBreakerState.OPEN:
        // Check if recovery timeout has passed
        if (this.shouldAttemptRecovery()) {
          this.transitionTo(CircuitBreakerState.HALF_OPEN);
          return true;
        }
        return false;
      
      case CircuitBreakerState.HALF_OPEN:
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Handle successful operation
   */
  private onSuccess(): void {
    this.successCount++;
    this.totalSuccesses++;
    this.lastSuccessTime = new Date();

    switch (this.state) {
      case CircuitBreakerState.HALF_OPEN:
        if (this.successCount >= this.config.successThreshold) {
          this.transitionTo(CircuitBreakerState.CLOSED);
        }
        break;
      
      case CircuitBreakerState.CLOSED:
        // Reset failure count on success in closed state
        this.failureCount = 0;
        break;
    }
  }

  /**
   * Handle failed operation
   */
  private onFailure(error: Error): void {
    // Check if this error should count as a failure
    if (this.config.isFailure && !this.config.isFailure(error)) {
      return;
    }

    this.failureCount++;
    this.totalFailures++;
    this.lastFailureTime = new Date();

    switch (this.state) {
      case CircuitBreakerState.CLOSED:
        if (this.failureCount >= this.config.failureThreshold) {
          this.transitionTo(CircuitBreakerState.OPEN);
        }
        break;
      
      case CircuitBreakerState.HALF_OPEN:
        // Any failure in half-open state transitions back to open
        this.transitionTo(CircuitBreakerState.OPEN);
        break;
    }
  }

  /**
   * Transition to a new state
   */
  private transitionTo(newState: CircuitBreakerState): void {
    const oldState = this.state;
    this.state = newState;
    this.stateChangedAt = new Date();

    // Reset counters based on new state
    switch (newState) {
      case CircuitBreakerState.CLOSED:
        this.failureCount = 0;
        this.successCount = 0;
        break;
      
      case CircuitBreakerState.OPEN:
        this.successCount = 0;
        break;
      
      case CircuitBreakerState.HALF_OPEN:
        this.successCount = 0;
        break;
    }

    // Notify state change
    if (this.config.onStateChange) {
      this.config.onStateChange(oldState, newState, this.serviceName);
    }
  }

  /**
   * Check if we should attempt recovery from OPEN state
   */
  private shouldAttemptRecovery(): boolean {
    if (!this.lastFailureTime) {
      return true;
    }

    const timeSinceLastFailure = Date.now() - this.lastFailureTime.getTime();
    return timeSinceLastFailure >= this.config.recoveryTimeout;
  }

  /**
   * Get current metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      stateChangedAt: this.stateChangedAt,
      totalCalls: this.totalCalls,
      totalFailures: this.totalFailures,
      totalSuccesses: this.totalSuccesses
    };
  }

  /**
   * Get current state
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * Force state transition (for testing or manual intervention)
   */
  forceState(state: CircuitBreakerState): void {
    this.transitionTo(state);
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = undefined;
    this.lastSuccessTime = undefined;
    this.stateChangedAt = new Date();
    this.totalCalls = 0;
    this.totalFailures = 0;
    this.totalSuccesses = 0;
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<CircuitBreakerConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Get current configuration
   */
  getConfig(): CircuitBreakerConfig {
    return { ...this.config };
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }
}

/**
 * Circuit Breaker Registry for managing multiple circuit breakers
 */
export class CircuitBreakerRegistry {
  private breakers = new Map<string, CircuitBreaker>();
  private defaultConfig: Partial<CircuitBreakerConfig>;

  constructor(defaultConfig: Partial<CircuitBreakerConfig> = {}) {
    this.defaultConfig = defaultConfig;
  }

  /**
   * Get or create a circuit breaker for a service
   */
  getBreaker(serviceName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.breakers.has(serviceName)) {
      const breakerConfig = { ...this.defaultConfig, ...config };
      this.breakers.set(serviceName, new CircuitBreaker(serviceName, breakerConfig));
    }
    return this.breakers.get(serviceName)!;
  }

  /**
   * Execute operation with circuit breaker protection
   */
  async execute<T>(
    serviceName: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>
  ): Promise<T> {
    const breaker = this.getBreaker(serviceName, config);
    return breaker.execute(operation);
  }

  /**
   * Get all circuit breakers
   */
  getAllBreakers(): Map<string, CircuitBreaker> {
    return new Map(this.breakers);
  }

  /**
   * Get metrics for all circuit breakers
   */
  getAllMetrics(): Record<string, CircuitBreakerMetrics> {
    const metrics: Record<string, CircuitBreakerMetrics> = {};
    for (const [serviceName, breaker] of this.breakers) {
      metrics[serviceName] = breaker.getMetrics();
    }
    return metrics;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
  }

  /**
   * Remove a circuit breaker
   */
  removeBreaker(serviceName: string): boolean {
    return this.breakers.delete(serviceName);
  }
}
