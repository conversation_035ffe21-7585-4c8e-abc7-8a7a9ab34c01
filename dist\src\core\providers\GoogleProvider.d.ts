import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';
export declare class GoogleProvider extends BaseProvider {
    name: string;
    private client;
    private model;
    constructor(config: ProviderConfig);
    sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    streamMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    private getModel;
    private processResponse;
    private formatToolCalls;
    protected formatMessages(messages: ChatMessage[]): any[];
    protected formatTools(tools?: BaseTool[]): any[];
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    protected validateConfig(): void;
}
//# sourceMappingURL=GoogleProvider.d.ts.map