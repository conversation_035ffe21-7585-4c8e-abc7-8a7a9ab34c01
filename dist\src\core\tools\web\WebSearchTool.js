"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSearchTool = void 0;
const axios_1 = __importDefault(require("axios"));
const BaseTool_1 = require("../base/BaseTool");
class WebSearchTool extends BaseTool_1.BaseTool {
    name = 'web_search';
    description = 'Searches the web using various search engines and returns formatted results';
    requiresConfirmation = false;
    parameters = {
        type: 'object',
        properties: {
            query: {
                type: 'string',
                description: 'Search query to execute',
            },
            num_results: {
                type: 'number',
                description: 'Number of search results to return',
                default: 10,
                minimum: 1,
                maximum: 50,
            },
            search_engine: {
                type: 'string',
                enum: ['duckduckgo', 'bing', 'google'],
                description: 'Search engine to use',
                default: 'duckduckgo',
            },
            safe_search: {
                type: 'boolean',
                description: 'Enable safe search filtering',
                default: true,
            },
            region: {
                type: 'string',
                description: 'Search region/country code (e.g., "us", "uk", "de")',
                default: 'us',
            },
            time_range: {
                type: 'string',
                enum: ['any', 'day', 'week', 'month', 'year'],
                description: 'Time range for search results',
                default: 'any',
            },
        },
        required: ['query'],
    };
    constructor() {
        super({
            category: 'web',
            tags: ['search', 'web', 'information'],
            version: '1.0.0',
            dangerous: false,
            requiresConfirmation: false,
        });
    }
    async validate(params) {
        const errors = [];
        const warnings = [];
        // Validate required parameters
        const requiredValidation = this.validateRequiredParams(params, ['query']);
        if (!requiredValidation.valid) {
            return requiredValidation;
        }
        // Validate query
        const queryErrors = this.validateStringParam(params.query, 'query');
        errors.push(...queryErrors);
        if (params.query && params.query.trim().length < 2) {
            errors.push('Search query must be at least 2 characters long');
        }
        // Validate optional parameters
        if (params.num_results !== undefined) {
            errors.push(...this.validateNumberParam(params.num_results, 'num_results', {
                min: 1,
                max: 50,
                integer: true,
            }));
        }
        if (params.search_engine !== undefined) {
            const validEngines = ['duckduckgo', 'bing', 'google'];
            if (!validEngines.includes(params.search_engine)) {
                errors.push(`Search engine must be one of: ${validEngines.join(', ')}`);
            }
        }
        if (params.time_range !== undefined) {
            const validRanges = ['any', 'day', 'week', 'month', 'year'];
            if (!validRanges.includes(params.time_range)) {
                errors.push(`Time range must be one of: ${validRanges.join(', ')}`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    async execute(params) {
        try {
            const { query, num_results = 10, search_engine = 'duckduckgo', safe_search = true, region = 'us', time_range = 'any', } = params;
            const startTime = Date.now();
            let searchResult;
            switch (search_engine) {
                case 'duckduckgo':
                    searchResult = await this.searchDuckDuckGo(query, num_results, safe_search, region);
                    break;
                case 'bing':
                    searchResult = await this.searchBing(query, num_results, safe_search, region, time_range);
                    break;
                case 'google':
                    searchResult = await this.searchGoogle(query, num_results, safe_search, region, time_range);
                    break;
                default:
                    throw new Error(`Unsupported search engine: ${search_engine}`);
            }
            searchResult.searchTime = Date.now() - startTime;
            const formattedResults = this.formatSearchResults(searchResult);
            return this.createSuccessResult(JSON.stringify(searchResult, null, 2), formattedResults, {
                query: searchResult.query,
                totalResults: searchResult.totalResults,
                provider: searchResult.provider,
                searchTime: searchResult.searchTime,
            });
        }
        catch (error) {
            return this.createErrorResult(`Web search failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async searchDuckDuckGo(query, numResults, safeSearch, region) {
        try {
            // DuckDuckGo Instant Answer API (limited but free)
            const response = await axios_1.default.get('https://api.duckduckgo.com/', {
                params: {
                    q: query,
                    format: 'json',
                    no_html: '1',
                    skip_disambig: '1',
                    safe_search: safeSearch ? 'strict' : 'off',
                },
                timeout: 10000,
            });
            const data = response.data;
            const results = [];
            // Process instant answer
            if (data.AbstractText) {
                results.push({
                    title: data.Heading || 'DuckDuckGo Instant Answer',
                    url: data.AbstractURL || '',
                    snippet: data.AbstractText,
                    displayUrl: data.AbstractSource || '',
                });
            }
            // Process related topics
            if (data.RelatedTopics) {
                for (const topic of data.RelatedTopics.slice(0, numResults - results.length)) {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: topic.Text.split(' - ')[0] || 'Related Topic',
                            url: topic.FirstURL,
                            snippet: topic.Text,
                            displayUrl: topic.FirstURL,
                        });
                    }
                }
            }
            return {
                query,
                results: results.slice(0, numResults),
                totalResults: results.length,
                searchTime: 0,
                provider: 'DuckDuckGo',
            };
        }
        catch (error) {
            throw new Error(`DuckDuckGo search failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async searchBing(query, numResults, safeSearch, region, timeRange) {
        // Note: This would require Bing Search API key
        // For now, return a placeholder implementation
        return {
            query,
            results: [{
                    title: 'Bing Search Not Configured',
                    url: 'https://www.bing.com',
                    snippet: 'Bing search requires API key configuration',
                    displayUrl: 'bing.com',
                }],
            totalResults: 1,
            searchTime: 0,
            provider: 'Bing (Not Configured)',
        };
    }
    async searchGoogle(query, numResults, safeSearch, region, timeRange) {
        // Note: This would require Google Custom Search API key
        // For now, return a placeholder implementation
        return {
            query,
            results: [{
                    title: 'Google Search Not Configured',
                    url: 'https://www.google.com',
                    snippet: 'Google search requires Custom Search API key configuration',
                    displayUrl: 'google.com',
                }],
            totalResults: 1,
            searchTime: 0,
            provider: 'Google (Not Configured)',
        };
    }
    formatSearchResults(searchResult) {
        const lines = [];
        lines.push(`Web Search Results`);
        lines.push(`Query: "${searchResult.query}"`);
        lines.push(`Provider: ${searchResult.provider}`);
        lines.push(`Results: ${searchResult.results.length} of ${searchResult.totalResults}`);
        lines.push(`Search Time: ${searchResult.searchTime}ms`);
        lines.push('');
        for (let i = 0; i < searchResult.results.length; i++) {
            const result = searchResult.results[i];
            lines.push(`${i + 1}. ${result.title}`);
            lines.push(`   ${result.url}`);
            lines.push(`   ${result.snippet}`);
            lines.push('');
        }
        return lines.join('\n');
    }
}
exports.WebSearchTool = WebSearchTool;
//# sourceMappingURL=WebSearchTool.js.map