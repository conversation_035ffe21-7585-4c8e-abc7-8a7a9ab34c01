import { ErrorClassifier } from '../../../src/core/error-handling/ErrorClassifier';
import { TestError, NetworkError, TimeoutError, RateLimitError } from '../../utils/mocks';

describe('ErrorClassifier', () => {
  let classifier: ErrorClassifier;

  beforeEach(() => {
    classifier = new ErrorClassifier();
  });

  describe('isRetryable', () => {
    it('should classify network errors as retryable', () => {
      const error = new NetworkError('Connection failed');
      expect(classifier.isRetryable(error)).toBe(true);
    });

    it('should classify timeout errors as retryable', () => {
      const error = new TimeoutError('Request timed out');
      expect(classifier.isRetryable(error)).toBe(true);
    });

    it('should classify rate limit errors as retryable', () => {
      const error = new RateLimitError('Too many requests');
      expect(classifier.isRetryable(error)).toBe(true);
    });

    it('should classify errors with retryable property correctly', () => {
      const retryableError = new TestError('Retryable error', 'TEST_ERROR', true);
      const nonRetryableError = new TestError('Non-retryable error', 'TEST_ERROR', false);
      
      expect(classifier.isRetryable(retryableError)).toBe(true);
      expect(classifier.isRetryable(nonRetryableError)).toBe(false);
    });

    it('should classify HTTP errors based on status codes', () => {
      // Retryable HTTP errors (5xx, 429, 408)
      const serverError = new Error('Internal Server Error');
      (serverError as any).status = 500;
      expect(classifier.isRetryable(serverError)).toBe(true);

      const badGateway = new Error('Bad Gateway');
      (badGateway as any).status = 502;
      expect(classifier.isRetryable(badGateway)).toBe(true);

      const serviceUnavailable = new Error('Service Unavailable');
      (serviceUnavailable as any).status = 503;
      expect(classifier.isRetryable(serviceUnavailable)).toBe(true);

      const gatewayTimeout = new Error('Gateway Timeout');
      (gatewayTimeout as any).status = 504;
      expect(classifier.isRetryable(gatewayTimeout)).toBe(true);

      const tooManyRequests = new Error('Too Many Requests');
      (tooManyRequests as any).status = 429;
      expect(classifier.isRetryable(tooManyRequests)).toBe(true);

      const requestTimeout = new Error('Request Timeout');
      (requestTimeout as any).status = 408;
      expect(classifier.isRetryable(requestTimeout)).toBe(true);

      // Non-retryable HTTP errors (4xx except 408, 429)
      const badRequest = new Error('Bad Request');
      (badRequest as any).status = 400;
      expect(classifier.isRetryable(badRequest)).toBe(false);

      const unauthorized = new Error('Unauthorized');
      (unauthorized as any).status = 401;
      expect(classifier.isRetryable(unauthorized)).toBe(false);

      const forbidden = new Error('Forbidden');
      (forbidden as any).status = 403;
      expect(classifier.isRetryable(forbidden)).toBe(false);

      const notFound = new Error('Not Found');
      (notFound as any).status = 404;
      expect(classifier.isRetryable(notFound)).toBe(false);
    });

    it('should classify errors based on error codes', () => {
      // Retryable error codes
      const econnreset = new Error('Connection reset');
      (econnreset as any).code = 'ECONNRESET';
      expect(classifier.isRetryable(econnreset)).toBe(true);

      const econnrefused = new Error('Connection refused');
      (econnrefused as any).code = 'ECONNREFUSED';
      expect(classifier.isRetryable(econnrefused)).toBe(true);

      const etimedout = new Error('Connection timed out');
      (etimedout as any).code = 'ETIMEDOUT';
      expect(classifier.isRetryable(etimedout)).toBe(true);

      const enotfound = new Error('DNS lookup failed');
      (enotfound as any).code = 'ENOTFOUND';
      expect(classifier.isRetryable(enotfound)).toBe(true);

      // Non-retryable error codes
      const eacces = new Error('Permission denied');
      (eacces as any).code = 'EACCES';
      expect(classifier.isRetryable(eacces)).toBe(false);

      const enoent = new Error('File not found');
      (enoent as any).code = 'ENOENT';
      expect(classifier.isRetryable(enoent)).toBe(false);
    });

    it('should classify errors based on message patterns', () => {
      // Retryable message patterns
      expect(classifier.isRetryable(new Error('socket hang up'))).toBe(true);
      expect(classifier.isRetryable(new Error('ECONNRESET'))).toBe(true);
      expect(classifier.isRetryable(new Error('timeout'))).toBe(true);
      expect(classifier.isRetryable(new Error('rate limit'))).toBe(true);
      expect(classifier.isRetryable(new Error('too many requests'))).toBe(true);
      expect(classifier.isRetryable(new Error('service unavailable'))).toBe(true);
      expect(classifier.isRetryable(new Error('internal server error'))).toBe(true);

      // Non-retryable message patterns
      expect(classifier.isRetryable(new Error('invalid api key'))).toBe(false);
      expect(classifier.isRetryable(new Error('unauthorized'))).toBe(false);
      expect(classifier.isRetryable(new Error('forbidden'))).toBe(false);
      expect(classifier.isRetryable(new Error('not found'))).toBe(false);
      expect(classifier.isRetryable(new Error('bad request'))).toBe(false);
    });

    it('should default to non-retryable for unknown errors', () => {
      const unknownError = new Error('Unknown error type');
      expect(classifier.isRetryable(unknownError)).toBe(false);
    });
  });

  describe('getErrorCategory', () => {
    it('should categorize network errors correctly', () => {
      const networkError = new NetworkError();
      expect(classifier.getErrorCategory(networkError)).toBe('NETWORK');
    });

    it('should categorize timeout errors correctly', () => {
      const timeoutError = new TimeoutError();
      expect(classifier.getErrorCategory(timeoutError)).toBe('TIMEOUT');
    });

    it('should categorize rate limit errors correctly', () => {
      const rateLimitError = new RateLimitError();
      expect(classifier.getErrorCategory(rateLimitError)).toBe('RATE_LIMIT');
    });

    it('should categorize HTTP errors based on status codes', () => {
      const serverError = new Error('Internal Server Error');
      (serverError as any).status = 500;
      expect(classifier.getErrorCategory(serverError)).toBe('SERVER');

      const clientError = new Error('Bad Request');
      (clientError as any).status = 400;
      expect(classifier.getErrorCategory(clientError)).toBe('CLIENT');

      const authError = new Error('Unauthorized');
      (authError as any).status = 401;
      expect(classifier.getErrorCategory(authError)).toBe('AUTH');
    });

    it('should categorize errors based on error codes', () => {
      const connectionError = new Error('Connection failed');
      (connectionError as any).code = 'ECONNREFUSED';
      expect(classifier.getErrorCategory(connectionError)).toBe('NETWORK');

      const timeoutError = new Error('Timed out');
      (timeoutError as any).code = 'ETIMEDOUT';
      expect(classifier.getErrorCategory(timeoutError)).toBe('TIMEOUT');

      const permissionError = new Error('Permission denied');
      (permissionError as any).code = 'EACCES';
      expect(classifier.getErrorCategory(permissionError)).toBe('PERMISSION');
    });

    it('should default to UNKNOWN for unclassified errors', () => {
      const unknownError = new Error('Some random error');
      expect(classifier.getErrorCategory(unknownError)).toBe('UNKNOWN');
    });
  });

  describe('getSeverity', () => {
    it('should assign HIGH severity to auth and permission errors', () => {
      const authError = new Error('Unauthorized');
      (authError as any).status = 401;
      expect(classifier.getSeverity(authError)).toBe('HIGH');

      const permissionError = new Error('Permission denied');
      (permissionError as any).code = 'EACCES';
      expect(classifier.getSeverity(permissionError)).toBe('HIGH');
    });

    it('should assign MEDIUM severity to client and server errors', () => {
      const clientError = new Error('Bad Request');
      (clientError as any).status = 400;
      expect(classifier.getSeverity(clientError)).toBe('MEDIUM');

      const serverError = new Error('Internal Server Error');
      (serverError as any).status = 500;
      expect(classifier.getSeverity(serverError)).toBe('MEDIUM');
    });

    it('should assign LOW severity to network and timeout errors', () => {
      const networkError = new NetworkError();
      expect(classifier.getSeverity(networkError)).toBe('LOW');

      const timeoutError = new TimeoutError();
      expect(classifier.getSeverity(timeoutError)).toBe('LOW');

      const rateLimitError = new RateLimitError();
      expect(classifier.getSeverity(rateLimitError)).toBe('LOW');
    });

    it('should assign MEDIUM severity to unknown errors', () => {
      const unknownError = new Error('Unknown error');
      expect(classifier.getSeverity(unknownError)).toBe('MEDIUM');
    });
  });

  describe('getRetryDelay', () => {
    it('should suggest longer delays for rate limit errors', () => {
      const rateLimitError = new RateLimitError();
      const delay = classifier.getRetryDelay(rateLimitError, 1, 1000);
      expect(delay).toBeGreaterThan(1000);
    });

    it('should respect Retry-After header for rate limit errors', () => {
      const rateLimitError = new RateLimitError();
      (rateLimitError as any).headers = { 'retry-after': '60' };
      
      const delay = classifier.getRetryDelay(rateLimitError, 1, 1000);
      expect(delay).toBe(60000); // 60 seconds in milliseconds
    });

    it('should return base delay for other error types', () => {
      const networkError = new NetworkError();
      const delay = classifier.getRetryDelay(networkError, 1, 1000);
      expect(delay).toBe(1000);
    });

    it('should handle invalid Retry-After headers gracefully', () => {
      const rateLimitError = new RateLimitError();
      (rateLimitError as any).headers = { 'retry-after': 'invalid' };
      
      const delay = classifier.getRetryDelay(rateLimitError, 1, 1000);
      expect(delay).toBeGreaterThan(1000); // Should fall back to default rate limit delay
    });
  });

  describe('edge cases', () => {
    it('should handle null and undefined errors gracefully', () => {
      expect(classifier.isRetryable(null as any)).toBe(false);
      expect(classifier.isRetryable(undefined as any)).toBe(false);
      expect(classifier.getErrorCategory(null as any)).toBe('UNKNOWN');
      expect(classifier.getSeverity(null as any)).toBe('MEDIUM');
    });

    it('should handle errors without message', () => {
      const error = new Error();
      error.message = '';
      
      expect(classifier.isRetryable(error)).toBe(false);
      expect(classifier.getErrorCategory(error)).toBe('UNKNOWN');
    });

    it('should handle errors with non-string properties', () => {
      const error = new Error('Test error');
      (error as any).status = 'not-a-number';
      (error as any).code = 123;
      
      expect(() => classifier.isRetryable(error)).not.toThrow();
      expect(() => classifier.getErrorCategory(error)).not.toThrow();
    });
  });
});
