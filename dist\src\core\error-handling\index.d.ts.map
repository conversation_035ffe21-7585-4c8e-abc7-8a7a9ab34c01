{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;GAUG;AAGH,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AACxF,OAAO,EACL,cAAc,EACd,oBAAoB,EACpB,mBAAmB,EACnB,sBAAsB,EACtB,mBAAmB,EACpB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAGxF,OAAO,EACL,uBAAuB,EACvB,oBAAoB,EACpB,mBAAmB,EACnB,gBAAgB,EACjB,MAAM,2BAA2B,CAAC;AAGnC,OAAO,EACL,gBAAgB,EAChB,YAAY,EACZ,qBAAqB,EACrB,YAAY,EACZ,kBAAkB,EAClB,mBAAmB,EACnB,eAAe,EACf,WAAW,EACZ,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EACL,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,YAAY,EACb,MAAM,eAAe,CAAC;AAGvB,OAAO,EACL,0BAA0B,EAC1B,kBAAkB,EAClB,2BAA2B,EAC5B,MAAM,UAAU,CAAC;AAGlB,eAAO,MAAM,oBAAoB;;;;;;;;;CAShC,CAAC;AAEF,eAAO,MAAM,8BAA8B;;;;;;CAM1C,CAAC;AAEF,eAAO,MAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCjC,CAAC;AAEF;;GAEG;AACH,wBAAgB,6BAA6B,CAC3C,MAAM,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,GAC3C,uBAAuB,CAkBzB;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAC/B,MAAM,EAAE,MAAM,OAAO,qBAAqB,GAAG,iBAAiB,GAC7D,WAAW,CAKb;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAClC,WAAW,EAAE,MAAM,EACnB,MAAM,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,GACrC,cAAc,CAGhB;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAItD;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,aAAa,CAI5D;AAED;;GAEG;AACH,wBAAgB,0BAA0B,CACxC,aAAa,EAAE,eAAe,GAAG,cAAc,GAAG,eAAe,GAAG,gBAAgB,GAAG,SAAS,EAChG,aAAa,EAAE,MAAM,EACrB,OAAO,CAAC,EAAE;IACR,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC,GACA,oBAAoB,CAStB;AAED;;GAEG;AACH,eAAO,MAAM,qBAAqB;;;;;CAaxB,CAAC"}