import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export declare class LSTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            path: {
                type: string;
                description: string;
            };
            recursive: {
                type: string;
                description: string;
                default: boolean;
            };
            showHidden: {
                type: string;
                description: string;
                default: boolean;
            };
            sortBy: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            maxDepth: {
                type: string;
                description: string;
                default: number;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private listDirectory;
    private sortEntries;
    private formatDirectoryListing;
    private createSummary;
    private getPermissions;
}
//# sourceMappingURL=LSTool.d.ts.map