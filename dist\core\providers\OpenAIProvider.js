"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const openai_1 = __importDefault(require("openai"));
const BaseProvider_1 = require("./BaseProvider");
class OpenAIProvider extends BaseProvider_1.BaseProvider {
    name = 'openai';
    client;
    constructor(config) {
        super(config);
        this.validateConfig();
        this.client = new openai_1.default({
            apiKey: this.config.apiKey,
            baseURL: this.config.baseUrl,
        });
    }
    async sendMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const formattedMessages = this.formatMessages(messages);
            const formattedTools = this.formatTools(tools);
            const requestParams = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                ...(formattedTools.length > 0 && { tools: formattedTools }),
            };
            // Note: System prompt is now handled by AIEngine, not here
            // if (this.config.systemPrompt) {
            //   requestParams.messages.unshift({
            //     role: 'system',
            //     content: this.config.systemPrompt,
            //   });
            // }
            const response = await this.client.chat.completions.create(requestParams);
            // Debug: Log raw response
            console.log('OpenAIProvider: Raw API response:', JSON.stringify(response, null, 2));
            return this.processResponse(response);
        }
        catch (error) {
            throw this.handleError(error, 'sendMessage');
        }
    }
    async *streamMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const formattedMessages = this.formatMessages(messages);
            const formattedTools = this.formatTools(tools);
            const requestParams = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                stream: true,
                ...(formattedTools.length > 0 && { tools: formattedTools }),
            };
            // Note: System prompt is now handled by AIEngine, not here
            // if (this.config.systemPrompt) {
            //   requestParams.messages.unshift({
            //     role: 'system',
            //     content: this.config.systemPrompt,
            //   });
            // }
            const stream = await this.client.chat.completions.create(requestParams);
            let fullContent = '';
            let toolCalls = [];
            let usage = null;
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    fullContent += delta.content;
                    yield delta.content;
                }
                if (delta?.tool_calls) {
                    toolCalls.push(...delta.tool_calls);
                }
                if (chunk.usage) {
                    usage = chunk.usage;
                }
            }
            // Debug: Log if content is empty
            if (!fullContent || fullContent.trim().length === 0) {
                console.warn('OpenAIProvider (stream): Received empty content from OpenAI');
                console.warn(`Tool calls: ${toolCalls.length}`);
            }
            return this.createChatMessage(fullContent, 'assistant', {
                provider: this.name,
                model: requestOptions.model,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens,
                } : undefined,
                toolCalls: toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined,
            });
        }
        catch (error) {
            throw this.handleError(error, 'streamMessage');
        }
    }
    processResponse(response) {
        const choice = response.choices[0];
        const message = choice.message;
        const content = message.content || '';
        const toolCalls = message.tool_calls ? this.formatToolCalls(message.tool_calls) : undefined;
        // Debug: Log if content is empty OR if finish reason is tool_calls
        if (!content || content.trim().length === 0 || choice.finish_reason === 'tool_calls') {
            console.warn('OpenAIProvider: Debug response details:');
            console.warn(`Content: "${content}"`);
            console.warn(`Finish reason: ${choice.finish_reason}`);
            console.warn(`Raw tool_calls from API:`, message.tool_calls);
            console.warn(`tool_calls type:`, typeof message.tool_calls);
            console.warn(`tool_calls is array:`, Array.isArray(message.tool_calls));
            console.warn(`Formatted tool calls: ${toolCalls?.length || 0}`);
            if (toolCalls && toolCalls.length > 0) {
                console.warn(`Tool calls:`, toolCalls);
            }
        }
        return this.createChatMessage(content, 'assistant', {
            provider: this.name,
            model: response.model,
            usage: response.usage ? {
                promptTokens: response.usage.prompt_tokens,
                completionTokens: response.usage.completion_tokens,
                totalTokens: response.usage.total_tokens,
            } : undefined,
            toolCalls,
            finishReason: choice.finish_reason,
        });
    }
    formatToolCalls(toolCalls) {
        return toolCalls.map(call => ({
            id: call.id,
            name: call.function.name,
            parameters: JSON.parse(call.function.arguments || '{}'),
        }));
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content,
            };
            // Handle tool calls in assistant messages
            if (msg.role === 'assistant' && msg.toolCalls) {
                formatted.tool_calls = msg.toolCalls.map((call) => ({
                    id: call.id,
                    type: 'function',
                    function: {
                        name: call.name,
                        arguments: JSON.stringify(call.parameters),
                    },
                }));
            }
            // Handle tool responses
            if (msg.role === 'tool') {
                formatted.tool_call_id = msg.metadata?.toolCallId;
            }
            return formatted;
        });
    }
    async testConnection() {
        try {
            const response = await this.client.models.list();
            return response.data.length > 0;
        }
        catch (error) {
            console.error('OpenAI connection test failed:', error);
            return false;
        }
    }
    async getAvailableModels() {
        try {
            const response = await this.client.models.list();
            return response.data
                .filter(model => model.id.includes('gpt'))
                .map(model => model.id)
                .sort();
        }
        catch (error) {
            console.error('Failed to fetch OpenAI models:', error);
            return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']; // Fallback to known models
        }
    }
    validateConfig() {
        super.validateConfig();
        if (!this.config.apiKey.startsWith('sk-')) {
            throw new Error('OpenAI API key should start with "sk-"');
        }
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=OpenAIProvider.js.map