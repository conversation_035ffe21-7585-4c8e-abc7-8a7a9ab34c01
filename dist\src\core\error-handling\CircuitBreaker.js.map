{"version": 3, "file": "CircuitBreaker.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/CircuitBreaker.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAEH,IAAY,mBAIX;AAJD,WAAY,mBAAmB;IAC7B,wCAAiB,CAAA;IACjB,oCAAa,CAAA;IACb,8CAAuB,CAAA;AACzB,CAAC,EAJW,mBAAmB,mCAAnB,mBAAmB,QAI9B;AAkCD,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAY,WAAmB,EAAE,KAA0B;QACzD,KAAK,CAAC,sBAAsB,KAAK,iBAAiB,WAAW,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AALD,kDAKC;AAED,MAAa,cAAc;IACjB,MAAM,CAAuB;IAC7B,KAAK,GAAwB,mBAAmB,CAAC,MAAM,CAAC;IACxD,YAAY,GAAW,CAAC,CAAC;IACzB,YAAY,GAAW,CAAC,CAAC;IACzB,eAAe,CAAQ;IACvB,eAAe,CAAQ;IACvB,cAAc,GAAS,IAAI,IAAI,EAAE,CAAC;IAClC,UAAU,GAAW,CAAC,CAAC;IACvB,aAAa,GAAW,CAAC,CAAC;IAC1B,cAAc,GAAW,CAAC,CAAC;IAC3B,WAAW,CAAS;IAE5B,YAAY,WAAmB,EAAE,SAAwC,EAAE;QACzE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG;YACZ,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,KAAK;YACvB,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,KAAK,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;YAEd,KAAK,mBAAmB,CAAC,IAAI;gBAC3B,uCAAuC;gBACvC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBACjC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBACjD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,KAAK,CAAC;YAEf,KAAK,mBAAmB,CAAC,SAAS;gBAChC,OAAO,IAAI,CAAC;YAEd;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,KAAK,mBAAmB,CAAC,SAAS;gBAChC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBACtD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM;YAER,KAAK,mBAAmB,CAAC,MAAM;gBAC7B,iDAAiD;gBACjD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,KAAY;QAC5B,gDAAgD;QAChD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,KAAK,mBAAmB,CAAC,MAAM;gBAC7B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBACtD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC9C,CAAC;gBACD,MAAM;YAER,KAAK,mBAAmB,CAAC,SAAS;gBAChC,0DAA0D;gBAC1D,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,QAA6B;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAEjC,oCAAoC;QACpC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mBAAmB,CAAC,MAAM;gBAC7B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,MAAM;YAER,KAAK,mBAAmB,CAAC,IAAI;gBAC3B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,MAAM;YAER,KAAK,mBAAmB,CAAC,SAAS;gBAChC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,MAAM;QACV,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACzE,OAAO,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;SACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAA0B;QACnC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,OAAsC;QACjD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAlOD,wCAkOC;AAED;;GAEG;AACH,MAAa,sBAAsB;IACzB,QAAQ,GAAG,IAAI,GAAG,EAA0B,CAAC;IAC7C,aAAa,CAAgC;IAErD,YAAY,gBAA+C,EAAE;QAC3D,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,WAAmB,EAAE,MAAsC;QACpE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;YAC3D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,WAAmB,EACnB,SAA2B,EAC3B,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,OAAO,GAA0C,EAAE,CAAC;QAC1D,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnD,OAAO,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAC9C,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,WAAmB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;CACF;AAhED,wDAgEC"}