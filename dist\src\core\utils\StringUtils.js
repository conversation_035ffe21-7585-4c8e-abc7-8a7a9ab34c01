"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StringUtils = void 0;
class StringUtils {
    /**
     * Truncate a string to a maximum length with ellipsis
     */
    static truncate(str, maxLength, ellipsis = '...') {
        if (str.length <= maxLength) {
            return str;
        }
        return str.slice(0, maxLength - ellipsis.length) + ellipsis;
    }
    /**
     * Capitalize the first letter of a string
     */
    static capitalize(str) {
        if (!str)
            return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    /**
     * Convert string to camelCase
     */
    static toCamelCase(str) {
        return str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
            return index === 0 ? word.toLowerCase() : word.toUpperCase();
        })
            .replace(/\s+/g, '');
    }
    /**
     * Convert string to kebab-case
     */
    static toKebabCase(str) {
        return str
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .replace(/[\s_]+/g, '-')
            .toLowerCase();
    }
    /**
     * Convert string to snake_case
     */
    static toSnakeCase(str) {
        return str
            .replace(/([a-z])([A-Z])/g, '$1_$2')
            .replace(/[\s-]+/g, '_')
            .toLowerCase();
    }
    /**
     * Convert string to PascalCase
     */
    static toPascalCase(str) {
        return str
            .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
            .replace(/\s+/g, '');
    }
    /**
     * Remove extra whitespace and normalize line endings
     */
    static normalize(str) {
        return str
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/[ \t]+/g, ' ')
            .trim();
    }
    /**
     * Escape HTML special characters
     */
    static escapeHtml(str) {
        const htmlEscapes = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;',
        };
        return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
    }
    /**
     * Unescape HTML special characters
     */
    static unescapeHtml(str) {
        const htmlUnescapes = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
        };
        return str.replace(/&(?:amp|lt|gt|quot|#39);/g, (match) => htmlUnescapes[match]);
    }
    /**
     * Escape regex special characters
     */
    static escapeRegex(str) {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    /**
     * Generate a random string
     */
    static random(length, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return result;
    }
    /**
     * Generate a UUID v4
     */
    static uuid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    /**
     * Check if string is empty or only whitespace
     */
    static isEmpty(str) {
        return !str || str.trim().length === 0;
    }
    /**
     * Check if string is a valid email
     */
    static isEmail(str) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(str);
    }
    /**
     * Check if string is a valid URL
     */
    static isUrl(str) {
        try {
            new URL(str);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Check if string is numeric
     */
    static isNumeric(str) {
        return !isNaN(Number(str)) && !isNaN(parseFloat(str));
    }
    /**
     * Extract numbers from string
     */
    static extractNumbers(str) {
        const matches = str.match(/-?\d+\.?\d*/g);
        return matches ? matches.map(Number) : [];
    }
    /**
     * Count occurrences of substring
     */
    static countOccurrences(str, substring) {
        return (str.match(new RegExp(this.escapeRegex(substring), 'g')) || []).length;
    }
    /**
     * Replace all occurrences of substring
     */
    static replaceAll(str, search, replace) {
        return str.replace(new RegExp(this.escapeRegex(search), 'g'), replace);
    }
    /**
     * Insert string at specific position
     */
    static insertAt(str, index, insertion) {
        return str.slice(0, index) + insertion + str.slice(index);
    }
    /**
     * Remove string at specific position and length
     */
    static removeAt(str, index, length = 1) {
        return str.slice(0, index) + str.slice(index + length);
    }
    /**
     * Reverse a string
     */
    static reverse(str) {
        return str.split('').reverse().join('');
    }
    /**
     * Check if string is palindrome
     */
    static isPalindrome(str) {
        const cleaned = str.toLowerCase().replace(/[^a-z0-9]/g, '');
        return cleaned === this.reverse(cleaned);
    }
    /**
     * Calculate Levenshtein distance between two strings
     */
    static levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++) {
            matrix[0][i] = i;
        }
        for (let j = 0; j <= str2.length; j++) {
            matrix[j][0] = j;
        }
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, // deletion
                matrix[j - 1][i] + 1, // insertion
                matrix[j - 1][i - 1] + indicator // substitution
                );
            }
        }
        return matrix[str2.length][str1.length];
    }
    /**
     * Calculate string similarity (0-1)
     */
    static similarity(str1, str2) {
        const maxLength = Math.max(str1.length, str2.length);
        if (maxLength === 0)
            return 1;
        const distance = this.levenshteinDistance(str1, str2);
        return (maxLength - distance) / maxLength;
    }
    /**
     * Find best match from array of strings
     */
    static findBestMatch(target, candidates) {
        if (candidates.length === 0)
            return null;
        let bestMatch = candidates[0];
        let bestScore = this.similarity(target, bestMatch);
        for (let i = 1; i < candidates.length; i++) {
            const score = this.similarity(target, candidates[i]);
            if (score > bestScore) {
                bestScore = score;
                bestMatch = candidates[i];
            }
        }
        return { match: bestMatch, score: bestScore };
    }
    /**
     * Wrap text to specified width
     */
    static wordWrap(str, width) {
        const words = str.split(' ');
        const lines = [];
        let currentLine = '';
        for (const word of words) {
            if (currentLine.length + word.length + 1 <= width) {
                currentLine += (currentLine ? ' ' : '') + word;
            }
            else {
                if (currentLine) {
                    lines.push(currentLine);
                }
                currentLine = word;
            }
        }
        if (currentLine) {
            lines.push(currentLine);
        }
        return lines.join('\n');
    }
    /**
     * Pad string to specified length
     */
    static pad(str, length, char = ' ', direction = 'right') {
        if (str.length >= length)
            return str;
        const padLength = length - str.length;
        switch (direction) {
            case 'left':
                return char.repeat(padLength) + str;
            case 'right':
                return str + char.repeat(padLength);
            case 'both':
                const leftPad = Math.floor(padLength / 2);
                const rightPad = padLength - leftPad;
                return char.repeat(leftPad) + str + char.repeat(rightPad);
            default:
                return str + char.repeat(padLength);
        }
    }
    /**
     * Extract text between delimiters
     */
    static extractBetween(str, start, end) {
        const results = [];
        let searchStart = 0;
        while (true) {
            const startIndex = str.indexOf(start, searchStart);
            if (startIndex === -1)
                break;
            const endIndex = str.indexOf(end, startIndex + start.length);
            if (endIndex === -1)
                break;
            results.push(str.slice(startIndex + start.length, endIndex));
            searchStart = endIndex + end.length;
        }
        return results;
    }
    /**
     * Format template string with variables
     */
    static template(template, variables) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return variables.hasOwnProperty(key) ? String(variables[key]) : match;
        });
    }
    /**
     * Convert string to title case
     */
    static toTitleCase(str) {
        return str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
    }
    /**
     * Remove diacritics/accents from string
     */
    static removeDiacritics(str) {
        return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    }
    /**
     * Slugify string for URLs
     */
    static slugify(str) {
        return this.removeDiacritics(str)
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
}
exports.StringUtils = StringUtils;
//# sourceMappingURL=StringUtils.js.map