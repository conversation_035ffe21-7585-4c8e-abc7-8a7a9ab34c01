{"version": 3, "file": "ErrorLogger.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorLogger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AAEH,oBAAY,QAAQ;IAClB,KAAK,IAAI;IACT,IAAI,IAAI;IACR,IAAI,IAAI;IACR,KAAK,IAAI;IACT,KAAK,IAAI;CACV;AAED,MAAM,WAAW,QAAQ;IACvB,SAAS,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,QAAQ,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,QAAQ,CAAC;IAChB,mBAAmB,EAAE,OAAO,CAAC;IAC7B,gBAAgB,EAAE,OAAO,CAAC;IAC1B,uBAAuB,EAAE,OAAO,CAAC;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,gBAAgB,EAAE,OAAO,CAAC;IAC1B,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,UAAU,CAAkB;IACpC,OAAO,CAAC,WAAW,CAAwC;gBAE/C,MAAM,GAAE,OAAO,CAAC,YAAY,CAAM;IAkB9C;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAIjF;;OAEG;IACH,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAIhF;;OAEG;IACH,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAIhF;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAIhG;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAIhG;;OAEG;IACH,eAAe,CACb,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,WAAW,EAAE,MAAM,EACnB,KAAK,EAAE,MAAM,EACb,KAAK,EAAE,KAAK,EACZ,WAAW,CAAC,EAAE,MAAM,GACnB,IAAI;IAgBP;;OAEG;IACH,4BAA4B,CAC1B,WAAW,EAAE,MAAM,EACnB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,MAAM,CAAC,EAAE,MAAM,EACf,WAAW,CAAC,EAAE,MAAM,GACnB,IAAI;IAcP;;OAEG;IACH,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI;IAYjG;;OAEG;IACH,oBAAoB,CAClB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,MAAM,EAChB,MAAM,CAAC,EAAE,GAAG,EACZ,WAAW,CAAC,EAAE,MAAM,GACnB,IAAI;IAcP;;OAEG;IACH,qBAAqB,CACnB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE;QACP,QAAQ,EAAE,MAAM,CAAC;QACjB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,EACD,WAAW,CAAC,EAAE,MAAM,GACnB,IAAI;IAYP;;OAEG;IACH,OAAO,CAAC,GAAG;IAyCX;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAuCzB;;OAEG;IACH,OAAO,CAAC,eAAe;IAavB;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAIvD;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAO1D;;OAEG;IACH,aAAa,CAAC,KAAK,GAAE,MAAY,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,EAAE;IAUhE;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE;QACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,KAAK,CAAC,EAAE,QAAQ,CAAC;QACjB,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B,GAAG,QAAQ,EAAE;IAad;;OAEG;IACH,UAAU,CAAC,QAAQ,CAAC,EAAE;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,KAAK,CAAC,EAAE,QAAQ,CAAC;QACjB,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;KAChB,GAAG,MAAM;IAKV;;OAEG;IACH,SAAS,IAAI,IAAI;IAIjB;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;IAIjD;;OAEG;IACH,SAAS,IAAI,YAAY;IAIzB;;OAEG;IACH,gBAAgB,IAAI;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpC,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvC,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,SAAS,CAAC,EAAE,IAAI,CAAC;KAClB;CAkBF"}