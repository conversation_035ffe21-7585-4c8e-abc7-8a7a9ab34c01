import { ChatMessage, SystemEvent } from '../types';
import { ConfigManager } from '../config/ConfigManager';
export interface Conversation {
    id: string;
    title: string;
    provider: string;
    model: string;
    messages: ChatMessage[];
    createdAt: Date;
    updatedAt: Date;
    metadata?: Record<string, any>;
}
export interface ConversationSummary {
    id: string;
    title: string;
    provider: string;
    model: string;
    messageCount: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class ConversationManager {
    private configManager;
    private conversationsDir;
    private currentConversation;
    private eventListeners;
    constructor(configManager: ConfigManager);
    private ensureConversationsDir;
    createConversation(provider: string, model: string, title?: string): Promise<Conversation>;
    loadConversation(conversationId: string): Promise<Conversation | null>;
    saveConversation(conversation: Conversation): Promise<void>;
    deleteConversation(conversationId: string): Promise<boolean>;
    addMessage(message: ChatMessage): Promise<void>;
    updateMessage(messageId: string, updates: Partial<ChatMessage>): Promise<boolean>;
    getConversationHistory(limit?: number): Promise<ConversationSummary[]>;
    getCurrentConversation(): Conversation | null;
    listConversations(): Promise<ConversationSummary[]>;
    getConversation(conversationId: string): Promise<Conversation | null>;
    updateConversation(conversationId: string, updates: Partial<Conversation>): Promise<boolean>;
    searchConversations(query: string): Promise<ConversationSummary[]>;
    exportConversation(conversationId: string): Promise<string | null>;
    importConversation(conversationData: string): Promise<Conversation | null>;
    cleanupOldConversations(maxAge?: number): Promise<number>;
    private generateTitle;
    addEventListener(eventType: string, listener: (event: SystemEvent) => void): void;
    removeEventListener(eventType: string, listener: (event: SystemEvent) => void): void;
    private emitEvent;
}
//# sourceMappingURL=ConversationManager.d.ts.map