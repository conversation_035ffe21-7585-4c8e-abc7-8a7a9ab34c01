"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUtils = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
class FileUtils {
    static MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    static SAFE_EXTENSIONS = new Set([
        '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.csv',
        '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp',
        '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift',
        '.html', '.css', '.scss', '.sass', '.less', '.vue', '.svelte',
        '.sql', '.sh', '.bat', '.ps1', '.dockerfile', '.gitignore',
        '.env', '.ini', '.conf', '.config', '.toml', '.lock'
    ]);
    static async exists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    static async getFileInfo(filePath) {
        const stats = await fs.stat(filePath);
        const parsedPath = path.parse(filePath);
        return {
            path: filePath,
            name: parsedPath.base,
            size: stats.size,
            isDirectory: stats.isDirectory(),
            isFile: stats.isFile(),
            extension: parsedPath.ext,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime,
            permissions: this.getPermissionsString(stats.mode),
        };
    }
    static async listDirectory(dirPath, options = {}) {
        const { recursive = false, includeHidden = false, sortBy = 'name', sortOrder = 'asc' } = options;
        const files = [];
        const directories = [];
        const entries = await fs.readdir(dirPath);
        for (const entry of entries) {
            if (!includeHidden && entry.startsWith('.')) {
                continue;
            }
            const fullPath = path.join(dirPath, entry);
            const fileInfo = await this.getFileInfo(fullPath);
            if (fileInfo.isDirectory) {
                directories.push(fileInfo);
                if (recursive) {
                    const subListing = await this.listDirectory(fullPath, options);
                    files.push(...subListing.files);
                    directories.push(...subListing.directories);
                }
            }
            else {
                files.push(fileInfo);
            }
        }
        // Sort files and directories
        const sortFn = this.getSortFunction(sortBy, sortOrder);
        files.sort(sortFn);
        directories.sort(sortFn);
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        return {
            path: dirPath,
            files,
            directories,
            totalSize,
            totalFiles: files.length,
            totalDirectories: directories.length,
        };
    }
    static async readFile(filePath, options = {}) {
        const { encoding = 'utf8', maxSize = this.MAX_FILE_SIZE, startLine, endLine } = options;
        // Check file size
        const stats = await fs.stat(filePath);
        if (stats.size > maxSize) {
            throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize})`);
        }
        // Check if file extension is safe
        const ext = path.extname(filePath).toLowerCase();
        if (!this.SAFE_EXTENSIONS.has(ext)) {
            throw new Error(`Unsafe file extension: ${ext}`);
        }
        let content = await fs.readFile(filePath, encoding);
        // Handle line range if specified
        if (startLine !== undefined || endLine !== undefined) {
            const lines = content.split('\n');
            const start = Math.max(0, (startLine || 1) - 1);
            const end = endLine ? Math.min(lines.length, endLine) : lines.length;
            content = lines.slice(start, end).join('\n');
        }
        return content;
    }
    static async writeFile(filePath, content, options = {}) {
        const { encoding = 'utf8', createBackup = false, ensureDir = true, mode } = options;
        // Ensure directory exists
        if (ensureDir) {
            await fs.ensureDir(path.dirname(filePath));
        }
        // Create backup if requested
        if (createBackup && await this.exists(filePath)) {
            const backupPath = `${filePath}.backup.${Date.now()}`;
            await fs.copy(filePath, backupPath);
        }
        // Write file
        await fs.writeFile(filePath, content, { encoding, mode });
    }
    static async appendFile(filePath, content, options = {}) {
        const { encoding = 'utf8', ensureDir = true } = options;
        if (ensureDir) {
            await fs.ensureDir(path.dirname(filePath));
        }
        await fs.appendFile(filePath, content, encoding);
    }
    static async copyFile(sourcePath, destPath, options = {}) {
        const { overwrite = false, preserveTimestamps = true, ensureDir = true } = options;
        if (!overwrite && await this.exists(destPath)) {
            throw new Error(`Destination file already exists: ${destPath}`);
        }
        if (ensureDir) {
            await fs.ensureDir(path.dirname(destPath));
        }
        await fs.copy(sourcePath, destPath, {
            overwrite,
            preserveTimestamps,
        });
    }
    static async moveFile(sourcePath, destPath, options = {}) {
        const { overwrite = false, ensureDir = true } = options;
        if (!overwrite && await this.exists(destPath)) {
            throw new Error(`Destination file already exists: ${destPath}`);
        }
        if (ensureDir) {
            await fs.ensureDir(path.dirname(destPath));
        }
        await fs.move(sourcePath, destPath, { overwrite });
    }
    static async deleteFile(filePath) {
        await fs.remove(filePath);
    }
    static async createDirectory(dirPath, options = {}) {
        const { recursive = true, mode } = options;
        if (recursive) {
            await fs.ensureDir(dirPath);
            if (mode) {
                await fs.chmod(dirPath, mode);
            }
        }
        else {
            await fs.mkdir(dirPath, { mode });
        }
    }
    static async getFileHash(filePath, algorithm = 'sha256') {
        const hash = crypto.createHash(algorithm);
        const stream = fs.createReadStream(filePath);
        return new Promise((resolve, reject) => {
            stream.on('data', (data) => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', reject);
        });
    }
    static async findFiles(searchPath, pattern, options = {}) {
        const { recursive = true, includeDirectories = false, maxDepth = Infinity } = options;
        const results = [];
        const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
        const search = async (currentPath, depth = 0) => {
            if (depth > maxDepth)
                return;
            const entries = await fs.readdir(currentPath);
            for (const entry of entries) {
                const fullPath = path.join(currentPath, entry);
                const stats = await fs.stat(fullPath);
                if (stats.isDirectory()) {
                    if (includeDirectories && regex.test(entry)) {
                        results.push(fullPath);
                    }
                    if (recursive) {
                        await search(fullPath, depth + 1);
                    }
                }
                else if (regex.test(entry)) {
                    results.push(fullPath);
                }
            }
        };
        await search(searchPath);
        return results;
    }
    static formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
    }
    static isTextFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.SAFE_EXTENSIONS.has(ext);
    }
    static sanitizePath(inputPath) {
        // Remove dangerous path components
        return path.normalize(inputPath)
            .replace(/\.\./g, '')
            .replace(/[<>:"|?*]/g, '');
    }
    static getRelativePath(from, to) {
        return path.relative(from, to);
    }
    static joinPath(...segments) {
        return path.join(...segments);
    }
    static resolvePath(...segments) {
        return path.resolve(...segments);
    }
    static getPermissionsString(mode) {
        const permissions = [];
        // Owner permissions
        permissions.push((mode & 0o400) ? 'r' : '-');
        permissions.push((mode & 0o200) ? 'w' : '-');
        permissions.push((mode & 0o100) ? 'x' : '-');
        // Group permissions
        permissions.push((mode & 0o040) ? 'r' : '-');
        permissions.push((mode & 0o020) ? 'w' : '-');
        permissions.push((mode & 0o010) ? 'x' : '-');
        // Other permissions
        permissions.push((mode & 0o004) ? 'r' : '-');
        permissions.push((mode & 0o002) ? 'w' : '-');
        permissions.push((mode & 0o001) ? 'x' : '-');
        return permissions.join('');
    }
    static getSortFunction(sortBy, sortOrder) {
        const multiplier = sortOrder === 'asc' ? 1 : -1;
        switch (sortBy) {
            case 'name':
                return (a, b) => a.name.localeCompare(b.name) * multiplier;
            case 'size':
                return (a, b) => (a.size - b.size) * multiplier;
            case 'modified':
                return (a, b) => (a.modifiedAt.getTime() - b.modifiedAt.getTime()) * multiplier;
            default:
                return (a, b) => a.name.localeCompare(b.name) * multiplier;
        }
    }
}
exports.FileUtils = FileUtils;
//# sourceMappingURL=FileUtils.js.map