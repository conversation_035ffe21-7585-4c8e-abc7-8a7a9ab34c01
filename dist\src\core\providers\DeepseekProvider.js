"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepseekProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const BaseProvider_1 = require("./BaseProvider");
class DeepseekProvider extends BaseProvider_1.BaseProvider {
    name = 'deepseek';
    client;
    MAX_CONTEXT_TOKENS = 60000; // Leave some buffer below the 65,536 limit
    COMPLETION_TOKENS_BUFFER = 4000; // Reserve tokens for completion
    constructor(config) {
        super(config);
        this.validateConfig();
        this.client = axios_1.default.create({
            baseURL: this.config.baseUrl || 'https://api.deepseek.com/v1',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json',
            },
            timeout: 60000, // 60 seconds
        });
    }
    async sendMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const truncatedMessages = this.truncateMessagesForContext(messages, tools);
            const formattedMessages = this.formatMessages(truncatedMessages);
            const formattedTools = this.formatTools(tools);
            const requestData = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                stream: false,
            };
            if (this.config.systemPrompt) {
                requestData.messages.unshift({
                    role: 'system',
                    content: this.config.systemPrompt,
                });
            }
            if (formattedTools.length > 0) {
                requestData.tools = formattedTools;
                requestData.tool_choice = 'auto';
            }
            const response = await this.client.post('/chat/completions', requestData);
            return this.processResponse(response.data);
        }
        catch (error) {
            throw this.handleError(error, 'sendMessage');
        }
    }
    async *streamMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const truncatedMessages = this.truncateMessagesForContext(messages, tools);
            const formattedMessages = this.formatMessages(truncatedMessages);
            const formattedTools = this.formatTools(tools);
            const requestData = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                stream: true,
            };
            if (this.config.systemPrompt) {
                requestData.messages.unshift({
                    role: 'system',
                    content: this.config.systemPrompt,
                });
            }
            if (formattedTools.length > 0) {
                requestData.tools = formattedTools;
                requestData.tool_choice = 'auto';
            }
            const response = await this.client.post('/chat/completions', requestData, {
                responseType: 'stream',
            });
            let fullContent = '';
            let toolCalls = [];
            let usage = null;
            const stream = response.data;
            for await (const chunk of this.parseSSEStream(stream)) {
                if (chunk.choices && chunk.choices[0]) {
                    const delta = chunk.choices[0].delta;
                    if (delta.content) {
                        fullContent += delta.content;
                        yield delta.content;
                    }
                    if (delta.tool_calls) {
                        toolCalls.push(...delta.tool_calls);
                    }
                }
                if (chunk.usage) {
                    usage = chunk.usage;
                }
            }
            // Debug: Log tool call processing for streaming
            if (toolCalls.length > 0) {
                console.log(`DeepseekProvider (stream): Found ${toolCalls.length} tool calls in response`);
                console.log('DeepseekProvider (stream): Tool calls:', toolCalls.map(tc => tc.name));
            }
            const formattedToolCalls = toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined;
            return this.createChatMessage(fullContent, 'assistant', {
                provider: this.name,
                model: requestOptions.model,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens,
                } : undefined,
                toolCalls: formattedToolCalls, // Put toolCalls in metadata at top level for consistency
            });
        }
        catch (error) {
            throw this.handleError(error, 'streamMessage');
        }
    }
    async *parseSSEStream(stream) {
        let buffer = '';
        for await (const chunk of stream) {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') {
                        return;
                    }
                    try {
                        const parsed = JSON.parse(data);
                        yield parsed;
                    }
                    catch (error) {
                        // Skip invalid JSON
                        continue;
                    }
                }
            }
        }
    }
    processResponse(response) {
        const choice = response.choices[0];
        const message = choice.message;
        const content = message.content || '';
        const toolCalls = message.tool_calls ? this.formatToolCalls(message.tool_calls) : undefined;
        // Debug: Log tool call processing
        if (toolCalls && toolCalls.length > 0) {
            console.log(`DeepseekProvider: Found ${toolCalls.length} tool calls in response`);
            console.log('DeepseekProvider: Tool calls:', toolCalls.map(tc => tc.name));
        }
        return this.createChatMessage(content, 'assistant', {
            provider: this.name,
            model: response.model,
            usage: response.usage ? {
                promptTokens: response.usage.prompt_tokens,
                completionTokens: response.usage.completion_tokens,
                totalTokens: response.usage.total_tokens,
            } : undefined,
            toolCalls, // Put toolCalls in metadata at top level for consistency
            finishReason: choice.finish_reason,
        });
    }
    formatToolCalls(toolCalls) {
        return toolCalls.map(call => ({
            id: call.id,
            name: call.function.name,
            parameters: JSON.parse(call.function.arguments || '{}'),
        }));
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content,
            };
            // Handle tool calls in assistant messages
            if (msg.role === 'assistant' && msg.toolCalls) {
                formatted.tool_calls = msg.toolCalls.map((call) => ({
                    id: call.id,
                    type: 'function',
                    function: {
                        name: call.name,
                        arguments: JSON.stringify(call.parameters),
                    },
                }));
            }
            // Handle tool responses
            if (msg.role === 'tool') {
                formatted.tool_call_id = msg.metadata?.toolCallId;
            }
            return formatted;
        });
    }
    async testConnection() {
        try {
            const response = await this.client.get('/models');
            return response.data && response.data.data && response.data.data.length > 0;
        }
        catch (error) {
            console.error('Deepseek connection test failed:', error);
            return false;
        }
    }
    async getAvailableModels() {
        try {
            const response = await this.client.get('/models');
            return response.data.data
                .filter((model) => model.id.includes('deepseek'))
                .map((model) => model.id)
                .sort();
        }
        catch (error) {
            console.error('Failed to fetch Deepseek models:', error);
            return ['deepseek-chat', 'deepseek-coder']; // Fallback to known models
        }
    }
    validateConfig() {
        super.validateConfig();
        if (!this.config.baseUrl) {
            this.config.baseUrl = 'https://api.deepseek.com/v1';
        }
    }
    handleError(error, context) {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            switch (status) {
                case 401:
                    return new Error('Deepseek API authentication failed. Check your API key.');
                case 403:
                    return new Error('Deepseek API access forbidden. Check your API permissions.');
                case 429:
                    return new Error('Deepseek API rate limit exceeded. Please try again later.');
                case 500:
                    return new Error('Deepseek API server error. Please try again later.');
                default:
                    const message = data?.error?.message || error.message;
                    return new Error(`Deepseek API error (${status}): ${message}`);
            }
        }
        return super.handleError(error, context);
    }
    /**
     * Estimates token count for a message (rough approximation)
     * This is a simple heuristic since we don't have access to the exact tokenizer
     */
    estimateTokenCount(message) {
        const content = message.content || '';
        // Rough estimation: ~4 characters per token for English text
        // Add extra tokens for role, metadata, and JSON structure
        const contentTokens = Math.ceil(content.length / 4);
        const structureTokens = 10; // For role, timestamps, etc.
        // Add tokens for tool calls if present
        let toolCallTokens = 0;
        if (message.toolCalls) {
            toolCallTokens = message.toolCalls.reduce((total, call) => {
                const nameTokens = Math.ceil(call.name.length / 4);
                const paramsTokens = Math.ceil(JSON.stringify(call.parameters).length / 4);
                return total + nameTokens + paramsTokens + 20; // 20 for structure
            }, 0);
        }
        return contentTokens + structureTokens + toolCallTokens;
    }
    /**
     * Estimates token count for tools
     */
    estimateToolsTokenCount(tools) {
        if (!tools || tools.length === 0)
            return 0;
        return tools.reduce((total, tool) => {
            const nameTokens = Math.ceil(tool.name.length / 4);
            const descTokens = Math.ceil(tool.description.length / 4);
            const paramsTokens = Math.ceil(JSON.stringify(tool.parameters).length / 4);
            return total + nameTokens + descTokens + paramsTokens + 30; // 30 for structure
        }, 0);
    }
    /**
     * Truncates messages to fit within the context window
     * Keeps system prompt, most recent messages, and ensures we don't exceed token limits
     */
    truncateMessagesForContext(messages, tools) {
        if (messages.length === 0)
            return messages;
        const systemPromptTokens = this.config.systemPrompt ?
            Math.ceil(this.config.systemPrompt.length / 4) + 10 : 0;
        const toolsTokens = this.estimateToolsTokenCount(tools);
        const availableTokens = this.MAX_CONTEXT_TOKENS - this.COMPLETION_TOKENS_BUFFER -
            systemPromptTokens - toolsTokens;
        // If we have very few tokens available, keep only the last message
        if (availableTokens < 1000) {
            console.warn('DeepseekProvider: Very low token budget, keeping only last message');
            return [messages[messages.length - 1]];
        }
        // Calculate tokens for all messages
        const messageTokens = messages.map(msg => ({
            message: msg,
            tokens: this.estimateTokenCount(msg)
        }));
        const totalTokens = messageTokens.reduce((sum, item) => sum + item.tokens, 0);
        // If we're within limits, return all messages
        if (totalTokens <= availableTokens) {
            return messages;
        }
        console.log(`DeepseekProvider: Truncating conversation. Total tokens: ${totalTokens}, Available: ${availableTokens}`);
        // Keep the most recent messages that fit within the token limit
        const truncatedMessages = [];
        let currentTokens = 0;
        // Start from the end and work backwards
        for (let i = messageTokens.length - 1; i >= 0; i--) {
            const item = messageTokens[i];
            if (currentTokens + item.tokens <= availableTokens) {
                truncatedMessages.unshift(item.message);
                currentTokens += item.tokens;
            }
            else {
                // If this is the first message and it doesn't fit, truncate its content
                if (truncatedMessages.length === 0) {
                    const availableForContent = availableTokens - 50; // Reserve tokens for structure
                    const maxContentLength = availableForContent * 4; // Rough conversion back to characters
                    const truncatedContent = item.message.content.substring(0, maxContentLength) +
                        (item.message.content.length > maxContentLength ? '...[truncated]' : '');
                    truncatedMessages.unshift({
                        ...item.message,
                        content: truncatedContent
                    });
                }
                break;
            }
        }
        console.log(`DeepseekProvider: Kept ${truncatedMessages.length} of ${messages.length} messages`);
        return truncatedMessages;
    }
}
exports.DeepseekProvider = DeepseekProvider;
//# sourceMappingURL=DeepseekProvider.js.map