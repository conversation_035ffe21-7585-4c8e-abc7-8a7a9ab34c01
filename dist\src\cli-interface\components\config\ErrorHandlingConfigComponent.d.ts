import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
import { ErrorHandlingMiddleware } from '../../../core/error-handling/ErrorHandlingMiddleware';
export declare class ErrorHandlingConfigComponent extends BaseComponent {
    private configManager;
    private errorHandler;
    private retryPolicyMenu;
    private circuitBreakerMenu;
    private metricsViewer;
    private presetManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager, errorHandler: ErrorHandlingMiddleware);
    render(): Promise<void>;
    private showMainMenu;
    private configureGlobalSettings;
    private configureAdvancedSettings;
    private configureOperationPolicies;
    private configureErrorClassification;
    private configureLogging;
    private configureMetrics;
    private resetToDefaults;
}
//# sourceMappingURL=ErrorHandlingConfigComponent.d.ts.map