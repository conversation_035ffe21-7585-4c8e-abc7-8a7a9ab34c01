"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputHandler = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
class InputHandler extends BaseComponent_1.BaseComponent {
    inputHandlers = [];
    constructor(state, config) {
        super(state, config);
        this.setupDefaultHandlers();
    }
    async render() {
        // This component is used inline, no standalone render needed
    }
    async processInput(input) {
        // This would typically be handled by the parent component
        // For now, just log the input
        console.log('Processing input:', input);
    }
    async getInput() {
        const prompt = this.createPrompt();
        const { input } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'input',
                message: prompt,
                validate: this.validateInput.bind(this),
            },
        ]);
        return input.trim();
    }
    async getMultilineInput() {
        console.log(this.utils.colorize('Enter your message (type "END" on a new line to finish):', this.config.theme.info));
        const lines = [];
        while (true) {
            const { line } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'line',
                    message: this.utils.colorize('│', this.config.theme.muted),
                },
            ]);
            if (line.trim() === 'END') {
                break;
            }
            lines.push(line);
        }
        return lines.join('\n').trim();
    }
    async getConfirmation(message, defaultValue = false) {
        return await this.utils.confirmAction(message, defaultValue);
    }
    async selectFromOptions(message, options, defaultValue) {
        return await this.utils.selectFromList(message, options, defaultValue);
    }
    addInputHandler(handler) {
        this.inputHandlers.push(handler);
    }
    removeInputHandler(pattern) {
        this.inputHandlers = this.inputHandlers.filter(h => h.pattern.source !== pattern.source);
    }
    setupDefaultHandlers() {
        // Handler for multiline input trigger
        this.addInputHandler({
            pattern: /^\/multiline$/i,
            handler: async () => {
                const input = await this.getMultilineInput();
                // Process the multiline input
                if (input.trim()) {
                    await this.processInput(input);
                }
            },
            description: 'Enter multiline input mode',
        });
        // Handler for file input
        this.addInputHandler({
            pattern: /^\/file\s+(.+)$/i,
            handler: async (input, matches) => {
                const filePath = matches[1];
                const content = await this.handleFileInput(filePath);
                if (content) {
                    await this.processInput(content);
                }
            },
            description: 'Read content from a file',
        });
        // Handler for clipboard paste (if available)
        this.addInputHandler({
            pattern: /^\/paste$/i,
            handler: async () => {
                const content = await this.handleClipboardPaste();
                if (content) {
                    await this.processInput(content);
                }
            },
            description: 'Paste from clipboard',
        });
    }
    createPrompt() {
        const promptSymbol = this.utils.colorize('❯', this.config.theme.primary);
        return promptSymbol;
    }
    validateInput(input) {
        if (!input || input.trim().length === 0) {
            return 'Please enter a message or command';
        }
        // Check for special input handlers
        for (const handler of this.inputHandlers) {
            if (handler.pattern.test(input)) {
                return true; // Will be handled by the handler
            }
        }
        return true;
    }
    async handleFileInput(filePath) {
        try {
            const fs = await Promise.resolve().then(() => __importStar(require('fs-extra')));
            const path = await Promise.resolve().then(() => __importStar(require('path')));
            const resolvedPath = path.resolve(filePath);
            if (!await fs.pathExists(resolvedPath)) {
                throw new Error(`File not found: ${filePath}`);
            }
            const content = await fs.readFile(resolvedPath, 'utf-8');
            this.utils.showSuccess(`Loaded content from ${filePath} (${this.utils.formatFileSize(content.length)} characters)`);
            return content;
        }
        catch (error) {
            this.utils.showError(`Failed to read file: ${error}`);
            return '';
        }
    }
    async handleClipboardPaste() {
        try {
            // This would require a clipboard library like 'clipboardy'
            // For now, just show a message
            this.utils.showInfo('Clipboard paste not implemented yet. Please type your content manually.');
            return await this.getMultilineInput();
        }
        catch (error) {
            this.utils.showError(`Failed to access clipboard: ${error}`);
            return '';
        }
    }
    async handleSpecialInput(input) {
        for (const handler of this.inputHandlers) {
            const matches = input.match(handler.pattern);
            if (matches) {
                try {
                    await handler.handler(input, matches);
                    return null; // Handler processed the input
                }
                catch (error) {
                    this.utils.showError(`Handler error: ${error}`);
                    return null;
                }
            }
        }
        return null;
    }
    showInputHelp() {
        console.log(this.utils.formatHeader('📝 Input Help'));
        console.log();
        console.log('Special input commands:');
        for (const handler of this.inputHandlers) {
            console.log(`  ${this.utils.colorize(handler.pattern.source, this.config.theme.accent)} - ${handler.description}`);
        }
        console.log();
        console.log('General commands:');
        console.log(`  ${this.utils.colorize('/help', this.config.theme.accent)} - Show available commands`);
        console.log(`  ${this.utils.colorize('/multiline', this.config.theme.accent)} - Enter multiline input mode`);
        console.log(`  ${this.utils.colorize('/file <path>', this.config.theme.accent)} - Load content from file`);
        console.log(`  ${this.utils.colorize('/paste', this.config.theme.accent)} - Paste from clipboard`);
        console.log();
    }
}
exports.InputHandler = InputHandler;
//# sourceMappingURL=InputHandler.js.map