{"version": 3, "file": "ToolRegistry.js", "sourceRoot": "", "sources": ["../../../../../src/core/tools/base/ToolRegistry.ts"], "names": [], "mappings": ";;;AAyBA,MAAa,YAAY;IACf,KAAK,GAA0B,IAAI,GAAG,EAAE,CAAC;IACzC,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;IACtC,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;IACvC,oBAAoB,GAA0B,EAAE,CAAC;IACjD,cAAc,GAAW,IAAI,CAAC;IAE/B,YAAY,CAAC,IAAc;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,IAAI,yBAAyB,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAEM,cAAc,CAAC,QAAgB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,OAAO,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IAC1C,CAAC;IAEM,WAAW;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CACvE,CAAC;IACJ,CAAC;IAEM,kBAAkB,CAAC,QAAgB;QACxC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,CAAC;IAC5E,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC;IAEM,WAAW,CAAC,MAAkB;QACnC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC1E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC9E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,IAAI,IAAI,CAAC,oBAAoB,KAAK,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC3G,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,IAAI,SAAS,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,QAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,WAAW,CAAC,QAAgB;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAEM,gBAAgB,CAAC,QAAgB;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAChC,QAAgB,EAChB,UAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,CAAC,SAAS,QAAQ,qBAAqB,CAAC;gBAChD,QAAQ,EAAE,EAAE;aACb,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,CAAC,SAAS,QAAQ,eAAe,CAAC;gBAC1C,QAAQ,EAAE,EAAE;aACb,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,WAAW,CACtB,QAAgB,EAChB,UAA+B,EAC/B,UAAgC,EAAE;QAElC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1E,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,MAAM,GAAwB;oBAClC,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,EAAE;oBACX,KAAK,EAAE,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC3D,QAAQ;oBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBACrC,OAAO;iBACR,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC1B,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAE,CAAC;YAErC,mBAAmB;YACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAElD,MAAM,MAAM,GAAwB;gBAClC,GAAG,UAAU;gBACb,QAAQ;gBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,OAAO;aACR,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC1B,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAwB;gBAClC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACpF,QAAQ;gBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACrC,OAAO;aACR,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CACjC,QAAgB,EAChB,UAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,IAAI,CAAC,CAAC,wBAAwB;QACvC,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB,CAAC,KAAc;QACvC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC/C,OAAO,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IACjD,CAAC;IAEM,qBAAqB;QAC1B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IACjC,CAAC;IAEM,iBAAiB;QAOtB,MAAM,KAAK,GAAwB,EAAE,CAAC;QAEtC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;oBACjB,eAAe,EAAE,CAAC;oBAClB,oBAAoB,EAAE,CAAC;oBACvB,gBAAgB,EAAE,CAAC;oBACnB,oBAAoB,EAAE,CAAC;iBACxB,CAAC;gBACF,SAAS;YACX,CAAC;YAED,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAC3E,MAAM,aAAa,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAE1D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBACjB,eAAe,EAAE,WAAW,CAAC,MAAM;gBACnC,oBAAoB,EAAE,UAAU,CAAC,MAAM;gBACvC,gBAAgB,EAAE,MAAM,CAAC,MAAM;gBAC/B,oBAAoB,EAAE,SAAS,GAAG,WAAW,CAAC,MAAM;gBACpD,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,aAAa,CAAC;aACjE,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,qBAAqB;QAM1B,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;SAC7B,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC;IACvC,CAAC;IAEO,YAAY,CAAC,MAA2B;QAC9C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvC,8BAA8B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAExC,iCAAiC;QACjC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IACjC,CAAC;CACF;AA5RD,oCA4RC"}