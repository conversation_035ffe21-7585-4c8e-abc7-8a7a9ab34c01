"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryTool = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const BaseTool_1 = require("../base/BaseTool");
class MemoryTool extends BaseTool_1.BaseTool {
    name = 'memory';
    description = 'Stores, retrieves, and manages persistent memory across conversations';
    requiresConfirmation = false;
    memoryFile;
    parameters = {
        type: 'object',
        properties: {
            action: {
                type: 'string',
                enum: ['store', 'retrieve', 'search', 'list', 'delete', 'update'],
                description: 'Action to perform with memory',
            },
            content: {
                type: 'string',
                description: 'Content to store or update (required for store/update actions)',
            },
            id: {
                type: 'string',
                description: 'Memory entry ID (required for retrieve/delete/update actions)',
            },
            query: {
                type: 'string',
                description: 'Search query (required for search action)',
            },
            tags: {
                type: 'array',
                items: { type: 'string' },
                description: 'Tags to associate with memory entry',
            },
            importance: {
                type: 'number',
                description: 'Importance level (1-10, default: 5)',
                minimum: 1,
                maximum: 10,
                default: 5,
            },
            context: {
                type: 'string',
                description: 'Additional context for the memory entry',
            },
            limit: {
                type: 'number',
                description: 'Maximum number of results to return',
                default: 10,
                minimum: 1,
                maximum: 100,
            },
        },
        required: ['action'],
    };
    constructor(memoryDir) {
        super({
            category: 'memory',
            tags: ['memory', 'storage', 'persistence'],
            version: '1.0.0',
            dangerous: false,
            requiresConfirmation: false,
        });
        const defaultMemoryDir = path.join(process.cwd(), '.ai-cli-memory');
        this.memoryFile = path.join(memoryDir || defaultMemoryDir, 'memory.json');
    }
    async validate(params) {
        const errors = [];
        const warnings = [];
        // Validate required parameters
        const requiredValidation = this.validateRequiredParams(params, ['action']);
        if (!requiredValidation.valid) {
            return requiredValidation;
        }
        const action = params.action;
        const validActions = ['store', 'retrieve', 'search', 'list', 'delete', 'update'];
        if (!validActions.includes(action)) {
            errors.push(`Action must be one of: ${validActions.join(', ')}`);
        }
        // Validate action-specific requirements
        switch (action) {
            case 'store':
                if (!params.content) {
                    errors.push('Content is required for store action');
                }
                break;
            case 'retrieve':
            case 'delete':
                if (!params.id) {
                    errors.push(`ID is required for ${action} action`);
                }
                break;
            case 'update':
                if (!params.id) {
                    errors.push('ID is required for update action');
                }
                if (!params.content) {
                    errors.push('Content is required for update action');
                }
                break;
            case 'search':
                if (!params.query) {
                    errors.push('Query is required for search action');
                }
                break;
        }
        // Validate optional parameters
        if (params.importance !== undefined) {
            errors.push(...this.validateNumberParam(params.importance, 'importance', {
                min: 1,
                max: 10,
                integer: true,
            }));
        }
        if (params.limit !== undefined) {
            errors.push(...this.validateNumberParam(params.limit, 'limit', {
                min: 1,
                max: 100,
                integer: true,
            }));
        }
        if (params.tags !== undefined) {
            errors.push(...this.validateArrayParam(params.tags, 'tags'));
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    async execute(params) {
        try {
            const { action } = params;
            switch (action) {
                case 'store':
                    return await this.storeMemory(params);
                case 'retrieve':
                    return await this.retrieveMemory(params);
                case 'search':
                    return await this.searchMemory(params);
                case 'list':
                    return await this.listMemories(params);
                case 'delete':
                    return await this.deleteMemory(params);
                case 'update':
                    return await this.updateMemory(params);
                default:
                    return this.createErrorResult(`Unknown action: ${action}`);
            }
        }
        catch (error) {
            return this.createErrorResult(`Memory operation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async storeMemory(params) {
        const { content, tags = [], importance = 5, context, } = params;
        const entry = {
            id: this.generateId(),
            content,
            tags,
            importance,
            context,
            timestamp: new Date(),
        };
        const memories = await this.loadMemories();
        memories.push(entry);
        await this.saveMemories(memories);
        return this.createSuccessResult(JSON.stringify(entry, null, 2), `Memory stored with ID: ${entry.id}`, { id: entry.id, action: 'store' });
    }
    async retrieveMemory(params) {
        const { id } = params;
        const memories = await this.loadMemories();
        const entry = memories.find(m => m.id === id);
        if (!entry) {
            return this.createErrorResult(`Memory entry with ID ${id} not found`);
        }
        return this.createSuccessResult(JSON.stringify(entry, null, 2), this.formatMemoryEntry(entry), { id: entry.id, action: 'retrieve' });
    }
    async searchMemory(params) {
        const { query, limit = 10 } = params;
        const startTime = Date.now();
        const memories = await this.loadMemories();
        const searchTerms = query.toLowerCase().split(/\s+/);
        const matches = memories.filter(entry => {
            const searchText = `${entry.content} ${entry.tags.join(' ')} ${entry.context || ''}`.toLowerCase();
            return searchTerms.some(term => searchText.includes(term));
        });
        // Sort by relevance (importance and recency)
        matches.sort((a, b) => {
            const scoreA = a.importance + (Date.now() - a.timestamp.getTime()) / (1000 * 60 * 60 * 24); // Decay over days
            const scoreB = b.importance + (Date.now() - b.timestamp.getTime()) / (1000 * 60 * 60 * 24);
            return scoreB - scoreA;
        });
        const results = matches.slice(0, limit);
        const searchResult = {
            entries: results,
            totalMatches: matches.length,
            searchTime: Date.now() - startTime,
        };
        return this.createSuccessResult(JSON.stringify(searchResult, null, 2), this.formatSearchResults(searchResult, query), {
            query,
            totalMatches: searchResult.totalMatches,
            returnedResults: results.length,
            action: 'search'
        });
    }
    async listMemories(params) {
        const { limit = 10 } = params;
        const memories = await this.loadMemories();
        // Sort by timestamp (most recent first)
        const sorted = memories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        const limited = sorted.slice(0, limit);
        const formatted = this.formatMemoryList(limited, memories.length);
        return this.createSuccessResult(JSON.stringify(limited, null, 2), formatted, {
            totalMemories: memories.length,
            returnedMemories: limited.length,
            action: 'list'
        });
    }
    async deleteMemory(params) {
        const { id } = params;
        const memories = await this.loadMemories();
        const index = memories.findIndex(m => m.id === id);
        if (index === -1) {
            return this.createErrorResult(`Memory entry with ID ${id} not found`);
        }
        const deleted = memories.splice(index, 1)[0];
        await this.saveMemories(memories);
        return this.createSuccessResult(JSON.stringify(deleted, null, 2), `Memory entry ${id} deleted successfully`, { id, action: 'delete' });
    }
    async updateMemory(params) {
        const { id, content, tags, importance, context } = params;
        const memories = await this.loadMemories();
        const entry = memories.find(m => m.id === id);
        if (!entry) {
            return this.createErrorResult(`Memory entry with ID ${id} not found`);
        }
        // Update fields
        if (content !== undefined)
            entry.content = content;
        if (tags !== undefined)
            entry.tags = tags;
        if (importance !== undefined)
            entry.importance = importance;
        if (context !== undefined)
            entry.context = context;
        entry.timestamp = new Date(); // Update timestamp
        await this.saveMemories(memories);
        return this.createSuccessResult(JSON.stringify(entry, null, 2), `Memory entry ${id} updated successfully`, { id, action: 'update' });
    }
    async loadMemories() {
        try {
            await fs.ensureDir(path.dirname(this.memoryFile));
            if (await fs.pathExists(this.memoryFile)) {
                const data = await fs.readJson(this.memoryFile);
                return data.map((entry) => ({
                    ...entry,
                    timestamp: new Date(entry.timestamp),
                }));
            }
            return [];
        }
        catch (error) {
            throw new Error(`Failed to load memories: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async saveMemories(memories) {
        try {
            await fs.ensureDir(path.dirname(this.memoryFile));
            await fs.writeJson(this.memoryFile, memories, { spaces: 2 });
        }
        catch (error) {
            throw new Error(`Failed to save memories: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    formatMemoryEntry(entry) {
        const lines = [];
        lines.push(`Memory Entry: ${entry.id}`);
        lines.push(`Timestamp: ${entry.timestamp.toLocaleString()}`);
        lines.push(`Importance: ${entry.importance}/10`);
        if (entry.tags.length > 0) {
            lines.push(`Tags: ${entry.tags.join(', ')}`);
        }
        if (entry.context) {
            lines.push(`Context: ${entry.context}`);
        }
        lines.push('');
        lines.push('Content:');
        lines.push('─'.repeat(40));
        lines.push(entry.content);
        return lines.join('\n');
    }
    formatSearchResults(result, query) {
        const lines = [];
        lines.push(`Memory Search Results for: "${query}"`);
        lines.push(`Found ${result.totalMatches} matches (showing ${result.entries.length})`);
        lines.push(`Search time: ${result.searchTime}ms`);
        lines.push('');
        for (let i = 0; i < result.entries.length; i++) {
            const entry = result.entries[i];
            lines.push(`${i + 1}. ${entry.id} (Importance: ${entry.importance}/10)`);
            lines.push(`   ${entry.timestamp.toLocaleDateString()}`);
            lines.push(`   ${this.truncateContent(entry.content, 100)}`);
            if (entry.tags.length > 0) {
                lines.push(`   Tags: ${entry.tags.join(', ')}`);
            }
            lines.push('');
        }
        return lines.join('\n');
    }
    formatMemoryList(memories, total) {
        const lines = [];
        lines.push(`Memory List (${memories.length} of ${total} total)`);
        lines.push('');
        for (let i = 0; i < memories.length; i++) {
            const entry = memories[i];
            lines.push(`${i + 1}. ${entry.id}`);
            lines.push(`   ${entry.timestamp.toLocaleDateString()} - Importance: ${entry.importance}/10`);
            lines.push(`   ${this.truncateContent(entry.content, 80)}`);
            if (entry.tags.length > 0) {
                lines.push(`   Tags: ${entry.tags.join(', ')}`);
            }
            lines.push('');
        }
        return lines.join('\n');
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
exports.MemoryTool = MemoryTool;
//# sourceMappingURL=MemoryTool.js.map