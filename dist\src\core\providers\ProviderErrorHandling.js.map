{"version": 3, "file": "ProviderErrorHandling.js", "sourceRoot": "", "sources": ["../../../../src/core/providers/ProviderErrorHandling.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAwJH,wDAOC;AAKD,4DAmCC;AA6HD,kEAYC;AAKD,8DAmBC;AApWD;;GAEG;AACU,QAAA,sBAAsB,GAAwD;IACzF,MAAM,EAAE;QACN,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,cAAc;YAC1B,cAAc,EAAE,KAAK;SACtB;QACD,cAAc,EAAE;YACd,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,MAAM;YACxB,OAAO,EAAE,IAAI;SACd;QACD,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,KAAK;QACxB,gBAAgB,EAAE,KAAK;QACvB,iBAAiB,EAAE;YACjB,aAAa,EAAE;gBACb,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,aAAa;gBAC9B,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,cAAc;aAC3B;SACF;KACF;IAED,SAAS,EAAE;QACT,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI,EAAE,2CAA2C;YAC5D,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,cAAc;YAC1B,cAAc,EAAE,KAAK,CAAC,mCAAmC;SAC1D;QACD,cAAc,EAAE;YACd,gBAAgB,EAAE,CAAC,EAAE,6BAA6B;YAClD,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,MAAM;YACxB,OAAO,EAAE,IAAI;SACd;QACD,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,KAAK;QACxB,gBAAgB,EAAE,KAAK;QACvB,iBAAiB,EAAE;YACjB,aAAa,EAAE;gBACb,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,aAAa;gBAC9B,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,cAAc;aAC3B;SACF;KACF;IAED,MAAM,EAAE;QACN,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,EAAE,yCAAyC;YACzD,SAAS,EAAE,GAAG;YACd,iBAAiB,EAAE,GAAG;YACtB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,MAAM;YAClB,cAAc,EAAE,KAAK;SACtB;QACD,cAAc,EAAE;YACd,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,MAAM;YACxB,OAAO,EAAE,IAAI;SACd;QACD,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,KAAK;QACxB,gBAAgB,EAAE,KAAK;QACvB,iBAAiB,EAAE;YACjB,aAAa,EAAE;gBACb,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,GAAG;gBACd,iBAAiB,EAAE,GAAG;gBACtB,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,aAAa;gBAC9B,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,MAAM;aACnB;SACF;KACF;IAED,QAAQ,EAAE;QACR,KAAK,EAAE;YACL,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,GAAG;YACtB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,cAAc;YAC1B,cAAc,EAAE,KAAK;SACtB;QACD,cAAc,EAAE;YACd,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,MAAM;YACxB,OAAO,EAAE,IAAI;SACd;QACD,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,KAAK;QACxB,gBAAgB,EAAE,KAAK;QACvB,iBAAiB,EAAE;YACjB,aAAa,EAAE;gBACb,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,GAAG;gBACtB,QAAQ,EAAE,KAAK;gBACf,eAAe,EAAE,aAAa;gBAC9B,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,cAAc;aAC3B;SACF;KACF;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,sBAAsB,CAAC,YAAoB;IACzD,MAAM,MAAM,GAAG,8BAAsB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;IAClE,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CAAC,IAAI,CAAC,gEAAgE,YAAY,gCAAgC,CAAC,CAAC;QAC3H,OAAO,8BAAsB,CAAC,MAAM,CAAC,CAAC,wBAAwB;IAChE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CACtC,YAAoB,EACpB,UAAgD;IAEhD,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,YAAY,CAAC,CAAC;IAE9D,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,gBAA8C,CAAC;IACxD,CAAC;IAED,4BAA4B;IAC5B,MAAM,MAAM,GAA+B;QACzC,KAAK,EAAE;YACL,GAAG,gBAAgB,CAAC,KAAK;YACzB,GAAG,UAAU,CAAC,KAAK;SACpB;QACD,cAAc,EAAE;YACd,GAAG,gBAAgB,CAAC,cAAc;YAClC,GAAG,UAAU,CAAC,cAAc;SAC7B;QACD,kBAAkB,EAAE,UAAU,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,kBAAkB,IAAI,IAAI;QAChG,aAAa,EAAE,UAAU,CAAC,aAAa,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI;QACjF,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,IAAI,gBAAgB,CAAC,iBAAiB,IAAI,KAAK;QAC9F,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,IAAI,KAAK;QAC3F,iBAAiB,EAAE;YACjB,GAAG,gBAAgB,CAAC,iBAAiB;YACrC,GAAG,UAAU,CAAC,iBAAiB;YAC/B,aAAa,EAAE;gBACb,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,aAAa;gBACpD,GAAG,UAAU,CAAC,iBAAiB,EAAE,aAAa;aAC/C;SACF;KACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACU,QAAA,uBAAuB,GAA6C;IAC/E,MAAM,EAAE;QACN,eAAe,EAAE;YACf,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,KAAK;SACN;QACD,oBAAoB,EAAE;YACpB,kBAAkB;YAClB,eAAe;YACf,KAAK;YACL,oBAAoB;SACrB;QACD,wBAAwB,EAAE;YACxB,sBAAsB;YACtB,KAAK;YACL,KAAK;YACL,KAAK;YACL,cAAc;YACd,kBAAkB;SACnB;QACD,aAAa,EAAE;YACb,cAAc;YACd,WAAW;YACX,WAAW;YACX,gBAAgB;SACjB;KACF;IAED,SAAS,EAAE;QACT,eAAe,EAAE;YACf,aAAa;YACb,oBAAoB;YACpB,KAAK;YACL,mBAAmB;SACpB;QACD,oBAAoB,EAAE;YACpB,kBAAkB;YAClB,eAAe;YACf,KAAK;YACL,uBAAuB;SACxB;QACD,wBAAwB,EAAE;YACxB,sBAAsB;YACtB,KAAK;YACL,KAAK;YACL,KAAK;YACL,mBAAmB;YACnB,YAAY;SACb;QACD,aAAa,EAAE;YACb,cAAc;YACd,WAAW;YACX,WAAW;YACX,gBAAgB;SACjB;KACF;IAED,MAAM,EAAE;QACN,eAAe,EAAE;YACf,iBAAiB;YACjB,aAAa;YACb,KAAK;YACL,qBAAqB;SACtB;QACD,oBAAoB,EAAE;YACpB,kBAAkB;YAClB,eAAe;YACf,KAAK;YACL,iBAAiB;SAClB;QACD,wBAAwB,EAAE;YACxB,sBAAsB;YACtB,KAAK;YACL,KAAK;YACL,KAAK;YACL,aAAa;YACb,UAAU;SACX;QACD,aAAa,EAAE;YACb,cAAc;YACd,WAAW;YACX,WAAW;YACX,gBAAgB;SACjB;KACF;IAED,QAAQ,EAAE;QACR,eAAe,EAAE;YACf,aAAa;YACb,oBAAoB;YACpB,KAAK;YACL,iBAAiB;SAClB;QACD,oBAAoB,EAAE;YACpB,kBAAkB;YAClB,eAAe;YACf,KAAK;YACL,wBAAwB;SACzB;QACD,wBAAwB,EAAE;YACxB,sBAAsB;YACtB,KAAK;YACL,KAAK;YACL,KAAK;YACL,eAAe;SAChB;QACD,aAAa,EAAE;YACb,cAAc;YACd,WAAW;YACX,WAAW;YACX,gBAAgB;SACjB;KACF;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,2BAA2B,CACzC,YAAoB,EACpB,KAAY,EACZ,WAAsG;IAEtG,MAAM,QAAQ,GAAG,+BAAuB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;IACpF,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IACjD,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CAAC,YAAoB,EAAE,KAAY;IAC1E,+DAA+D;IAC/D,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IAEjD,gDAAgD;IAChD,MAAM,eAAe,GAAG,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACjE,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,0BAA0B;IACxE,CAAC;IAED,mDAAmD;IACnD,MAAM,aAAa,GAA2B;QAC5C,MAAM,EAAE,KAAK,EAAM,WAAW;QAC9B,SAAS,EAAE,KAAK,EAAG,cAAc;QACjC,MAAM,EAAE,KAAK,EAAM,aAAa;QAChC,QAAQ,EAAE,KAAK,CAAI,eAAe;KACnC,CAAC;IAEF,OAAO,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,IAAI,KAAK,CAAC;AAC5D,CAAC"}