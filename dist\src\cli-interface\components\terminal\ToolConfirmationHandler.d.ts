import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig } from '../../types';
export declare class ToolConfirmationHandler extends BaseComponent {
    private trustedTools;
    private deniedTools;
    constructor(state: CLIState, config: CLIConfig);
    render(): Promise<void>;
    confirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
    confirmToolExecution(toolName: string, parameters: Record<string, any>, description?: string): Promise<boolean>;
    private showConfirmationDialog;
    private renderParameters;
    private formatParameterValue;
    private showToolDetails;
    private getToolSafetyInfo;
    getTrustedTools(): string[];
    getDeniedTools(): string[];
    trustTool(toolName: string): void;
    denyTool(toolName: string): void;
    resetTool(toolName: string): void;
    clearAllTrusted(): void;
    clearAllDenied(): void;
    private setupTrustedTools;
    showToolManagement(): Promise<void>;
}
//# sourceMappingURL=ToolConfirmationHandler.d.ts.map