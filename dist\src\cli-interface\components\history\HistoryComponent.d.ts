import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class HistoryComponent extends BaseComponent {
    private configManager;
    private conversationManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private showMainMenu;
    private browseConversations;
    private viewConversation;
    private viewMessages;
    private continueConversation;
    private searchConversations;
    private viewStatistics;
    private exportConversations;
    private selectConversationForExport;
    private deleteConversations;
    private deleteSpecificConversation;
    private deleteAllConversations;
    private exportSingleConversation;
    private renameConversation;
    private deleteSingleConversation;
    private formatConversationSummary;
}
//# sourceMappingURL=HistoryComponent.d.ts.map