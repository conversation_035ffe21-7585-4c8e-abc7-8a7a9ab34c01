import { createErrorHandlingMiddleware } from '../../src/core/error-handling';
import { ErrorHandlingMiddleware } from '../../src/core/error-handling/ErrorHandlingMiddleware';
import { createMockErrorHandlingConfig } from '../utils/mocks';
import { TestError, NetworkError, TimeoutError, RateLimitError } from '../utils/mocks';

describe('Error Handling Pipeline Integration', () => {
  let middleware: ErrorHandlingMiddleware;
  let config: any;

  beforeEach(() => {
    config = createMockErrorHandlingConfig();
    middleware = createErrorHandlingMiddleware(config);
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('complete retry and circuit breaker flow', () => {
    it('should handle a complete success scenario', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await middleware.execute(operation, 'test-operation', 'test-service');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
      
      const metrics = await middleware.getMetrics();
      expect(metrics.totalOperations).toBe(1);
      expect(metrics.successfulOperations).toBe(1);
      expect(metrics.failedOperations).toBe(0);
    });

    it('should handle retry with eventual success', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockRejectedValueOnce(new TimeoutError('Request timed out'))
        .mockResolvedValue('success');
      
      const promise = middleware.execute(operation, 'test-operation', 'test-service');
      
      // Advance timers to allow retries
      jest.advanceTimersByTime(5000);
      await testUtils.flushPromises();
      
      const result = await promise;
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(3);
      
      const metrics = await middleware.getMetrics();
      expect(metrics.totalOperations).toBe(1);
      expect(metrics.successfulOperations).toBe(1);
      expect(metrics.operationsWithRetries).toBe(1);
      expect(metrics.retryMetrics.totalRetryAttempts).toBe(2);
    });

    it('should handle retry exhaustion and circuit breaker opening', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Persistent connection failure'));
      
      // First operation should exhaust retries
      await expect(middleware.execute(operation, 'test-operation', 'test-service'))
        .rejects.toThrow('Persistent connection failure');
      
      expect(operation).toHaveBeenCalledTimes(3); // maxAttempts
      
      // Continue failing to open circuit breaker
      for (let i = 0; i < 3; i++) {
        try {
          await middleware.execute(operation, 'test-operation', 'test-service');
        } catch (e) {
          // Expected failures
        }
      }
      
      // Circuit should now be open
      const blockedOperation = jest.fn().mockResolvedValue('success');
      await expect(middleware.execute(blockedOperation, 'test-operation', 'test-service'))
        .rejects.toThrow('Circuit breaker is OPEN');
      
      expect(blockedOperation).not.toHaveBeenCalled();
      
      const metrics = await middleware.getMetrics();
      expect(metrics.circuitBreakerMetrics.perServiceStatus['test-service']).toBe('OPEN');
    });

    it('should handle circuit breaker recovery', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      const successOperation = jest.fn().mockResolvedValue('success');
      
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        try {
          await middleware.execute(failingOperation, 'test-operation', 'test-service');
        } catch (e) {
          // Expected failures
        }
      }
      
      // Verify circuit is open
      let metrics = await middleware.getMetrics();
      expect(metrics.circuitBreakerMetrics.perServiceStatus['test-service']).toBe('OPEN');
      
      // Advance time past recovery timeout
      jest.advanceTimersByTime(30001);
      
      // Circuit should now be HALF_OPEN, allowing limited requests
      const result = await middleware.execute(successOperation, 'test-operation', 'test-service');
      expect(result).toBe('success');
      
      // After success threshold, circuit should close
      await middleware.execute(successOperation, 'test-operation', 'test-service');
      
      metrics = await middleware.getMetrics();
      expect(metrics.circuitBreakerMetrics.perServiceStatus['test-service']).toBe('CLOSED');
    });
  });

  describe('different error types and handling', () => {
    it('should handle rate limit errors with appropriate delays', async () => {
      const rateLimitError = new RateLimitError('Rate limit exceeded');
      (rateLimitError as any).headers = { 'retry-after': '2' };
      
      const operation = jest.fn()
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValue('success');
      
      const promise = middleware.execute(operation, 'test-operation', 'test-service');
      
      // Should wait for the retry-after period
      jest.advanceTimersByTime(2000);
      await testUtils.flushPromises();
      
      const result = await promise;
      expect(result).toBe('success');
    });

    it('should not retry authentication errors', async () => {
      const authError = new TestError('Invalid API key', 'AUTH_ERROR', false);
      const operation = jest.fn().mockRejectedValue(authError);
      
      await expect(middleware.execute(operation, 'test-operation', 'test-service'))
        .rejects.toThrow('Invalid API key');
      
      expect(operation).toHaveBeenCalledTimes(1);
      
      const metrics = await middleware.getMetrics();
      expect(metrics.retryMetrics.totalRetryAttempts).toBe(0);
    });

    it('should handle mixed error types in sequence', async () => {
      const operations = [
        jest.fn().mockRejectedValue(new NetworkError('Network error')),
        jest.fn().mockRejectedValue(new TimeoutError('Timeout error')),
        jest.fn().mockRejectedValue(new TestError('Auth error', 'AUTH', false)),
        jest.fn().mockResolvedValue('success')
      ];
      
      // Network error - should retry
      try {
        await middleware.execute(operations[0], 'network-op', 'test-service');
      } catch (e) {
        // Expected after retries
      }
      
      // Timeout error - should retry
      try {
        await middleware.execute(operations[1], 'timeout-op', 'test-service');
      } catch (e) {
        // Expected after retries
      }
      
      // Auth error - should not retry
      try {
        await middleware.execute(operations[2], 'auth-op', 'test-service');
      } catch (e) {
        // Expected immediately
      }
      
      // Success operation
      const result = await middleware.execute(operations[3], 'success-op', 'test-service');
      expect(result).toBe('success');
      
      const metrics = await middleware.getMetrics();
      expect(metrics.totalOperations).toBe(4);
      expect(metrics.successfulOperations).toBe(1);
      expect(metrics.failedOperations).toBe(3);
    });
  });

  describe('operation-specific policies', () => {
    it('should use different retry policies for different operations', async () => {
      const networkError = new NetworkError('Connection failed');
      
      // Tool execution should use 2 max attempts
      const toolOperation = jest.fn().mockRejectedValue(networkError);
      try {
        await middleware.execute(toolOperation, 'toolExecution', 'test-service');
      } catch (e) {
        // Expected
      }
      expect(toolOperation).toHaveBeenCalledTimes(2);
      
      // Provider calls should use 3 max attempts
      const providerOperation = jest.fn().mockRejectedValue(networkError);
      try {
        await middleware.execute(providerOperation, 'providerCalls', 'test-service');
      } catch (e) {
        // Expected
      }
      expect(providerOperation).toHaveBeenCalledTimes(3);
      
      // File operations should use 2 max attempts with different backoff
      const fileOperation = jest.fn().mockRejectedValue(networkError);
      try {
        await middleware.execute(fileOperation, 'fileOperations', 'test-service');
      } catch (e) {
        // Expected
      }
      expect(fileOperation).toHaveBeenCalledTimes(2);
    });
  });

  describe('metrics and monitoring', () => {
    it('should provide comprehensive metrics across multiple operations', async () => {
      const operations = [
        { op: jest.fn().mockResolvedValue('success1'), name: 'op1', service: 'service1' },
        { op: jest.fn().mockRejectedValueOnce(new NetworkError()).mockResolvedValue('success2'), name: 'op2', service: 'service1' },
        { op: jest.fn().mockRejectedValue(new TestError('Auth', 'AUTH', false)), name: 'op3', service: 'service2' },
        { op: jest.fn().mockResolvedValue('success3'), name: 'op4', service: 'service2' }
      ];
      
      // Execute operations
      await middleware.execute(operations[0].op, operations[0].name, operations[0].service);
      
      const promise = middleware.execute(operations[1].op, operations[1].name, operations[1].service);
      jest.advanceTimersByTime(2000);
      await testUtils.flushPromises();
      await promise;
      
      try {
        await middleware.execute(operations[2].op, operations[2].name, operations[2].service);
      } catch (e) {
        // Expected
      }
      
      await middleware.execute(operations[3].op, operations[3].name, operations[3].service);
      
      const metrics = await middleware.getMetrics();
      
      // Overall metrics
      expect(metrics.totalOperations).toBe(4);
      expect(metrics.successfulOperations).toBe(3);
      expect(metrics.failedOperations).toBe(1);
      expect(metrics.operationsWithRetries).toBe(1);
      
      // Retry metrics
      expect(metrics.retryMetrics.totalRetryAttempts).toBe(1);
      expect(metrics.retryMetrics.successfulRetries).toBe(1);
      
      // Per-service metrics
      expect(metrics.perServiceMetrics['service1']).toBeDefined();
      expect(metrics.perServiceMetrics['service2']).toBeDefined();
      expect(metrics.perServiceMetrics['service1'].totalRequests).toBe(2);
      expect(metrics.perServiceMetrics['service2'].totalRequests).toBe(2);
      
      // Recent errors
      expect(metrics.recentErrors).toHaveLength(1);
      expect(metrics.recentErrors[0].service).toBe('service2');
    });

    it('should track performance trends over time', async () => {
      const fastOperation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve('fast'), 100))
      );
      
      const slowOperation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve('slow'), 1000))
      );
      
      // Execute operations with different performance characteristics
      const promises = [
        middleware.execute(fastOperation, 'fast-op', 'test-service'),
        middleware.execute(slowOperation, 'slow-op', 'test-service')
      ];
      
      jest.advanceTimersByTime(1100);
      await Promise.all(promises);
      
      const metrics = await middleware.getMetrics();
      
      expect(metrics.performanceMetrics.averageOperationTime).toBeGreaterThan(0);
      expect(metrics.performanceMetrics.slowestOperationTime).toBeGreaterThan(metrics.performanceMetrics.fastestOperationTime);
    });
  });

  describe('configuration changes during runtime', () => {
    it('should adapt to configuration changes', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      // Initial configuration allows 3 attempts
      try {
        await middleware.execute(operation, 'test-operation', 'test-service');
      } catch (e) {
        // Expected
      }
      expect(operation).toHaveBeenCalledTimes(3);
      
      // Update configuration to allow 5 attempts
      const newConfig = {
        ...config,
        retry: { ...config.retry, maxAttempts: 5 }
      };
      middleware.updateConfig(newConfig);
      
      // Reset mock
      operation.mockClear();
      
      // Should now allow 5 attempts
      try {
        await middleware.execute(operation, 'test-operation', 'test-service');
      } catch (e) {
        // Expected
      }
      expect(operation).toHaveBeenCalledTimes(5);
    });
  });
});
