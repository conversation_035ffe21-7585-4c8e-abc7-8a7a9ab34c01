{"version": 3, "file": "DeepseekProvider.js", "sourceRoot": "", "sources": ["../../../src/core/providers/DeepseekProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,iDAA+E;AAG/E,MAAa,gBAAiB,SAAQ,2BAAY;IACzC,IAAI,GAAG,UAAU,CAAC;IACjB,MAAM,CAAgB;IAE9B,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,6BAA6B;YAC7D,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK,EAAE,aAAa;SAC9B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CACtB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,WAAW,GAAQ;gBACvB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,MAAM,EAAE,KAAK;aACd,CAAC;YAEF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC7B,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC3B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,WAAW,CAAC,KAAK,GAAG,cAAc,CAAC;gBACnC,WAAW,CAAC,WAAW,GAAG,MAAM,CAAC;YACnC,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YAE1E,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,CAAC,aAAa,CACzB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,WAAW,GAAQ;gBACvB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC7B,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC3B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,WAAW,CAAC,KAAK,GAAG,cAAc,CAAC;gBACnC,WAAW,CAAC,WAAW,GAAG,MAAM,CAAC;YACnC,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE;gBACxE,YAAY,EAAE,QAAQ;aACvB,CAAC,CAAC;YAEH,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAU,EAAE,CAAC;YAC1B,IAAI,KAAK,GAAQ,IAAI,CAAC;YAEtB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE7B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtD,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAErC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;wBAClB,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC;wBAC7B,MAAM,KAAK,CAAC,OAAO,CAAC;oBACtB,CAAC;oBAED,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wBACrB,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;gBAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,oCAAoC,SAAS,CAAC,MAAM,yBAAyB,CAAC,CAAC;gBAC3F,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACtF,CAAC;YAED,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE9F,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,EAAE;gBACtD,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,aAAa;oBACjC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;oBACzC,WAAW,EAAE,KAAK,CAAC,YAAY;iBAChC,CAAC,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,kBAAkB,EAAE,yDAAyD;aACzF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,CAAC,cAAc,CAAC,MAAW;QACvC,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;YAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC3B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACtB,OAAO;oBACT,CAAC;oBAED,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,MAAM,CAAC;oBACf,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,oBAAoB;wBACpB,SAAS;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAa;QACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE5F,kCAAkC;QAClC,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,2BAA2B,SAAS,CAAC,MAAM,yBAAyB,CAAC,CAAC;YAClF,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE;YAClD,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;gBAC1C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB;gBAClD,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;aACzC,CAAC,CAAC,CAAC,SAAS;YACb,SAAS,EAAE,yDAAyD;YACpE,YAAY,EAAE,MAAM,CAAC,aAAa;SACnC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,SAAgB;QACtC,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACxB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC;SACxD,CAAC,CAAC,CAAC;IACN,CAAC;IAES,cAAc,CAAC,QAAuB;QAC9C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,SAAS,GAAQ;gBACrB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC;YAEF,0CAA0C;YAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC9C,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACvD,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;qBAC3C;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,wBAAwB;YACxB,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,SAAS,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;YACpD,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI;iBACtB,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;iBACrD,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;iBAC7B,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAC,2BAA2B;QACzE,CAAC;IACH,CAAC;IAES,cAAc;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,6BAA6B,CAAC;QACtD,CAAC;IACH,CAAC;IAES,WAAW,CAAC,KAAU,EAAE,OAAe;QAC/C,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YACrC,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;YAEjC,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;gBAC9E,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;gBACjF,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAChF,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBACzE;oBACE,MAAM,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;oBACtD,OAAO,IAAI,KAAK,CAAC,uBAAuB,MAAM,MAAM,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;CACF;AAxRD,4CAwRC"}