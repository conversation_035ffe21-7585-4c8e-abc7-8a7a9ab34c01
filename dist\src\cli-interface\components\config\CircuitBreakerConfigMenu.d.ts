import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class CircuitBreakerConfigMenu extends BaseComponent {
    private configManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private showCircuitBreakerMenu;
    private configureBasicSettings;
    private configureTimingSettings;
    private configureThresholds;
    private configurePerServiceSettings;
    private testCircuitBreaker;
    private simulateScenario;
    private showConfigurationDetails;
    private resetCircuitBreakerConfig;
}
//# sourceMappingURL=CircuitBreakerConfigMenu.d.ts.map