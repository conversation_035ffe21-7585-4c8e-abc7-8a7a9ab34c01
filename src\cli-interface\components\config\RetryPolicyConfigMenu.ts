import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';

export class RetryPolicyConfigMenu extends BaseComponent {
  private configManager: ConfigManager;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🔄 Retry Policy Configuration', 'Configure retry behavior and backoff strategies');

    await this.showRetryPolicyMenu();
  }

  private async showRetryPolicyMenu(): Promise<void> {
    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    // Display current configuration
    this.utils.showInfo('Current Retry Policy Settings:');
    console.log(`  Max Attempts: ${currentConfig.maxAttempts || 3}`);
    console.log(`  Base Delay: ${currentConfig.baseDelay || 1000}ms`);
    console.log(`  Max Delay: ${currentConfig.maxDelay || 60000}ms`);
    console.log(`  Backoff Multiplier: ${currentConfig.backoffMultiplier || 2}`);
    console.log(`  Backoff Strategy: ${currentConfig.backoffStrategy || 'exponential'}`);
    console.log(`  Enable Jitter: ${currentConfig.enableJitter !== false ? 'Yes' : 'No'}`);
    console.log(`  Jitter Type: ${currentConfig.jitterType || 'full'}`);
    console.log();

    const choices = [
      { name: '⚙️ Configure Basic Settings', value: 'basic' },
      { name: '🎯 Configure Backoff Strategy', value: 'backoff' },
      { name: '🎲 Configure Jitter Settings', value: 'jitter' },
      { name: '🧪 Test Retry Policy', value: 'test' },
      { name: '📋 View Policy Details', value: 'details' },
      { name: '🔄 Reset to Defaults', value: 'reset' },
      { name: '🔙 Back to Error Handling Config', value: 'back' },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices,
      },
    ]);

    switch (action) {
      case 'basic':
        await this.configureBasicSettings();
        break;
      case 'backoff':
        await this.configureBackoffStrategy();
        break;
      case 'jitter':
        await this.configureJitterSettings();
        break;
      case 'test':
        await this.testRetryPolicy();
        break;
      case 'details':
        await this.showPolicyDetails();
        break;
      case 'reset':
        await this.resetRetryPolicy();
        break;
      case 'back':
        return;
    }

    await this.showRetryPolicyMenu();
  }

  private async configureBasicSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('⚙️ Basic Retry Settings');

    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    const answers = await inquirer.prompt([
      {
        type: 'number',
        name: 'maxAttempts',
        message: 'Maximum retry attempts (1-10):',
        default: currentConfig.maxAttempts || 3,
        validate: (value) => {
          if (value < 1 || value > 10) {
            return 'Must be between 1 and 10';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'baseDelay',
        message: 'Base delay in milliseconds (100-10000):',
        default: currentConfig.baseDelay || 1000,
        validate: (value) => {
          if (value < 100 || value > 10000) {
            return 'Must be between 100 and 10000 milliseconds';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'maxDelay',
        message: 'Maximum delay in milliseconds (1000-300000):',
        default: currentConfig.maxDelay || 60000,
        validate: (value) => {
          if (value < 1000 || value > 300000) {
            return 'Must be between 1000 and 300000 milliseconds (5 minutes max)';
          }
          return true;
        },
      },
    ]);

    // Validate that maxDelay >= baseDelay
    if (answers.maxDelay < answers.baseDelay) {
      this.utils.showError('Maximum delay must be greater than or equal to base delay');
      await this.utils.waitForKeyPress();
      return;
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        retry: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Basic retry settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update settings: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureBackoffStrategy(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🎯 Backoff Strategy Configuration');

    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    this.utils.showInfo('Backoff strategies determine how delay increases between retry attempts:');
    console.log('• Linear: delay = baseDelay * attempt');
    console.log('• Exponential: delay = baseDelay * (multiplier ^ attempt)');
    console.log('• Fixed: delay = baseDelay (constant)');
    console.log();

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'backoffStrategy',
        message: 'Select backoff strategy:',
        choices: [
          { name: 'Exponential (recommended)', value: 'exponential' },
          { name: 'Linear', value: 'linear' },
          { name: 'Fixed', value: 'fixed' },
        ],
        default: currentConfig.backoffStrategy || 'exponential',
      },
    ]);

    if (answers.backoffStrategy === 'exponential') {
      const multiplierAnswer = await inquirer.prompt([
        {
          type: 'number',
          name: 'backoffMultiplier',
          message: 'Backoff multiplier (1.1-5.0):',
          default: currentConfig.backoffMultiplier || 2,
          validate: (value) => {
            if (value < 1.1 || value > 5.0) {
              return 'Must be between 1.1 and 5.0';
            }
            return true;
          },
        },
      ]);
      answers.backoffMultiplier = multiplierAnswer.backoffMultiplier;
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        retry: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Backoff strategy updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update strategy: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureJitterSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🎲 Jitter Configuration');

    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    this.utils.showInfo('Jitter adds randomness to retry delays to prevent thundering herd problems:');
    console.log('• Full Jitter: delay = random(0, calculated_delay)');
    console.log('• Equal Jitter: delay = calculated_delay/2 + random(0, calculated_delay/2)');
    console.log('• Decorrelated Jitter: delay = random(base_delay, previous_delay * 3)');
    console.log('• None: delay = calculated_delay (no randomness)');
    console.log();

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'jitterType',
        message: 'Select jitter type:',
        choices: [
          { name: 'Full Jitter (recommended)', value: 'full' },
          { name: 'Equal Jitter', value: 'equal' },
          { name: 'Decorrelated Jitter', value: 'decorrelated' },
          { name: 'None', value: 'none' },
        ],
        default: currentConfig.jitterType || 'full',
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        retry: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Jitter settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update jitter: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async testRetryPolicy(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🧪 Test Retry Policy');

    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    this.utils.showInfo('This will simulate retry delays based on your current configuration:');
    console.log();

    const maxAttempts = currentConfig.maxAttempts || 3;
    const baseDelay = currentConfig.baseDelay || 1000;
    const maxDelay = currentConfig.maxDelay || 60000;
    const backoffMultiplier = currentConfig.backoffMultiplier || 2;
    const jitterType = currentConfig.jitterType || 'full';

    console.log('Simulated retry delays:');
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      let delay = baseDelay;
      
      // Calculate delay based on backoff strategy
      if (currentConfig.backoffStrategy === 'exponential') {
        delay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt - 1), maxDelay);
      } else if (currentConfig.backoffStrategy === 'linear') {
        delay = Math.min(baseDelay * attempt, maxDelay);
      }

      // Apply jitter
      let jitteredDelay = delay;
      if (jitterType === 'full') {
        jitteredDelay = Math.random() * delay;
      } else if (jitterType === 'decorrelated') {
        jitteredDelay = Math.random() * (delay * 3);
      }

      console.log(`  Attempt ${attempt}: ${Math.round(jitteredDelay)}ms (base: ${delay}ms)`);
    }

    console.log();
    await this.utils.waitForKeyPress();
  }

  private async showPolicyDetails(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📋 Retry Policy Details');

    const currentConfig = this.configManager.getConfig().errorHandling?.retry || {};

    this.utils.showInfo('Current Retry Policy Configuration:');
    console.log();
    console.log(`📊 Basic Settings:`);
    console.log(`  • Maximum Attempts: ${currentConfig.maxAttempts || 3}`);
    console.log(`  • Base Delay: ${currentConfig.baseDelay || 1000}ms`);
    console.log(`  • Maximum Delay: ${currentConfig.maxDelay || 60000}ms`);
    console.log();
    console.log(`🎯 Backoff Strategy:`);
    console.log(`  • Strategy: ${currentConfig.backoffStrategy || 'exponential'}`);
    console.log(`  • Multiplier: ${currentConfig.backoffMultiplier || 2}`);
    console.log();
    console.log(`🎲 Jitter Settings:`);
    console.log(`  • Type: ${currentConfig.jitterType || 'full'}`);
    console.log();
    console.log(`📈 Calculated Behavior:`);
    const totalMaxTime = this.calculateMaxRetryTime(currentConfig);
    console.log(`  • Maximum total retry time: ~${Math.round(totalMaxTime / 1000)}s`);
    console.log(`  • Recommended for: ${this.getRecommendation(currentConfig)}`);

    console.log();
    await this.utils.waitForKeyPress();
  }

  private calculateMaxRetryTime(config: any): number {
    const maxAttempts = config.maxAttempts || 3;
    const baseDelay = config.baseDelay || 1000;
    const maxDelay = config.maxDelay || 60000;
    const backoffMultiplier = config.backoffMultiplier || 2;

    let totalTime = 0;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      let delay = baseDelay;
      if (config.backoffStrategy === 'exponential') {
        delay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt - 1), maxDelay);
      } else if (config.backoffStrategy === 'linear') {
        delay = Math.min(baseDelay * attempt, maxDelay);
      }
      totalTime += delay;
    }
    return totalTime;
  }

  private getRecommendation(config: any): string {
    const maxAttempts = config.maxAttempts || 3;
    const baseDelay = config.baseDelay || 1000;

    if (maxAttempts <= 2 && baseDelay <= 500) {
      return 'Fast operations, low latency requirements';
    } else if (maxAttempts <= 3 && baseDelay <= 2000) {
      return 'Standard operations, balanced reliability';
    } else if (maxAttempts <= 5 && baseDelay <= 5000) {
      return 'Critical operations, high reliability needs';
    } else {
      return 'Long-running operations, maximum reliability';
    }
  }

  private async resetRetryPolicy(): Promise<void> {
    const confirmed = await this.confirmAction(
      'This will reset retry policy settings to defaults. Continue?'
    );

    if (!confirmed) {
      return;
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        retry: {
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 60000,
          backoffMultiplier: 2,
          backoffStrategy: 'exponential',
          enableJitter: true,
          jitterType: 'full',
        },
      });
      this.utils.showSuccess('Retry policy reset to defaults!');
    } catch (error) {
      this.utils.showError(`Failed to reset policy: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }
}
