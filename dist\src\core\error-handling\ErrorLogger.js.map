{"version": 3, "file": "ErrorLogger.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorLogger.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,IAAY,QAMX;AAND,WAAY,QAAQ;IAClB,yCAAS,CAAA;IACT,uCAAQ,CAAA;IACR,uCAAQ,CAAA;IACR,yCAAS,CAAA;IACT,yCAAS,CAAA;AACX,CAAC,EANW,QAAQ,wBAAR,QAAQ,QAMnB;AA2BD,MAAa,WAAW;IACd,MAAM,CAAe;IACrB,UAAU,GAAe,EAAE,CAAC;IAC5B,WAAW,GAAqC,EAAE,CAAC;IAE3D,YAAY,SAAgC,EAAE;QAC5C,IAAI,CAAC,MAAM,GAAG;YACZ,KAAK,EAAE,QAAQ,CAAC,IAAI;YACpB,mBAAmB,EAAE,IAAI;YACzB,gBAAgB,EAAE,KAAK;YACvB,uBAAuB,EAAE,IAAI;YAC7B,aAAa,EAAE,IAAI;YACnB,iBAAiB,EAAE,IAAI;YACvB,gBAAgB,EAAE,IAAI;YACtB,UAAU,EAAE,KAAK;YACjB,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe,EAAE,OAA6B,EAAE,WAAoB;QACxE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,OAAe,EAAE,OAA6B,EAAE,WAAoB;QACvE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,OAAe,EAAE,OAA6B,EAAE,WAAoB;QACvE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe,EAAE,KAAa,EAAE,OAA6B,EAAE,WAAoB;QACvF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe,EAAE,KAAa,EAAE,OAA6B,EAAE,WAAoB;QACvF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,eAAe,CACb,SAAiB,EACjB,OAAe,EACf,WAAmB,EACnB,KAAa,EACb,KAAY,EACZ,WAAoB;QAEpB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAC7B,iBAAiB,OAAO,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,UAAU,EACjF;YACE,SAAS;YACT,OAAO;YACP,WAAW;YACX,KAAK;YACL,YAAY,EAAE,KAAK,CAAC,OAAO;YAC3B,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;SAClC,EACD,KAAK,EACL,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,4BAA4B,CAC1B,WAAmB,EACnB,QAAgB,EAChB,QAAgB,EAChB,MAAe,EACf,WAAoB;QAEpB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,iBAAiB,EACvC,uBAAuB,WAAW,KAAK,QAAQ,OAAO,QAAQ,EAAE,EAChE;YACE,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,MAAM;SACP,EACD,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB,EAAE,UAA+B,EAAE,WAAoB;QACxF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,iBAAiB,EACxC,uBAAuB,SAAS,EAAE,EAClC;YACE,SAAS;YACT,UAAU;SACX,EACD,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB,CAClB,SAAiB,EACjB,OAAgB,EAChB,QAAgB,EAChB,MAAY,EACZ,WAAoB;QAEpB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,oBAAoB,EACrE,aAAa,SAAS,IAAI,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,OAAO,QAAQ,IAAI,EAC7E;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;SACrC,EACD,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CACnB,SAAiB,EACjB,OAIC,EACD,WAAoB;QAEpB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,EACpC,2BAA2B,SAAS,EAAE,EACtC;YACE,SAAS;YACT,GAAG,OAAO;SACX,EACD,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,GAAG,CACT,KAAe,EACf,QAAgB,EAChB,OAAe,EACf,OAA6B,EAC7B,KAAa,EACb,WAAoB;QAEpB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,CAAC,mCAAmC;QAC7C,CAAC;QAED,MAAM,KAAK,GAAa;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK;YACL,QAAQ;YACR,OAAO;YACP,OAAO;YACP,KAAK;YACL,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YAC5E,WAAW;SACZ,CAAC;QAEF,8BAA8B;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACtE,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,KAAe;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5C,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;YAC/C,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,GAAG,SAAS,GAAG,SAAS,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;QAE9D,IAAI,UAAU,GAAG,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAE9C,IAAI,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,UAAU,IAAI,cAAc,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;QACvE,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,UAAU,IAAI,YAAY,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAChD,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACrB,UAAU,IAAI,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,oDAAoD;QACpD,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;YACpB,KAAK,QAAQ,CAAC,KAAK;gBACjB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,QAAQ,CAAC,IAAI;gBAChB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ,CAAC,IAAI;gBAChB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ,CAAC,KAAK,CAAC;YACpB,KAAK,QAAQ,CAAC,KAAK;gBACjB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC1B,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,SAAe;QACrC,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,KAAK,KAAK;gBACR,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC;YACjC,KAAK,OAAO;gBACV,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;YACpC,KAAK,MAAM;gBACT,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC;YACxC;gBACE,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAkC;QAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,OAAkC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB,GAAG,EAAE,KAAgB;QACjD,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;QAEzC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAOV;QACC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACpC,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;gBAAE,OAAO,KAAK,CAAC;YAC5E,IAAI,QAAQ,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAC;YACrF,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;gBAAE,OAAO,KAAK,CAAC;YAC/E,IAAI,QAAQ,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAC7E,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,OAAO;gBAAE,OAAO,KAAK,CAAC;YACzE,IAAI,QAAQ,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC;gBAAE,OAAO,KAAK,CAAC;YAEhG,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAKV;QACC,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QACpE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAA6B;QACxC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,gBAAgB;QAOd,MAAM,WAAW,GAA2B,EAAE,CAAC;QAC/C,MAAM,cAAc,GAA2B,EAAE,CAAC;QAElD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3D,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACjC,WAAW;YACX,cAAc;YACd,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;YAChF,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;SAC1G,CAAC;IACJ,CAAC;CACF;AAhYD,kCAgYC"}