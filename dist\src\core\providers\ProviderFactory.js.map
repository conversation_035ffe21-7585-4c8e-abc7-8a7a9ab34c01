{"version": 3, "file": "ProviderFactory.js", "sourceRoot": "", "sources": ["../../../../src/core/providers/ProviderFactory.ts"], "names": [], "mappings": ";;;AACA,qDAAkD;AAClD,2DAAwD;AACxD,qDAAkD;AAClD,yDAAsD;AACtD,oCAA+C;AAE/C,MAAa,eAAe;IAClB,MAAM,CAAC,SAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;IAEzD,MAAM,CAAC,cAAc,CAAC,YAAoB,EAAE,MAAsB;QACvE,IAAI,CAAC,2BAAmB,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,sEAAsE;QACtE,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;YACjF,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,IAAI,QAAsB,CAAC;QAE3B,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,QAAQ,GAAG,IAAI,+BAAc,CAAC,MAAM,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,WAAW;gBACd,QAAQ,GAAG,IAAI,qCAAiB,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,QAAQ;gBACX,QAAQ,GAAG,IAAI,+BAAc,CAAC,MAAM,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,UAAU;gBACb,QAAQ,GAAG,IAAI,mCAAgB,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,sCAAsC,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAC3C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,YAAoB;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,YAAoB;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,cAAc,CAAC,YAAoB;QAC/C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,cAAc;QAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAEM,MAAM,CAAC,kBAAkB;QAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,YAAoB,EAAE,MAAsB;QAC3E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC3D,OAAO,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAAC,YAAoB,EAAE,MAA+B;QACxF,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,YAAY,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;QAEvD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,CAAC,IAAI,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;YACjD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,uCAAuC;YACvC,QAAQ,YAAY,EAAE,CAAC;gBACrB,KAAK,QAAQ;oBACX,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBACrC,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;oBACxD,CAAC;oBACD,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;wBACzC,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;oBAC/D,CAAC;oBACD,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;wBAC9B,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;oBAClE,CAAC;oBACD,MAAM;gBACR,KAAK,UAAU;oBACb,sDAAsD;oBACtD,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9D,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,YAAY,yBAAyB,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC;YAC3F,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,MAAM,CAAC,uBAAuB,CAAC,YAAoB;QAOxD,MAAM,YAAY,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,YAAY,GAAG;YACnB,iBAAiB,EAAE,IAAI,EAAE,kCAAkC;YAC3D,aAAa,EAAE,IAAI,EAAE,8BAA8B;YACnD,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,IAAI;YACf,eAAe,EAAE,YAAY,CAAC,MAAM;SACrC,CAAC;QAEF,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC;gBACnC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,cAAc;gBAC/C,MAAM;YACR,KAAK,WAAW;gBACd,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC;gBACnC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,WAAW;gBAC5C,MAAM;YACR,KAAK,QAAQ;gBACX,YAAY,CAAC,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBAChF,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,aAAa;gBAC7C,MAAM;YACR,KAAK,UAAU;gBACb,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,kBAAkB;gBAClD,MAAM;QACV,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,OAAuB,EAAE,OAAuB;QAC3E,OAAO,CACL,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;YACjC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;YACnC,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY;YAC7C,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;YACvC,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW;YAC3C,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY,CAC9C,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,GAAW;QACnC,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,qBAAqB;QACjC,OAAO,MAAM,CAAC,IAAI,CAAC,2BAAmB,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,YAAoB;QAChD,OAAO,2BAAmB,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,MAAsB;QACjF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAE3D,kDAAkD;YAClD,IAAI,oBAAoB,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,kBAAkB,KAAK,UAAU,EAAE,CAAC;gBAC1F,OAAO,MAAO,QAAgB,CAAC,kBAAkB,EAAE,CAAC;YACtD,CAAC;YAED,gCAAgC;YAChC,OAAO,2BAAmB,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,2BAAmB,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;QAClD,CAAC;IACH,CAAC;;AA/MH,0CAgNC"}