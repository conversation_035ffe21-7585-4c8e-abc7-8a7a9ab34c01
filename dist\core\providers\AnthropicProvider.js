"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicProvider = void 0;
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const BaseProvider_1 = require("./BaseProvider");
class AnthropicProvider extends BaseProvider_1.BaseProvider {
    name = 'anthropic';
    client;
    constructor(config) {
        super(config);
        this.validateConfig();
        this.client = new sdk_1.default({
            apiKey: this.config.apiKey,
            baseURL: this.config.baseUrl,
        });
    }
    async sendMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const { systemMessage, formattedMessages } = this.formatMessages(messages);
            const formattedTools = this.formatTools(tools);
            const requestParams = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                ...(systemMessage && { system: systemMessage }),
                ...(formattedTools.length > 0 && { tools: formattedTools }),
            };
            const response = await this.client.messages.create(requestParams);
            return this.processResponse(response, requestOptions.model);
        }
        catch (error) {
            throw this.handleError(error, 'sendMessage');
        }
    }
    async *streamMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const { systemMessage, formattedMessages } = this.formatMessages(messages);
            const formattedTools = this.formatTools(tools);
            const requestParams = {
                model: requestOptions.model,
                messages: formattedMessages,
                max_tokens: requestOptions.maxTokens,
                temperature: requestOptions.temperature,
                stream: true,
                ...(systemMessage && { system: systemMessage }),
                ...(formattedTools.length > 0 && { tools: formattedTools }),
            };
            const stream = await this.client.messages.create(requestParams);
            let fullContent = '';
            let toolCalls = [];
            let usage = null;
            for await (const chunk of stream) {
                if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
                    const text = chunk.delta.text;
                    fullContent += text;
                    yield text;
                }
                if (chunk.type === 'content_block_start' && chunk.content_block.type === 'tool_use') {
                    toolCalls.push({
                        id: chunk.content_block.id,
                        name: chunk.content_block.name,
                        input: chunk.content_block.input,
                    });
                }
                if (chunk.type === 'message_delta' && chunk.usage) {
                    usage = chunk.usage;
                }
            }
            // Debug: Log tool call processing for streaming
            if (toolCalls.length > 0) {
                console.log(`AnthropicProvider (stream): Found ${toolCalls.length} tool calls in response`);
                console.log('AnthropicProvider (stream): Tool calls:', toolCalls.map(tc => tc.name));
            }
            const formattedToolCalls = toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined;
            return this.createChatMessage(fullContent, 'assistant', {
                provider: this.name,
                model: requestOptions.model,
                usage: usage ? {
                    promptTokens: usage.input_tokens,
                    completionTokens: usage.output_tokens,
                    totalTokens: usage.input_tokens + usage.output_tokens,
                } : undefined,
                toolCalls: formattedToolCalls, // Put toolCalls at top level, not in nested metadata
            });
        }
        catch (error) {
            throw this.handleError(error, 'streamMessage');
        }
    }
    processResponse(response, model) {
        let content = '';
        let toolCalls = [];
        for (const block of response.content) {
            if (block.type === 'text') {
                content += block.text;
            }
            else if (block.type === 'tool_use') {
                toolCalls.push({
                    id: block.id,
                    name: block.name,
                    input: block.input,
                });
            }
        }
        // Debug: Log tool call processing
        if (toolCalls.length > 0) {
            console.log(`AnthropicProvider: Found ${toolCalls.length} tool calls in response`);
            console.log('AnthropicProvider: Tool calls:', toolCalls.map(tc => tc.name));
        }
        const formattedToolCalls = toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined;
        return this.createChatMessage(content, 'assistant', {
            provider: this.name,
            model,
            usage: response.usage ? {
                promptTokens: response.usage.input_tokens,
                completionTokens: response.usage.output_tokens,
                totalTokens: response.usage.input_tokens + response.usage.output_tokens,
            } : undefined,
            toolCalls: formattedToolCalls, // Put toolCalls at top level, not in nested metadata
            finishReason: response.stop_reason,
        });
    }
    formatToolCalls(toolCalls) {
        return toolCalls.map(call => ({
            id: call.id,
            name: call.name,
            parameters: call.input,
        }));
    }
    formatMessages(messages) {
        let systemMessage = this.config.systemPrompt;
        const formattedMessages = [];
        for (const msg of messages) {
            if (msg.role === 'system') {
                // Anthropic handles system messages separately
                systemMessage = systemMessage ? `${systemMessage}\n\n${msg.content}` : msg.content;
                continue;
            }
            const formatted = {
                role: msg.role === 'assistant' ? 'assistant' : 'user',
                content: [],
            };
            // Add text content
            if (msg.content) {
                formatted.content.push({
                    type: 'text',
                    text: msg.content,
                });
            }
            // Handle tool calls in assistant messages
            if (msg.role === 'assistant' && msg.toolCalls) {
                for (const call of msg.toolCalls) {
                    formatted.content.push({
                        type: 'tool_use',
                        id: call.id,
                        name: call.name,
                        input: call.parameters,
                    });
                }
            }
            // Handle tool responses
            if (msg.role === 'tool') {
                formatted.content = [{
                        type: 'tool_result',
                        tool_use_id: msg.metadata?.toolCallId,
                        content: msg.content,
                    }];
            }
            formattedMessages.push(formatted);
        }
        return { systemMessage, formattedMessages };
    }
    formatTools(tools) {
        if (!tools || tools.length === 0) {
            return [];
        }
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            input_schema: {
                type: 'object',
                properties: tool.parameters.properties || {},
                required: tool.parameters.required || [],
            },
        }));
    }
    async testConnection() {
        try {
            const testMessage = {
                id: 'test',
                role: 'user',
                content: 'Hello',
                timestamp: new Date(),
            };
            await this.sendMessage([testMessage], [], { maxTokens: 10 });
            return true;
        }
        catch (error) {
            console.error('Anthropic connection test failed:', error);
            return false;
        }
    }
    validateConfig() {
        super.validateConfig();
        if (!this.config.apiKey.startsWith('sk-ant-')) {
            throw new Error('Anthropic API key should start with "sk-ant-"');
        }
    }
}
exports.AnthropicProvider = AnthropicProvider;
//# sourceMappingURL=AnthropicProvider.js.map