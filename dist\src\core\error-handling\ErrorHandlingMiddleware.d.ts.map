{"version": 3, "file": "ErrorHandlingMiddleware.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorHandlingMiddleware.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AAIH,OAAO,EAAmB,aAAa,EAAE,MAAM,mBAAmB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAY,MAAM,eAAe,CAAC;AACtD,OAAO,EAAE,0BAA0B,EAAE,MAAM,UAAU,CAAC;AAEtD,MAAM,WAAW,oBAAoB;IACnC,aAAa,EAAE,eAAe,GAAG,cAAc,GAAG,eAAe,GAAG,gBAAgB,GAAG,SAAS,CAAC;IACjG,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,mBAAmB,CAAC,CAAC;IACpC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,QAAQ,CAAC,EAAE,aAAa,CAAC;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,uBAAuB,EAAE,OAAO,CAAC;IACjC,WAAW,EAAE,MAAM,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,gBAAgB,EAAE,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAC7F,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,KAAK,IAAI,CAAC;IACrD,wBAAwB,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,KAAK,IAAI,CAAC;IACzD,sBAAsB,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,KAAK,IAAI,CAAC;IACvD,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;CACvB;AAED,qBAAa,uBAAuB;IAClC,OAAO,CAAC,MAAM,CAA6B;IAC3C,OAAO,CAAC,UAAU,CAAkB;IACpC,OAAO,CAAC,sBAAsB,CAAyB;IACvD,OAAO,CAAC,aAAa,CAAuC;IAC5D,OAAO,CAAC,kBAAkB,CAAmC;IAC7D,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,MAAM,CAAc;gBAEhB,MAAM,EAAE,0BAA0B;IAyB9C;;OAEG;IACG,wBAAwB,CAAC,CAAC,EAC9B,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,EAAE,oBAAoB,EAC7B,gBAAgB,CAAC,EAAE,gBAAgB,GAClC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAwDlC;;OAEG;YACW,qBAAqB;IAsEnC;;OAEG;IACH,OAAO,CAAC,WAAW;IA+CnB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA8C3B;;OAEG;IACH,eAAe,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAI1C;;OAEG;IACH,OAAO,CAAC,cAAc;IAYtB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAQ/B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAM1B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAI3B;;OAEG;IACH,wBAAwB,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAI/C;;OAEG;IACH,kBAAkB;IAIlB;;OAEG;IACH,mBAAmB,IAAI,gBAAgB;IAIvC;;OAEG;IACH,SAAS,IAAI,WAAW;IAIxB;;OAEG;IACH,aAAa,CAAC,KAAK,GAAE,MAAY;IAIjC;;OAEG;IACH,aAAa,IAAI,MAAM;IAIvB;;OAEG;IACH,UAAU,IAAI,MAAM;IAIpB;;OAEG;IACH,oBAAoB,IAAI,IAAI;IAI5B;;OAEG;IACH,mBAAmB,IAAI,IAAI;IAK3B;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,0BAA0B,CAAC,GAAG,IAAI;IAY/D;;OAEG;IACH,SAAS,IAAI,0BAA0B;CAGxC"}