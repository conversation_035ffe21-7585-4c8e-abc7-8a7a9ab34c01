import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { AIEngine } from '../../../core/engine/AIEngine';
import { CLIState, CLIConfig, TerminalSession, TerminalMessage } from '../../types';
export declare class TerminalManager extends BaseComponent {
    private configManager;
    private aiEngine;
    private terminalInterface;
    private messageRenderer;
    private inputHandler;
    private toolConfirmationHandler;
    private systemPromptRegistry;
    private errorHandler;
    private currentSession;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager, aiEngine: AIEngine);
    render(): Promise<void>;
    private initializeSession;
    private createWelcomeMessage;
    private getEnabledToolsCount;
    private startConversationLoop;
    private handleCommand;
    private processUserMessage;
    private showHelp;
    private showHistory;
    private clearConversation;
    private switchProvider;
    private exitTerminal;
    getCurrentSession(): TerminalSession | null;
    addMessage(message: TerminalMessage): Promise<void>;
    confirmToolExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
    startNewConversation(): Promise<void>;
    loadConversation(conversationId: string): Promise<void>;
    private handlePromptCommand;
    private showPromptHelp;
    private listSystemPrompts;
    private showActivePrompt;
    private setActivePrompt;
    private searchPrompts;
    private showPromptInfo;
}
//# sourceMappingURL=TerminalManager.d.ts.map