{"version": 3, "file": "OpenAIProvider.js", "sourceRoot": "", "sources": ["../../../../src/core/providers/OpenAIProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,iDAA+E;AAG/E,MAAa,cAAe,SAAQ,2BAAY;IACvC,IAAI,GAAG,QAAQ,CAAC;IACf,MAAM,CAAS;IAEvB,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;SAC7B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CACtB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,OAAO,IAAI,CAAC,wBAAwB,CAClC,KAAK,IAAI,EAAE;YACT,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,aAAa,GAA2C;gBAC5D,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAA6D;gBACvE,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;aAC5D,CAAC;YAEF,2DAA2D;YAC3D,kCAAkC;YAClC,qCAAqC;YACrC,sBAAsB;YACtB,yCAAyC;YACzC,QAAQ;YACR,IAAI;YAEJ,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAE1E,0BAA0B;YAC1B,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAEpF,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC,EACD,aAAa,EACb;YACE,QAAQ,EAAE;gBACR,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,SAAS,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC;gBAC7B,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBACjD,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;aACvD;SACF,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,CAAC,aAAa,CACzB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,6EAA6E;QAC7E,mFAAmF;QACnF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACzD,KAAK,IAAI,EAAE;YACT,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,aAAa,GAA2C;gBAC5D,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAA6D;gBACvE,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,MAAM,EAAE,IAAI;gBACZ,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;aAC5D,CAAC;YAEF,2DAA2D;YAC3D,kCAAkC;YAClC,qCAAqC;YACrC,sBAAsB;YACtB,yCAAyC;YACzC,QAAQ;YACR,IAAI;YAEJ,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAClE,CAAC,EACD,eAAe,EACf;YACE,QAAQ,EAAE;gBACR,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,SAAS,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC;gBAC7B,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBACjD,SAAS,EAAE,IAAI;aAChB;SACF,CACF,CAAC;QAEF,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,SAAS,GAAU,EAAE,CAAC;QAC1B,IAAI,KAAK,GAAQ,IAAI,CAAC;QAEtB,IAAI,CAAC;YACH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBAC1C,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAEtC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oBACnB,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC;oBAC7B,MAAM,KAAK,CAAC,OAAO,CAAC;gBACtB,CAAC;gBAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oBACtB,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;gBACtC,CAAC;gBAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAC5E,OAAO,CAAC,IAAI,CAAC,eAAe,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,EAAE;gBACtD,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,aAAa;oBACjC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;oBACzC,WAAW,EAAE,KAAK,CAAC,YAAY;iBAChC,CAAC,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;aAC9E,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,0BAA0B;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAgD;QACtE,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACtC,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE5F,mEAAmE;QACnE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,aAAa,KAAK,YAAY,EAAE,CAAC;YACrF,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,aAAa,OAAO,GAAG,CAAC,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7D,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC,yBAAyB,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;YAChE,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE;YAClD,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;gBAC1C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB;gBAClD,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;aACzC,CAAC,CAAC,CAAC,SAAS;YACb,SAAS;YACT,YAAY,EAAE,MAAM,CAAC,aAAa;SACnC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,SAAgB;QACtC,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACxB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC;SACxD,CAAC,CAAC,CAAC;IACN,CAAC;IAES,cAAc,CAAC,QAAuB;QAC9C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,SAAS,GAAQ;gBACrB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC;YAEF,0CAA0C;YAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC9C,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACvD,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;qBAC3C;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,wBAAwB;YACxB,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,SAAS,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;YACpD,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAClD,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAC/B,gBAAgB,CACjB,CAAC;YACF,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAClD,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAC/B,oBAAoB,CACrB,CAAC;YACF,OAAO,QAAQ,CAAC,IAAI;iBACjB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACzC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;iBACtB,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC,2BAA2B;QAC/E,CAAC;IACH,CAAC;IAES,cAAc;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;CACF;AA9PD,wCA8PC"}