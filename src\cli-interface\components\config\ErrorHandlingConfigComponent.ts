import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
import { ErrorHandlingMiddleware } from '../../../core/error-handling/ErrorHandlingMiddleware';
import { createErrorHandlingMiddleware } from '../../../core/error-handling';
import { RetryPolicyConfigMenu } from './RetryPolicyConfigMenu';
import { CircuitBreakerConfigMenu } from './CircuitBreakerConfigMenu';
import { ErrorHandlingMetricsViewer } from './ErrorHandlingMetricsViewer';
import { ConfigurationPresetManager } from './ConfigurationPresetManager';

export class ErrorHandlingConfigComponent extends BaseComponent {
  private configManager: ConfigManager;
  private errorHandler: ErrorHandlingMiddleware;
  private retryPolicyMenu: RetryPolicyConfigMenu;
  private circuitBreakerMenu: CircuitBreakerConfigMenu;
  private metricsViewer: ErrorHandlingMetricsViewer;
  private presetManager: ConfigurationPresetManager;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager, errorHandler: ErrorHandlingMiddleware) {
    super(state, config);
    this.configManager = configManager;
    this.errorHandler = errorHandler;
    
    // Initialize sub-components
    this.retryPolicyMenu = new RetryPolicyConfigMenu(state, config, configManager);
    this.circuitBreakerMenu = new CircuitBreakerConfigMenu(state, config, configManager);
    this.metricsViewer = new ErrorHandlingMetricsViewer(state, config, errorHandler);
    this.presetManager = new ConfigurationPresetManager(state, config, configManager);
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🛡️ Error Handling Configuration', 'Manage retry policies, circuit breakers, and error handling settings');

    await this.showMainMenu();
  }

  private async showMainMenu(): Promise<void> {
    // Get current configuration status
    const appConfig = this.configManager.getConfig();
    const errorConfig = appConfig.errorHandling;
    
    const choices = [
      { 
        name: `🔄 Retry Policies ${errorConfig?.retry?.maxAttempts ? `(Max: ${errorConfig.retry.maxAttempts})` : '(Default)'}`, 
        value: 'retry' 
      },
      { 
        name: `⚡ Circuit Breakers ${errorConfig?.circuitBreaker?.enabled ? '(Enabled)' : '(Disabled)'}`, 
        value: 'circuit' 
      },
      { 
        name: '📊 View Metrics & Status', 
        value: 'metrics' 
      },
      { 
        name: '🎛️ Global Settings', 
        value: 'global' 
      },
      { 
        name: '📋 Configuration Presets', 
        value: 'presets' 
      },
      { 
        name: '🔧 Advanced Settings', 
        value: 'advanced' 
      },
      { 
        name: '🔄 Reset to Defaults', 
        value: 'reset' 
      },
      { 
        name: '🏠 Back to Main Config', 
        value: 'back' 
      },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices,
      },
    ]);

    switch (action) {
      case 'retry':
        await this.retryPolicyMenu.render();
        break;
      case 'circuit':
        await this.circuitBreakerMenu.render();
        break;
      case 'metrics':
        await this.metricsViewer.render();
        break;
      case 'global':
        await this.configureGlobalSettings();
        break;
      case 'presets':
        await this.presetManager.render();
        break;
      case 'advanced':
        await this.configureAdvancedSettings();
        break;
      case 'reset':
        await this.resetToDefaults();
        break;
      case 'back':
        this.updateState({ currentView: 'config' });
        return;
    }

    // Show menu again unless we're navigating away
    await this.showMainMenu();
  }

  private async configureGlobalSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🌐 Global Error Handling Settings');

    const currentConfig = this.configManager.getConfig().errorHandling || {};

    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'enableErrorLogging',
        message: 'Enable detailed error logging?',
        default: currentConfig.enableErrorLogging ?? true,
      },
      {
        type: 'confirm',
        name: 'enableMetrics',
        message: 'Enable metrics collection?',
        default: currentConfig.enableMetrics ?? true,
      },
      {
        type: 'confirm',
        name: 'showRetryProgress',
        message: 'Show retry progress to users?',
        default: currentConfig.showRetryProgress ?? true,
      },
      {
        type: 'confirm',
        name: 'allowRetryCancel',
        message: 'Allow users to cancel retry operations?',
        default: currentConfig.allowRetryCancel ?? true,
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig(answers);
      this.utils.showSuccess('Global settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update settings: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureAdvancedSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🔧 Advanced Error Handling Settings');

    const currentConfig = this.configManager.getConfig().errorHandling || {};

    const choices = [
      { name: '🎯 Operation-Specific Policies', value: 'operation_policies' },
      { name: '🏷️ Error Classification Rules', value: 'error_classification' },
      { name: '📝 Logging Configuration', value: 'logging' },
      { name: '📈 Metrics Configuration', value: 'metrics_config' },
      { name: '🔙 Back to Main Menu', value: 'back' },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Which advanced setting would you like to configure?',
        choices,
      },
    ]);

    switch (action) {
      case 'operation_policies':
        await this.configureOperationPolicies();
        break;
      case 'error_classification':
        await this.configureErrorClassification();
        break;
      case 'logging':
        await this.configureLogging();
        break;
      case 'metrics_config':
        await this.configureMetrics();
        break;
      case 'back':
        return;
    }

    await this.configureAdvancedSettings();
  }

  private async configureOperationPolicies(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🎯 Operation-Specific Retry Policies');

    const currentConfig = this.configManager.getConfig().errorHandling?.operationPolicies || {};

    this.utils.showInfo('Configure different retry policies for different types of operations:');
    console.log();

    const operations = ['toolExecution', 'providerCalls', 'fileOperations', 'networkRequests'];
    const updatedPolicies: any = {};

    for (const operation of operations) {
      const current = currentConfig[operation] || {};
      
      console.log(this.utils.colorize(`\n📋 ${operation.replace(/([A-Z])/g, ' $1').toLowerCase()}:`, this.config.theme.primary));
      
      const answers = await inquirer.prompt([
        {
          type: 'number',
          name: 'maxAttempts',
          message: 'Maximum retry attempts:',
          default: current.maxAttempts || 3,
          validate: (value) => value > 0 && value <= 10 || 'Must be between 1 and 10',
        },
        {
          type: 'number',
          name: 'baseDelay',
          message: 'Base delay (ms):',
          default: current.baseDelay || 1000,
          validate: (value) => value >= 100 || 'Must be at least 100ms',
        },
        {
          type: 'number',
          name: 'backoffMultiplier',
          message: 'Backoff multiplier:',
          default: current.backoffMultiplier || 2,
          validate: (value) => value >= 1 || 'Must be at least 1',
        },
      ]);

      updatedPolicies[operation] = answers;
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        operationPolicies: updatedPolicies
      });
      this.utils.showSuccess('Operation policies updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update policies: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureErrorClassification(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🏷️ Error Classification Rules');
    
    this.utils.showInfo('Error classification rules determine which errors should be retried.');
    this.utils.showWarning('This is an advanced feature. Incorrect configuration may affect system reliability.');
    
    const shouldContinue = await this.confirmAction('Do you want to continue with advanced error classification configuration?');
    
    if (!shouldContinue) {
      return;
    }

    // Show current classification rules
    this.utils.showInfo('\nCurrent error classification rules:');
    console.log('• Network errors: Retryable');
    console.log('• Timeout errors: Retryable');
    console.log('• Rate limit errors: Retryable');
    console.log('• Server errors (5xx): Retryable');
    console.log('• Authentication errors: Not retryable');
    console.log('• Validation errors: Not retryable');
    console.log('• Not found errors: Not retryable');
    
    this.utils.showInfo('\nFor custom error classification rules, please edit the configuration file directly.');
    await this.utils.waitForKeyPress();
  }

  private async configureLogging(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📝 Logging Configuration');

    const currentConfig = this.configManager.getConfig().errorHandling || {};

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'logLevel',
        message: 'Error logging level:',
        choices: [
          { name: 'DEBUG - All error details', value: 'DEBUG' },
          { name: 'INFO - General error information', value: 'INFO' },
          { name: 'WARN - Warnings and errors only', value: 'WARN' },
          { name: 'ERROR - Errors only', value: 'ERROR' },
        ],
        default: 'INFO',
      },
      {
        type: 'confirm',
        name: 'includeStackTrace',
        message: 'Include stack traces in error logs?',
        default: true,
      },
      {
        type: 'confirm',
        name: 'logRetryAttempts',
        message: 'Log individual retry attempts?',
        default: true,
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        enableErrorLogging: true // Just enable error logging for now
      });
      this.utils.showSuccess('Logging configuration updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update logging config: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureMetrics(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📈 Metrics Configuration');

    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'collectRetryMetrics',
        message: 'Collect retry attempt metrics?',
        default: true,
      },
      {
        type: 'confirm',
        name: 'collectCircuitBreakerMetrics',
        message: 'Collect circuit breaker metrics?',
        default: true,
      },
      {
        type: 'confirm',
        name: 'collectPerformanceMetrics',
        message: 'Collect performance metrics?',
        default: true,
      },
      {
        type: 'number',
        name: 'metricsRetentionHours',
        message: 'Metrics retention period (hours):',
        default: 24,
        validate: (value) => value > 0 || 'Must be greater than 0',
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        enableMetrics: true // Just enable metrics for now
      });
      this.utils.showSuccess('Metrics configuration updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update metrics config: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async resetToDefaults(): Promise<void> {
    const confirmed = await this.confirmAction(
      'This will reset ALL error handling settings to defaults. Are you sure?'
    );

    if (!confirmed) {
      return;
    }

    try {
      await this.configManager.resetErrorHandlingConfig();

      // Update the error handler with the new configuration
      const newConfig = this.configManager.getErrorHandlingConfig();
      this.errorHandler.updateConfig(newConfig);

      this.utils.showSuccess('Error handling configuration reset to defaults!');
    } catch (error) {
      this.utils.showError(`Failed to reset configuration: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }
}
