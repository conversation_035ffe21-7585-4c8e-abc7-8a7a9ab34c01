import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';
export declare class DeepseekProvider extends BaseProvider {
    name: string;
    private client;
    private readonly MAX_CONTEXT_TOKENS;
    private readonly COMPLETION_TOKENS_BUFFER;
    constructor(config: ProviderConfig);
    sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    streamMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    private parseSSEStream;
    private processResponse;
    private formatToolCalls;
    protected formatMessages(messages: ChatMessage[]): any[];
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    protected validateConfig(): void;
    protected handleError(error: any, context: string): Error;
    /**
     * Estimates token count for a message (rough approximation)
     * This is a simple heuristic since we don't have access to the exact tokenizer
     */
    private estimateTokenCount;
    /**
     * Estimates token count for tools
     */
    private estimateToolsTokenCount;
    /**
     * Truncates messages to fit within the context window
     * Keeps system prompt, most recent messages, and ensures we don't exceed token limits
     */
    private truncateMessagesForContext;
}
//# sourceMappingURL=DeepseekProvider.d.ts.map