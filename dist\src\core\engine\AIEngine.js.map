{"version": 3, "file": "AIEngine.js", "sourceRoot": "", "sources": ["../../../../src/core/engine/AIEngine.ts"], "names": [], "mappings": ";;;AACA,kEAA+D;AAE/D,2EAAwE;AAExE,wCAAkD;AA4BlD,MAAa,QAAQ;IACX,aAAa,CAAgB;IAC7B,YAAY,CAAe;IAC3B,mBAAmB,CAAsB;IACzC,eAAe,CAAkB;IACjC,oBAAoB,CAAuB;IAC3C,uBAAuB,CAA2B;IAE1D,YACE,aAA4B,EAC5B,YAA0B,EAC1B,uBAAiD;QAEjD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC;QAC1F,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,oBAAoB,GAAG,IAAI,8BAAoB,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC;QACxE,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;IACzD,CAAC;IAEM,0BAA0B,CAAC,OAAgC;QAChE,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;QACvC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,QAAuB,EACvB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,wDAAwD,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,oCAAoC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,mCAAmC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE7F,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACxF,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YAEhG,OAAO,CAAC,GAAG,CAAC,qBAAqB,wBAAwB,CAAC,MAAM,gBAAgB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAEjG,sCAAsC;YACtC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,wBAAwB,EAAE,cAAc,EAAE;gBACpF,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,oCAAoC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;YACnF,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YACzE,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;YAEtF,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACzI,CAAC;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAAqD,EAAE,CAAC;YACzE,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,CAAC,SAAS,CAAC,MAAM,aAAa,CAAC,CAAC;gBAC5E,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBAC1C,OAAO,CAAC,GAAG,CAAC,kCAAkC,QAAQ,CAAC,IAAI,eAAe,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACjG,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBACpD,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,CAAC,IAAI,YAAY,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACrG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YAC3D,CAAC;YAEC,qEAAqE;YACrE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,yCAAyC,WAAW,CAAC,MAAM,eAAe,CAAC,CAAC;gBACxF,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAC1D,OAAO,CAAC,GAAG,CAAC,qBAAqB,YAAY,CAAC,MAAM,gBAAgB,CAAC,CAAC;gBAEtE,MAAM,aAAa,GAAG,CAAC,GAAG,wBAAwB,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,CAAC;gBAC/E,OAAO,CAAC,GAAG,CAAC,qBAAqB,aAAa,CAAC,MAAM,yCAAyC,CAAC,CAAC;gBAEhG,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE;oBAClE,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,0CAA0C,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvE,OAAO,CAAC,GAAG,CAAC,4CAA4C,aAAa,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;gBAE9F,wCAAwC;gBACxC,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACxE,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;oBACvE,OAAO,CAAC,IAAI,CAAC,iCAAiC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,OAAO,CAAC,IAAI,CAAC,2BAA2B,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjI,OAAO,CAAC,IAAI,CAAC,kCAAkC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;oBACtE,OAAO,CAAC,IAAI,CAAC,mCAAmC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC1E,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,aAAa;oBACtB,SAAS,EAAE,WAAW;oBACtB,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,KAAK;oBACpC,YAAY,EAAE,aAAa,CAAC,QAAQ,EAAE,YAAY;iBACnD,CAAC;YACJ,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACnE,OAAO;gBACL,OAAO,EAAE,QAAQ;gBACjB,SAAS,EAAE,WAAW;gBACtB,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK;gBAC/B,YAAY,EAAE,QAAQ,CAAC,QAAQ,EAAE,YAAY;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,CAAC,aAAa,CACzB,QAAuB,EACvB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC;YACH,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE;gBACjE,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,YAAyB,CAAC;YAE9B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;gBACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC9B,WAAW,IAAI,KAAK,CAAC;oBACrB,MAAM,KAAK,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,YAAY,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAAqD,EAAE,CAAC;YACzE,IAAI,YAAa,CAAC,SAAS,IAAI,YAAa,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,KAAK,MAAM,QAAQ,IAAI,YAAa,CAAC,SAAS,EAAE,CAAC;oBAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBACpD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBAED,qEAAqE;gBACrE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;oBAC1D,MAAM,aAAa,GAAG,CAAC,GAAG,QAAQ,EAAE,YAAa,EAAE,GAAG,YAAY,CAAC,CAAC;oBAEpE,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE;wBAClE,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;oBAEH,wCAAwC;oBACxC,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACxE,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;wBAChF,OAAO,CAAC,IAAI,CAAC,uBAAuB,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;wBAC1D,OAAO,CAAC,IAAI,CAAC,iBAAiB,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACzH,CAAC;oBAED,OAAO;wBACL,OAAO,EAAE,aAAa;wBACtB,SAAS,EAAE,WAAW;wBACtB,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,KAAK;wBACpC,YAAY,EAAE,aAAa,CAAC,QAAQ,EAAE,YAAY;qBACnD,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,YAAa;gBACtB,SAAS,EAAE,WAAW;gBACtB,KAAK,EAAE,YAAa,CAAC,QAAQ,EAAE,KAAK;gBACpC,YAAY,EAAE,YAAa,CAAC,QAAQ,EAAE,YAAY;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,YAAqB;QACvC,MAAM,IAAI,GAAG,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACnE,OAAO,iCAAe,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;IAEO,iBAAiB,CAAC,cAAsB;QAC9C,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,sCAAsC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,8BAA8B,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEvF,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAa;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAC7D,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,UAAU,EACnB;gBACE,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE;gBAClE,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,CAAC;aACX,CACF,CAAC;YAEF,mEAAmE;YACnE,OAAO;gBACL,GAAG,MAAM;gBACT,UAAU,EAAE,QAAQ,CAAC,EAAE;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,0BAA0B,KAAK,EAAE;gBACxC,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,WAA6D;QACtF,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,MAAe;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,uBAAuB;YAClF,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE;gBACR,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,iDAAiD;gBAChF,UAAU,EAAE,MAAM;aACnB;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,kBAAkB;IACX,KAAK,CAAC,YAAY,CAAC,YAAqB;QAC7C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,YAAqB;QACnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IACzC,CAAC;IAEM,kBAAkB,CAAC,QAAgB;QACxC,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAEM,aAAa,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEM,UAAU,CAAC,QAAgB;QAChC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEM,WAAW,CAAC,QAAgB;QACjC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,mBAA4B;QACjE,yFAAyF;QACzF,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YACjE,OAAO,YAAY,EAAE,OAAO,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAuB,EAAE,YAAqB;QAC7E,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,4CAA4C;QAC5C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;QAE9E,IAAI,gBAAgB,EAAE,CAAC;YACrB,kCAAkC;YAClC,OAAO;gBACL;oBACE,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC1B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,YAAY;oBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;aACrB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,sCAAsC;YACtC,OAAO;gBACL;oBACE,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;oBAC1B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,YAAY;oBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,GAAG,QAAQ;aACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,uBAAuB;QAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;CACF;AA7WD,4BA6WC"}