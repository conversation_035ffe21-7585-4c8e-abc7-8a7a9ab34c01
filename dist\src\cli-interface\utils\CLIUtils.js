"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIUtils = void 0;
const chalk_1 = __importDefault(require("chalk"));
const boxen_1 = __importDefault(require("boxen"));
const ora_1 = __importDefault(require("ora"));
const inquirer_1 = __importDefault(require("inquirer"));
const types_1 = require("../types");
class CLIUtils {
    static spinner = null;
    theme;
    constructor(theme = types_1.DEFAULT_CLI_THEME) {
        this.theme = theme;
    }
    showBanner(title, subtitle) {
        const content = this.colorize(title, this.theme.primary, true) +
            (subtitle ? '\n' + this.colorize(subtitle, this.theme.muted) : '');
        const banner = (0, boxen_1.default)(content, {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: this.theme.primary,
            textAlignment: 'center'
        });
        console.log(banner);
    }
    showSuccess(message) {
        console.log(this.colorize('✅ ' + message, this.theme.success));
    }
    showError(message) {
        console.log(this.colorize('❌ ' + message, this.theme.error));
    }
    showWarning(message) {
        console.log(this.colorize('⚠️  ' + message, this.theme.warning));
    }
    showInfo(message) {
        console.log(this.colorize('ℹ️  ' + message, this.theme.info));
    }
    showDebug(message) {
        console.log(this.colorize('🐛 ' + message, this.theme.muted));
    }
    colorize(text, color, bold = false) {
        let colorFunc = chalk_1.default[color];
        if (bold) {
            colorFunc = colorFunc.bold;
        }
        return colorFunc(text);
    }
    formatHeader(text) {
        return this.colorize(text, this.theme.primary, true);
    }
    startSpinner(text) {
        if (CLIUtils.spinner) {
            CLIUtils.spinner.stop();
        }
        CLIUtils.spinner = (0, ora_1.default)({
            text,
            color: this.theme.primary,
        }).start();
    }
    updateSpinner(text) {
        if (CLIUtils.spinner) {
            CLIUtils.spinner.text = text;
        }
    }
    stopSpinner(success = true, message) {
        if (CLIUtils.spinner) {
            if (success) {
                CLIUtils.spinner.succeed(message);
            }
            else {
                CLIUtils.spinner.fail(message);
            }
            CLIUtils.spinner = null;
        }
    }
    async confirmAction(message, defaultValue = false) {
        const { confirmed } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirmed',
                message,
                default: defaultValue,
            },
        ]);
        return confirmed;
    }
    async selectFromList(message, choices, defaultValue) {
        const { selected } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'selected',
                message,
                choices,
                default: defaultValue,
            },
        ]);
        return selected;
    }
    async getInput(message, defaultValue, validate) {
        const { input } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'input',
                message,
                default: defaultValue,
                validate,
            },
        ]);
        return input;
    }
    async getPassword(message) {
        const { password } = await inquirer_1.default.prompt([
            {
                type: 'password',
                name: 'password',
                message,
                validate: (input) => input.trim().length > 0 || 'Password is required',
            },
        ]);
        return password;
    }
    formatCodeBlock(code, language = '') {
        const lines = code.split('\n');
        const formattedLines = lines.map((line, index) => {
            const lineNumber = (index + 1).toString().padStart(3, ' ');
            return this.colorize(`${lineNumber} │ `, this.theme.muted) + line;
        });
        return (0, boxen_1.default)(this.colorize(`${language}\n`, this.theme.accent) + formattedLines.join('\n'), {
            padding: 1,
            borderStyle: 'round',
            borderColor: this.theme.muted,
        });
    }
    formatDiff(oldContent, newContent) {
        const oldLines = oldContent.split('\n');
        const newLines = newContent.split('\n');
        const maxLines = Math.max(oldLines.length, newLines.length);
        const diffLines = [];
        for (let i = 0; i < maxLines; i++) {
            const oldLine = oldLines[i] || '';
            const newLine = newLines[i] || '';
            if (oldLine !== newLine) {
                if (oldLine) {
                    diffLines.push(this.colorize(`- ${oldLine}`, this.theme.error));
                }
                if (newLine) {
                    diffLines.push(this.colorize(`+ ${newLine}`, this.theme.success));
                }
            }
            else if (oldLine) {
                diffLines.push(this.colorize(`  ${oldLine}`, this.theme.muted));
            }
        }
        return (0, boxen_1.default)(this.colorize('Diff Preview:\n', this.theme.warning, true) + diffLines.join('\n'), {
            padding: 1,
            borderStyle: 'round',
            borderColor: this.theme.warning,
        });
    }
    async waitForKeyPress(message = 'Press Enter to continue...') {
        await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'continue',
                message,
            },
        ]);
    }
    clearScreen() {
        console.clear();
    }
    printSeparator(char = '─', length = 50) {
        console.log(this.colorize(char.repeat(length), this.theme.muted));
    }
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    formatDuration(ms) {
        if (ms < 1000)
            return `${ms}ms`;
        if (ms < 60000)
            return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    }
    truncateText(text, maxLength) {
        if (text.length <= maxLength)
            return text;
        return text.substring(0, maxLength - 3) + '...';
    }
}
exports.CLIUtils = CLIUtils;
//# sourceMappingURL=CLIUtils.js.map