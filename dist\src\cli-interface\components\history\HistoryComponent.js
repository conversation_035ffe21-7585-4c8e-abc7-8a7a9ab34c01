"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryComponent = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
const engine_1 = require("../../../core/engine");
class HistoryComponent extends BaseComponent_1.BaseComponent {
    configManager;
    conversationManager;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
        this.conversationManager = new engine_1.ConversationManager(configManager);
    }
    async render() {
        this.utils.clearScreen();
        this.utils.showBanner('📚 Conversation History', 'Browse and manage your conversations');
        await this.showMainMenu();
    }
    async showMainMenu() {
        const choices = [
            { name: '📋 Browse Conversations', value: 'browse' },
            { name: '🔍 Search Conversations', value: 'search' },
            { name: '📊 View Statistics', value: 'stats' },
            { name: '📤 Export Conversations', value: 'export' },
            { name: '🗑️ Delete Conversations', value: 'delete' },
            { name: '🏠 Back to Terminal', value: 'back' },
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'What would you like to do?',
                choices,
            },
        ]);
        switch (action) {
            case 'browse':
                await this.browseConversations();
                break;
            case 'search':
                await this.searchConversations();
                break;
            case 'stats':
                await this.viewStatistics();
                break;
            case 'export':
                await this.exportConversations();
                break;
            case 'delete':
                await this.deleteConversations();
                break;
            case 'back':
                if (this.state.isAuthenticated) {
                    this.updateState({ currentView: 'terminal' });
                }
                else {
                    this.updateState({ currentView: 'auth' });
                }
                return;
        }
        // Show menu again unless we're navigating away
        await this.showMainMenu();
    }
    async browseConversations() {
        this.utils.clearScreen();
        this.utils.showBanner('📋 Browse Conversations');
        try {
            const conversations = await this.conversationManager.listConversations();
            if (conversations.length === 0) {
                this.utils.showInfo('No conversations found.');
                await this.utils.waitForKeyPress();
                return;
            }
            // Sort by most recent first
            conversations.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
            const choices = conversations.map(conv => ({
                name: this.formatConversationSummary(conv),
                value: conv.id,
            }));
            choices.push({ name: '🔙 Back', value: 'back' });
            const { conversationId } = await inquirer_1.default.prompt([
                {
                    type: 'list',
                    name: 'conversationId',
                    message: 'Select a conversation:',
                    choices,
                    pageSize: 10,
                },
            ]);
            if (conversationId === 'back') {
                return;
            }
            await this.viewConversation(conversationId);
        }
        catch (error) {
            this.utils.showError(`Failed to load conversations: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async viewConversation(conversationId) {
        try {
            const conversation = await this.conversationManager.getConversation(conversationId);
            if (!conversation) {
                this.utils.showError('Conversation not found.');
                await this.utils.waitForKeyPress();
                return;
            }
            this.utils.clearScreen();
            this.utils.showBanner(`💬 ${conversation.title}`, `${conversation.provider} • ${conversation.model} • ${conversation.messages.length} messages`);
            // Show conversation actions
            const choices = [
                { name: '👁️ View Messages', value: 'view' },
                { name: '▶️ Continue Conversation', value: 'continue' },
                { name: '📤 Export Conversation', value: 'export' },
                { name: '✏️ Rename Conversation', value: 'rename' },
                { name: '🗑️ Delete Conversation', value: 'delete' },
                { name: '🔙 Back', value: 'back' },
            ];
            const { action } = await inquirer_1.default.prompt([
                {
                    type: 'list',
                    name: 'action',
                    message: 'What would you like to do?',
                    choices,
                },
            ]);
            switch (action) {
                case 'view':
                    await this.viewMessages(conversation);
                    break;
                case 'continue':
                    await this.continueConversation(conversationId);
                    break;
                case 'export':
                    await this.exportSingleConversation(conversationId);
                    break;
                case 'rename':
                    await this.renameConversation(conversationId);
                    break;
                case 'delete':
                    await this.deleteSingleConversation(conversationId);
                    break;
                case 'back':
                    return;
            }
        }
        catch (error) {
            this.utils.showError(`Failed to load conversation: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async viewMessages(conversation) {
        this.utils.clearScreen();
        this.utils.showBanner(`💬 ${conversation.title} - Messages`);
        console.log();
        for (const message of conversation.messages) {
            const timestamp = this.config.terminal.showTimestamps
                ? this.utils.colorize(`[${message.timestamp.toLocaleString()}]`, this.config.theme.muted) + ' '
                : '';
            const role = message.role === 'user' ? '👤 You' : '🤖 AI';
            const roleColor = message.role === 'user' ? this.config.theme.primary : this.config.theme.secondary;
            console.log(timestamp + this.utils.colorize(role, roleColor, true));
            console.log(message.content);
            console.log();
        }
        await this.utils.waitForKeyPress();
    }
    async continueConversation(conversationId) {
        this.updateState({
            currentView: 'terminal',
            conversationId: conversationId
        });
    }
    async searchConversations() {
        this.utils.clearScreen();
        this.utils.showBanner('🔍 Search Conversations');
        const { searchTerm } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'searchTerm',
                message: 'Enter search term:',
                validate: (input) => input.length > 0 || 'Search term is required',
            },
        ]);
        try {
            const results = await this.conversationManager.searchConversations(searchTerm);
            if (results.length === 0) {
                this.utils.showInfo(`No conversations found matching "${searchTerm}".`);
                await this.utils.waitForKeyPress();
                return;
            }
            this.utils.showSuccess(`Found ${results.length} conversation(s) matching "${searchTerm}"`);
            console.log();
            for (const conv of results) {
                console.log(this.formatConversationSummary(conv));
            }
            await this.utils.waitForKeyPress();
        }
        catch (error) {
            this.utils.showError(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async viewStatistics() {
        this.utils.clearScreen();
        this.utils.showBanner('📊 Conversation Statistics');
        try {
            const conversations = await this.conversationManager.listConversations();
            if (conversations.length === 0) {
                this.utils.showInfo('No conversations to analyze.');
                await this.utils.waitForKeyPress();
                return;
            }
            // Calculate statistics
            const totalConversations = conversations.length;
            const totalMessages = conversations.reduce((sum, conv) => sum + conv.messageCount, 0);
            const providerStats = conversations.reduce((stats, conv) => {
                stats[conv.provider] = (stats[conv.provider] || 0) + 1;
                return stats;
            }, {});
            const modelStats = conversations.reduce((stats, conv) => {
                stats[conv.model] = (stats[conv.model] || 0) + 1;
                return stats;
            }, {});
            const oldestConversation = conversations.reduce((oldest, conv) => conv.createdAt < oldest.createdAt ? conv : oldest);
            const newestConversation = conversations.reduce((newest, conv) => conv.createdAt > newest.createdAt ? conv : newest);
            // Display statistics
            console.log();
            console.log(this.utils.colorize('📈 General Statistics', this.config.theme.primary, true));
            console.log(`Total Conversations: ${totalConversations}`);
            console.log(`Total Messages: ${totalMessages}`);
            console.log(`Average Messages per Conversation: ${(totalMessages / totalConversations).toFixed(1)}`);
            console.log();
            console.log(this.utils.colorize('🤖 Provider Usage', this.config.theme.secondary, true));
            for (const [provider, count] of Object.entries(providerStats)) {
                const percentage = ((count / totalConversations) * 100).toFixed(1);
                console.log(`${provider}: ${count} (${percentage}%)`);
            }
            console.log();
            console.log(this.utils.colorize('🧠 Model Usage', this.config.theme.info, true));
            for (const [model, count] of Object.entries(modelStats)) {
                const percentage = ((count / totalConversations) * 100).toFixed(1);
                console.log(`${model}: ${count} (${percentage}%)`);
            }
            console.log();
            console.log(this.utils.colorize('📅 Timeline', this.config.theme.muted, true));
            console.log(`Oldest Conversation: ${oldestConversation.createdAt.toLocaleDateString()}`);
            console.log(`Newest Conversation: ${newestConversation.createdAt.toLocaleDateString()}`);
            await this.utils.waitForKeyPress();
        }
        catch (error) {
            this.utils.showError(`Failed to calculate statistics: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async exportConversations() {
        this.utils.clearScreen();
        this.utils.showBanner('📤 Export Conversations');
        const choices = [
            { name: '📋 Export All Conversations', value: 'all' },
            { name: '🎯 Export Specific Conversation', value: 'specific' },
            { name: '📅 Export by Date Range', value: 'date_range' },
            { name: '🔙 Back', value: 'back' },
        ];
        const { exportType } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'exportType',
                message: 'What would you like to export?',
                choices,
            },
        ]);
        if (exportType === 'back') {
            return;
        }
        try {
            let conversations = [];
            switch (exportType) {
                case 'all':
                    conversations = await this.conversationManager.listConversations();
                    break;
                case 'specific':
                    const { conversationId } = await this.selectConversationForExport();
                    if (conversationId) {
                        const conv = await this.conversationManager.getConversation(conversationId);
                        if (conv)
                            conversations = [conv];
                    }
                    break;
                case 'date_range':
                    // For simplicity, just export all for now
                    conversations = await this.conversationManager.listConversations();
                    break;
            }
            if (conversations.length === 0) {
                this.utils.showWarning('No conversations to export.');
                await this.utils.waitForKeyPress();
                return;
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `conversations-export-${timestamp}.json`;
            console.log('\nExport data:');
            console.log(JSON.stringify(conversations, null, 2));
            console.log(`\nSave this to: ${filename}`);
            this.utils.showSuccess(`${conversations.length} conversation(s) exported successfully!`);
            await this.utils.waitForKeyPress();
        }
        catch (error) {
            this.utils.showError(`Export failed: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async selectConversationForExport() {
        const conversations = await this.conversationManager.listConversations();
        if (conversations.length === 0) {
            return { conversationId: null };
        }
        const choices = conversations.map(conv => ({
            name: this.formatConversationSummary(conv),
            value: conv.id,
        }));
        return await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'conversationId',
                message: 'Select conversation to export:',
                choices,
            },
        ]);
    }
    async deleteConversations() {
        this.utils.clearScreen();
        this.utils.showBanner('🗑️ Delete Conversations');
        const choices = [
            { name: '🎯 Delete Specific Conversation', value: 'specific' },
            { name: '📅 Delete by Date Range', value: 'date_range' },
            { name: '🧹 Delete All Conversations', value: 'all' },
            { name: '🔙 Back', value: 'back' },
        ];
        const { deleteType } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'deleteType',
                message: 'What would you like to delete?',
                choices,
            },
        ]);
        if (deleteType === 'back') {
            return;
        }
        const confirmed = await this.utils.confirmAction('Are you sure? This action cannot be undone.');
        if (!confirmed) {
            return;
        }
        try {
            switch (deleteType) {
                case 'specific':
                    await this.deleteSpecificConversation();
                    break;
                case 'date_range':
                    this.utils.showWarning('Date range deletion not implemented yet.');
                    break;
                case 'all':
                    await this.deleteAllConversations();
                    break;
            }
        }
        catch (error) {
            this.utils.showError(`Delete failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async deleteSpecificConversation() {
        const conversations = await this.conversationManager.listConversations();
        if (conversations.length === 0) {
            this.utils.showWarning('No conversations to delete.');
            return;
        }
        const choices = conversations.map(conv => ({
            name: this.formatConversationSummary(conv),
            value: conv.id,
        }));
        const { conversationId } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'conversationId',
                message: 'Select conversation to delete:',
                choices,
            },
        ]);
        await this.conversationManager.deleteConversation(conversationId);
        this.utils.showSuccess('Conversation deleted successfully!');
    }
    async deleteAllConversations() {
        const conversations = await this.conversationManager.listConversations();
        for (const conv of conversations) {
            await this.conversationManager.deleteConversation(conv.id);
        }
        this.utils.showSuccess(`${conversations.length} conversation(s) deleted successfully!`);
    }
    async exportSingleConversation(conversationId) {
        try {
            const conversation = await this.conversationManager.getConversation(conversationId);
            if (!conversation) {
                this.utils.showError('Conversation not found.');
                return;
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `conversation-${conversationId}-${timestamp}.json`;
            console.log('\nConversation export:');
            console.log(JSON.stringify(conversation, null, 2));
            console.log(`\nSave this to: ${filename}`);
            this.utils.showSuccess('Conversation exported successfully!');
        }
        catch (error) {
            this.utils.showError(`Export failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async renameConversation(conversationId) {
        const { newTitle } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'newTitle',
                message: 'Enter new conversation title:',
                validate: (input) => input.length > 0 || 'Title is required',
            },
        ]);
        try {
            await this.conversationManager.updateConversation(conversationId, { title: newTitle });
            this.utils.showSuccess('Conversation renamed successfully!');
        }
        catch (error) {
            this.utils.showError(`Rename failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async deleteSingleConversation(conversationId) {
        const confirmed = await this.utils.confirmAction('Are you sure you want to delete this conversation? This cannot be undone.');
        if (confirmed) {
            try {
                await this.conversationManager.deleteConversation(conversationId);
                this.utils.showSuccess('Conversation deleted successfully!');
            }
            catch (error) {
                this.utils.showError(`Delete failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        await this.utils.waitForKeyPress();
    }
    formatConversationSummary(conv) {
        const date = conv.updatedAt.toLocaleDateString();
        const time = conv.updatedAt.toLocaleTimeString();
        return `${conv.title} | ${conv.provider}/${conv.model} | ${conv.messageCount} msgs | ${date} ${time}`;
    }
}
exports.HistoryComponent = HistoryComponent;
//# sourceMappingURL=HistoryComponent.js.map