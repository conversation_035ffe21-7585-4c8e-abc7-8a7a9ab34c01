import { CircuitBreaker } from '../../../src/core/error-handling/CircuitBreaker';
import { CircuitBreakerConfiguration } from '../../../src/core/types';
import { TestError, NetworkError, RateLimitError } from '../../utils/mocks';

describe('CircuitBreaker', () => {
  let defaultConfig: CircuitBreakerConfiguration;

  beforeEach(() => {
    defaultConfig = {
      enabled: true,
      failureThreshold: 5,
      recoveryTimeout: 30000,
      successThreshold: 2,
      monitoringWindow: 60000,
      halfOpenTimeout: 10000,
      failureRateThreshold: 50,
      minimumRequests: 10,
      enablePerServiceBreakers: true,
      enableGlobalBreaker: false
    };
  });

  describe('constructor', () => {
    it('should create CircuitBreaker with default configuration', () => {
      const breaker = new CircuitBreaker('test-service', defaultConfig);
      expect(breaker).toBeInstanceOf(CircuitBreaker);
      expect(breaker.getState()).toBe('CLOSED');
    });

    it('should validate configuration parameters', () => {
      expect(() => new CircuitBreaker('test', { ...defaultConfig, failureThreshold: 0 }))
        .toThrow('failureThreshold must be greater than 0');
      
      expect(() => new CircuitBreaker('test', { ...defaultConfig, recoveryTimeout: -1 }))
        .toThrow('recoveryTimeout must be non-negative');
      
      expect(() => new CircuitBreaker('test', { ...defaultConfig, successThreshold: 0 }))
        .toThrow('successThreshold must be greater than 0');
    });

    it('should start in OPEN state when disabled', () => {
      const breaker = new CircuitBreaker('test', { ...defaultConfig, enabled: false });
      expect(breaker.getState()).toBe('CLOSED'); // Should still be closed but not enforce
    });
  });

  describe('state transitions', () => {
    let breaker: CircuitBreaker;

    beforeEach(() => {
      breaker = new CircuitBreaker('test-service', defaultConfig);
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    describe('CLOSED to OPEN', () => {
      it('should open when failure threshold is reached', () => {
        // Record failures to reach threshold
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        expect(breaker.getState()).toBe('OPEN');
      });

      it('should open when failure rate threshold is exceeded', () => {
        // Record enough requests to meet minimum
        for (let i = 0; i < 8; i++) {
          breaker.recordSuccess();
        }
        
        // Record failures to exceed failure rate (6 failures out of 14 = 43%)
        for (let i = 0; i < 6; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        expect(breaker.getState()).toBe('OPEN');
      });

      it('should not open if minimum requests not met', () => {
        // Record only a few requests
        for (let i = 0; i < 3; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        expect(breaker.getState()).toBe('CLOSED');
      });
    });

    describe('OPEN to HALF_OPEN', () => {
      it('should transition to HALF_OPEN after recovery timeout', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        expect(breaker.getState()).toBe('OPEN');
        
        // Advance time past recovery timeout
        jest.advanceTimersByTime(30001);
        
        // Check state should trigger transition
        expect(breaker.getState()).toBe('HALF_OPEN');
      });

      it('should not transition before recovery timeout', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        expect(breaker.getState()).toBe('OPEN');
        
        // Advance time but not enough
        jest.advanceTimersByTime(15000);
        
        expect(breaker.getState()).toBe('OPEN');
      });
    });

    describe('HALF_OPEN to CLOSED', () => {
      it('should close when success threshold is met', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        // Transition to HALF_OPEN
        jest.advanceTimersByTime(30001);
        expect(breaker.getState()).toBe('HALF_OPEN');
        
        // Record successful attempts
        breaker.recordSuccess();
        breaker.recordSuccess();
        
        expect(breaker.getState()).toBe('CLOSED');
      });

      it('should reset failure count when closing', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        // Transition to HALF_OPEN and then CLOSED
        jest.advanceTimersByTime(30001);
        breaker.recordSuccess();
        breaker.recordSuccess();
        
        expect(breaker.getState()).toBe('CLOSED');
        
        // Should be able to handle failures again before opening
        breaker.recordFailure(new TestError('Test error'));
        expect(breaker.getState()).toBe('CLOSED');
      });
    });

    describe('HALF_OPEN to OPEN', () => {
      it('should reopen on failure in HALF_OPEN state', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        // Transition to HALF_OPEN
        jest.advanceTimersByTime(30001);
        expect(breaker.getState()).toBe('HALF_OPEN');
        
        // Record a failure
        breaker.recordFailure(new TestError('Test error'));
        
        expect(breaker.getState()).toBe('OPEN');
      });

      it('should timeout HALF_OPEN state', () => {
        // Open the circuit
        for (let i = 0; i < 5; i++) {
          breaker.recordFailure(new TestError('Test error'));
        }
        
        // Transition to HALF_OPEN
        jest.advanceTimersByTime(30001);
        expect(breaker.getState()).toBe('HALF_OPEN');
        
        // Advance time past half-open timeout
        jest.advanceTimersByTime(10001);
        
        expect(breaker.getState()).toBe('OPEN');
      });
    });
  });

  describe('canExecute', () => {
    let breaker: CircuitBreaker;

    beforeEach(() => {
      breaker = new CircuitBreaker('test-service', defaultConfig);
    });

    it('should allow execution when CLOSED', () => {
      expect(breaker.canExecute()).toBe(true);
    });

    it('should block execution when OPEN', () => {
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        breaker.recordFailure(new TestError('Test error'));
      }
      
      expect(breaker.canExecute()).toBe(false);
    });

    it('should allow limited execution when HALF_OPEN', () => {
      jest.useFakeTimers();
      
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        breaker.recordFailure(new TestError('Test error'));
      }
      
      // Transition to HALF_OPEN
      jest.advanceTimersByTime(30001);
      
      expect(breaker.canExecute()).toBe(true);
      
      jest.useRealTimers();
    });

    it('should allow execution when disabled', () => {
      const disabledBreaker = new CircuitBreaker('test', { ...defaultConfig, enabled: false });
      
      // Even after failures, should still allow execution
      for (let i = 0; i < 10; i++) {
        disabledBreaker.recordFailure(new TestError('Test error'));
      }
      
      expect(disabledBreaker.canExecute()).toBe(true);
    });
  });

  describe('metrics and monitoring', () => {
    let breaker: CircuitBreaker;

    beforeEach(() => {
      breaker = new CircuitBreaker('test-service', defaultConfig);
    });

    it('should track success and failure counts', () => {
      breaker.recordSuccess();
      breaker.recordSuccess();
      breaker.recordFailure(new TestError('Test error'));
      
      const metrics = breaker.getMetrics();
      expect(metrics.totalRequests).toBe(3);
      expect(metrics.successCount).toBe(2);
      expect(metrics.failureCount).toBe(1);
      expect(metrics.successRate).toBeCloseTo(66.67, 1);
      expect(metrics.failureRate).toBeCloseTo(33.33, 1);
    });

    it('should track state transitions', () => {
      const metrics = breaker.getMetrics();
      expect(metrics.stateTransitions).toBe(0);
      
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        breaker.recordFailure(new TestError('Test error'));
      }
      
      const metricsAfterOpen = breaker.getMetrics();
      expect(metricsAfterOpen.stateTransitions).toBe(1);
    });

    it('should track last failure information', () => {
      const error = new NetworkError('Connection failed');
      breaker.recordFailure(error);
      
      const metrics = breaker.getMetrics();
      expect(metrics.lastFailure).toBeDefined();
      expect(metrics.lastFailure?.message).toBe('Connection failed');
      expect(metrics.lastFailure?.timestamp).toBeDefined();
    });

    it('should calculate current failure rate', () => {
      // Record mixed results
      for (let i = 0; i < 7; i++) {
        breaker.recordSuccess();
      }
      for (let i = 0; i < 3; i++) {
        breaker.recordFailure(new TestError('Test error'));
      }
      
      const metrics = breaker.getMetrics();
      expect(metrics.failureRate).toBe(30); // 3 failures out of 10 requests
    });
  });

  describe('reset functionality', () => {
    let breaker: CircuitBreaker;

    beforeEach(() => {
      breaker = new CircuitBreaker('test-service', defaultConfig);
    });

    it('should reset to CLOSED state', () => {
      // Open the circuit
      for (let i = 0; i < 5; i++) {
        breaker.recordFailure(new TestError('Test error'));
      }
      expect(breaker.getState()).toBe('OPEN');
      
      // Reset
      breaker.reset();
      
      expect(breaker.getState()).toBe('CLOSED');
    });

    it('should clear all metrics on reset', () => {
      breaker.recordSuccess();
      breaker.recordFailure(new TestError('Test error'));
      
      breaker.reset();
      
      const metrics = breaker.getMetrics();
      expect(metrics.totalRequests).toBe(0);
      expect(metrics.successCount).toBe(0);
      expect(metrics.failureCount).toBe(0);
      expect(metrics.stateTransitions).toBe(0);
    });
  });

  describe('error classification', () => {
    let breaker: CircuitBreaker;

    beforeEach(() => {
      breaker = new CircuitBreaker('test-service', defaultConfig);
    });

    it('should count different error types', () => {
      breaker.recordFailure(new NetworkError());
      breaker.recordFailure(new RateLimitError());
      breaker.recordFailure(new TestError('Generic error'));
      
      const metrics = breaker.getMetrics();
      expect(metrics.failureCount).toBe(3);
      expect(metrics.errorTypes).toBeDefined();
    });
  });
});
