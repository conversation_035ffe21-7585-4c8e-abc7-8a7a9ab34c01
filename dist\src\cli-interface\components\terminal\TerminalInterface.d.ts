import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig } from '../../types';
export declare class TerminalInterface extends BaseComponent {
    private progressIndicator;
    constructor(state: CLIState, config: CLIConfig);
    render(): Promise<void>;
    private renderHeader;
    private renderStatusBar;
    renderPrompt(): string;
    renderTypingIndicator(): void;
    stopTypingIndicator(): void;
    renderError(error: string): void;
    renderWarning(warning: string): void;
    renderInfo(info: string): void;
    renderSeparator(): void;
    /**
     * Show retry progress to user
     */
    showRetryProgress(attempt: number, maxAttempts: number, delay: number, error: Error): void;
    /**
     * Show operation progress
     */
    showOperationProgress(message: string): void;
    /**
     * Update operation progress
     */
    updateOperationProgress(message: string): void;
    /**
     * Show operation success
     */
    showOperationSuccess(message?: string): void;
    /**
     * Show operation failure
     */
    showOperationFailure(message?: string): void;
    /**
     * Stop any ongoing progress indicators
     */
    stopProgress(): void;
    /**
     * Show circuit breaker status
     */
    showCircuitBreakerStatus(serviceName: string, status: 'open' | 'half-open' | 'closed'): void;
    /**
     * Show cancellation prompt
     */
    showCancellationPrompt(): Promise<boolean>;
    renderToolConfirmation(toolName: string, parameters: any): Promise<boolean>;
    renderToolResult(toolName: string, result: any, duration?: number): void;
    renderStreamingResponse(content: string): Promise<void>;
    renderMessageTimestamp(timestamp: Date): string;
    renderTokenCount(tokens?: number): string;
}
//# sourceMappingURL=TerminalInterface.d.ts.map