import { CLITheme } from '../types';
export declare class CLIUtils {
    private static spinner;
    private theme;
    constructor(theme?: CLITheme);
    showBanner(title: string, subtitle?: string): void;
    showSuccess(message: string): void;
    showError(message: string): void;
    showWarning(message: string): void;
    showInfo(message: string): void;
    showDebug(message: string): void;
    colorize(text: string, color: string, bold?: boolean): string;
    formatHeader(text: string): string;
    startSpinner(text: string): void;
    updateSpinner(text: string): void;
    stopSpinner(success?: boolean, message?: string): void;
    confirmAction(message: string, defaultValue?: boolean): Promise<boolean>;
    selectFromList<T>(message: string, choices: Array<{
        name: string;
        value: T;
    }>, defaultValue?: T): Promise<T>;
    getInput(message: string, defaultValue?: string, validate?: (input: string) => boolean | string): Promise<string>;
    getPassword(message: string): Promise<string>;
    formatCodeBlock(code: string, language?: string): string;
    formatDiff(oldContent: string, newContent: string): string;
    waitFor<PERSON>eyPress(message?: string): Promise<void>;
    clearScreen(): void;
    printSeparator(char?: string, length?: number): void;
    formatFileSize(bytes: number): string;
    formatDuration(ms: number): string;
    truncateText(text: string, maxLength: number): string;
}
//# sourceMappingURL=CLIUtils.d.ts.map