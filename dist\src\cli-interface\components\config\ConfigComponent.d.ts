import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class ConfigComponent extends BaseComponent {
    private configManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private showMainMenu;
    private manageProviders;
    private addProvider;
    private configureProvider;
    private removeProvider;
    private configureTheme;
    private configureTerminal;
    private exportConfiguration;
    private importConfiguration;
    private resetConfiguration;
}
//# sourceMappingURL=ConfigComponent.d.ts.map