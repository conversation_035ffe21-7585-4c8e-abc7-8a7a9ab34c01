{"version": 3, "file": "WebFetchTool.d.ts", "sourceRoot": "", "sources": ["../../../../../src/core/tools/web/WebFetchTool.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAClE,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC,MAAM,WAAW,cAAc;IAC7B,GAAG,EAAE,MAAM,CAAC;IACZ,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,EAAE,CAAC;CACrB;AAED,qBAAa,YAAa,SAAQ,QAAQ;IACjC,IAAI,SAAe;IACnB,WAAW,SAAoF;IAC/F,oBAAoB,UAAS;IAE7B,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAsDf;;IAYW,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAoEpE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;YAwDxD,QAAQ;IAqCtB,OAAO,CAAC,mBAAmB;IAgB3B,OAAO,CAAC,iBAAiB;CAoB1B"}