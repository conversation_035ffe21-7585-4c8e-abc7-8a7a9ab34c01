"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolValidationService = exports.ProviderValidationService = exports.ValidationService = void 0;
class ValidationService {
    rules = new Map();
    addRule(field, rule) {
        if (!this.rules.has(field)) {
            this.rules.set(field, []);
        }
        this.rules.get(field).push(rule);
    }
    addRules(field, rules) {
        for (const rule of rules) {
            this.addRule(field, rule);
        }
    }
    async validate(data) {
        const errors = [];
        const warnings = [];
        for (const [field, rules] of this.rules) {
            const value = data[field];
            for (const rule of rules) {
                try {
                    const isValid = await rule.validate(value);
                    if (!isValid) {
                        errors.push(`${field}: ${rule.message}`);
                    }
                }
                catch (error) {
                    errors.push(`${field}: Validation error - ${error instanceof Error ? error.message : String(error)}`);
                }
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    async validateField(field, value) {
        const errors = [];
        const warnings = [];
        const rules = this.rules.get(field) || [];
        for (const rule of rules) {
            try {
                const isValid = await rule.validate(value);
                if (!isValid) {
                    errors.push(rule.message);
                }
            }
            catch (error) {
                errors.push(`Validation error - ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    removeRule(field, ruleName) {
        const rules = this.rules.get(field);
        if (!rules)
            return false;
        const index = rules.findIndex(rule => rule.name === ruleName);
        if (index === -1)
            return false;
        rules.splice(index, 1);
        if (rules.length === 0) {
            this.rules.delete(field);
        }
        return true;
    }
    removeAllRules(field) {
        return this.rules.delete(field);
    }
    clear() {
        this.rules.clear();
    }
    getFields() {
        return Array.from(this.rules.keys());
    }
    getRules(field) {
        return this.rules.get(field) || [];
    }
    // Common validation rules
    static COMMON_RULES = {
        required: (message = 'This field is required') => ({
            name: 'required',
            validate: (value) => value !== null && value !== undefined && value !== '',
            message,
        }),
        minLength: (min, message) => ({
            name: 'minLength',
            validate: (value) => typeof value === 'string' && value.length >= min,
            message: message || `Must be at least ${min} characters long`,
        }),
        maxLength: (max, message) => ({
            name: 'maxLength',
            validate: (value) => typeof value === 'string' && value.length <= max,
            message: message || `Must be no more than ${max} characters long`,
        }),
        email: (message = 'Must be a valid email address') => ({
            name: 'email',
            validate: (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return typeof value === 'string' && emailRegex.test(value);
            },
            message,
        }),
        url: (message = 'Must be a valid URL') => ({
            name: 'url',
            validate: (value) => {
                try {
                    new URL(value);
                    return true;
                }
                catch {
                    return false;
                }
            },
            message,
        }),
        numeric: (message = 'Must be a number') => ({
            name: 'numeric',
            validate: (value) => !isNaN(Number(value)),
            message,
        }),
        integer: (message = 'Must be an integer') => ({
            name: 'integer',
            validate: (value) => Number.isInteger(Number(value)),
            message,
        }),
        min: (min, message) => ({
            name: 'min',
            validate: (value) => typeof value === 'number' && value >= min,
            message: message || `Must be at least ${min}`,
        }),
        max: (max, message) => ({
            name: 'max',
            validate: (value) => typeof value === 'number' && value <= max,
            message: message || `Must be no more than ${max}`,
        }),
        pattern: (regex, message = 'Invalid format') => ({
            name: 'pattern',
            validate: (value) => typeof value === 'string' && regex.test(value),
            message,
        }),
        oneOf: (options, message) => ({
            name: 'oneOf',
            validate: (value) => options.includes(value),
            message: message || `Must be one of: ${options.join(', ')}`,
        }),
        custom: (name, validator, message) => ({
            name,
            validate: validator,
            message,
        }),
    };
}
exports.ValidationService = ValidationService;
// Provider-specific validation
class ProviderValidationService extends ValidationService {
    constructor() {
        super();
        this.setupProviderRules();
    }
    setupProviderRules() {
        // API Key validation
        this.addRule('apiKey', ValidationService.COMMON_RULES.required('API key is required'));
        this.addRule('apiKey', ValidationService.COMMON_RULES.minLength(10, 'API key must be at least 10 characters'));
        // Model validation
        this.addRule('defaultModel', ValidationService.COMMON_RULES.required('Default model is required'));
        // Provider-specific rules
        this.addRule('baseURL', ValidationService.COMMON_RULES.url('Base URL must be a valid URL'));
        // OpenAI specific
        this.addRule('organization', ValidationService.COMMON_RULES.custom('organization', (value) => !value || (typeof value === 'string' && value.length > 0), 'Organization ID must be a non-empty string if provided'));
        // Google specific
        this.addRule('projectId', ValidationService.COMMON_RULES.custom('projectId', (value) => !value || (typeof value === 'string' && /^[a-z][a-z0-9-]*[a-z0-9]$/.test(value)), 'Project ID must be a valid Google Cloud project ID'));
    }
}
exports.ProviderValidationService = ProviderValidationService;
// Tool parameter validation
class ToolValidationService extends ValidationService {
    constructor() {
        super();
        this.setupToolRules();
    }
    setupToolRules() {
        // File path validation
        this.addRule('path', ValidationService.COMMON_RULES.required('Path is required'));
        this.addRule('path', ValidationService.COMMON_RULES.custom('validPath', (value) => typeof value === 'string' && value.length > 0 && !value.includes('..'), 'Path must be valid and not contain ".."'));
        // Command validation
        this.addRule('command', ValidationService.COMMON_RULES.required('Command is required'));
        this.addRule('command', ValidationService.COMMON_RULES.minLength(1, 'Command cannot be empty'));
        // URL validation for web tools
        this.addRule('url', ValidationService.COMMON_RULES.required('URL is required'));
        this.addRule('url', ValidationService.COMMON_RULES.url('Must be a valid URL'));
        // Search query validation
        this.addRule('query', ValidationService.COMMON_RULES.required('Search query is required'));
        this.addRule('query', ValidationService.COMMON_RULES.minLength(1, 'Search query cannot be empty'));
        this.addRule('query', ValidationService.COMMON_RULES.maxLength(500, 'Search query too long'));
        // Memory validation
        this.addRule('content', ValidationService.COMMON_RULES.required('Content is required'));
        this.addRule('content', ValidationService.COMMON_RULES.minLength(1, 'Content cannot be empty'));
        this.addRule('content', ValidationService.COMMON_RULES.maxLength(10000, 'Content too long'));
        // Importance validation
        this.addRule('importance', ValidationService.COMMON_RULES.numeric('Importance must be a number'));
        this.addRule('importance', ValidationService.COMMON_RULES.min(1, 'Importance must be at least 1'));
        this.addRule('importance', ValidationService.COMMON_RULES.max(10, 'Importance must be at most 10'));
    }
}
exports.ToolValidationService = ToolValidationService;
//# sourceMappingURL=ValidationService.js.map