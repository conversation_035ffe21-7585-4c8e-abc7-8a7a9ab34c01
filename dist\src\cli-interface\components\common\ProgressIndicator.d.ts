import { ProgressIndicator, CLITheme } from '../../types';
export declare class CLIProgressIndicator implements ProgressIndicator {
    private spinner;
    private theme;
    constructor(theme: CLITheme);
    start(message: string): void;
    update(message: string): void;
    succeed(message?: string): void;
    fail(message?: string): void;
    stop(): void;
}
export declare class SimpleProgressIndicator implements ProgressIndicator {
    private theme;
    constructor(theme: CLITheme);
    start(message: string): void;
    update(message: string): void;
    succeed(message?: string): void;
    fail(message?: string): void;
    stop(): void;
}
//# sourceMappingURL=ProgressIndicator.d.ts.map