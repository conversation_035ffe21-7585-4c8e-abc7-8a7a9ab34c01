"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOOL_CONFIGS = exports.TOOL_CATEGORIES = exports.MCPToolInterface = exports.MCPTool = exports.MemoryTool = exports.WebSearchTool = exports.WebFetchTool = exports.ShellTool = exports.EditTool = exports.WriteFileTool = exports.ReadFileTool = exports.LSTool = exports.ToolRegistry = exports.BaseTool = void 0;
exports.createDefaultToolRegistry = createDefaultToolRegistry;
// Base tools
var base_1 = require("./base");
Object.defineProperty(exports, "BaseTool", { enumerable: true, get: function () { return base_1.BaseTool; } });
Object.defineProperty(exports, "ToolRegistry", { enumerable: true, get: function () { return base_1.ToolRegistry; } });
// Filesystem tools
var LSTool_1 = require("./filesystem/LSTool");
Object.defineProperty(exports, "LSTool", { enumerable: true, get: function () { return LSTool_1.LSTool; } });
var ReadFileTool_1 = require("./filesystem/ReadFileTool");
Object.defineProperty(exports, "ReadFileTool", { enumerable: true, get: function () { return ReadFileTool_1.ReadFileTool; } });
var WriteFileTool_1 = require("./filesystem/WriteFileTool");
Object.defineProperty(exports, "WriteFileTool", { enumerable: true, get: function () { return WriteFileTool_1.WriteFileTool; } });
var EditTool_1 = require("./filesystem/EditTool");
Object.defineProperty(exports, "EditTool", { enumerable: true, get: function () { return EditTool_1.EditTool; } });
// Execution tools
var ShellTool_1 = require("./execution/ShellTool");
Object.defineProperty(exports, "ShellTool", { enumerable: true, get: function () { return ShellTool_1.ShellTool; } });
// Web tools
var WebFetchTool_1 = require("./web/WebFetchTool");
Object.defineProperty(exports, "WebFetchTool", { enumerable: true, get: function () { return WebFetchTool_1.WebFetchTool; } });
var WebSearchTool_1 = require("./web/WebSearchTool");
Object.defineProperty(exports, "WebSearchTool", { enumerable: true, get: function () { return WebSearchTool_1.WebSearchTool; } });
// Memory tools
var MemoryTool_1 = require("./memory/MemoryTool");
Object.defineProperty(exports, "MemoryTool", { enumerable: true, get: function () { return MemoryTool_1.MemoryTool; } });
// MCP tools
var MCPTool_1 = require("./mcp/MCPTool");
Object.defineProperty(exports, "MCPTool", { enumerable: true, get: function () { return MCPTool_1.MCPTool; } });
Object.defineProperty(exports, "MCPToolInterface", { enumerable: true, get: function () { return MCPTool_1.MCPTool; } });
// Import classes for factory function
const ToolRegistry_1 = require("./base/ToolRegistry");
const LSTool_2 = require("./filesystem/LSTool");
const ReadFileTool_2 = require("./filesystem/ReadFileTool");
const WriteFileTool_2 = require("./filesystem/WriteFileTool");
const EditTool_2 = require("./filesystem/EditTool");
const ShellTool_2 = require("./execution/ShellTool");
const WebFetchTool_2 = require("./web/WebFetchTool");
const WebSearchTool_2 = require("./web/WebSearchTool");
const MemoryTool_2 = require("./memory/MemoryTool");
const MCPTool_2 = require("./mcp/MCPTool");
// Tool factory function
function createDefaultToolRegistry() {
    const registry = new ToolRegistry_1.ToolRegistry();
    // Register filesystem tools
    registry.registerTool(new LSTool_2.LSTool());
    registry.registerTool(new ReadFileTool_2.ReadFileTool());
    registry.registerTool(new WriteFileTool_2.WriteFileTool());
    registry.registerTool(new EditTool_2.EditTool());
    // Register execution tools
    registry.registerTool(new ShellTool_2.ShellTool());
    // Register web tools
    registry.registerTool(new WebFetchTool_2.WebFetchTool());
    registry.registerTool(new WebSearchTool_2.WebSearchTool());
    // Register memory tools
    registry.registerTool(new MemoryTool_2.MemoryTool());
    // Register MCP tools
    registry.registerTool(new MCPTool_2.MCPTool());
    return registry;
}
// Tool categories
exports.TOOL_CATEGORIES = {
    FILESYSTEM: 'filesystem',
    EXECUTION: 'execution',
    WEB: 'web',
    MEMORY: 'memory',
    MCP: 'mcp',
};
// Common tool configurations
exports.TOOL_CONFIGS = {
    DEFAULT_TIMEOUT: 30000,
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_CONTENT_LENGTH: 100000,
    DEFAULT_ENCODING: 'utf8',
};
//# sourceMappingURL=index.js.map