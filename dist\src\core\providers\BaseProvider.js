"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseProvider = void 0;
const ErrorHandlingMiddleware_1 = require("../error-handling/ErrorHandlingMiddleware");
const ProviderErrorHandling_1 = require("./ProviderErrorHandling");
class BaseProvider {
    config;
    errorHandler;
    constructor(config) {
        this.config = config;
        this.initializeErrorHandling();
    }
    /**
     * Initialize error handling middleware with provider-specific configuration
     */
    initializeErrorHandling() {
        // Use provider-specific error handling configuration
        const mergedConfig = (0, ProviderErrorHandling_1.mergeProviderErrorConfig)(this.name, this.config.errorHandling);
        this.errorHandler = new ErrorHandlingMiddleware_1.ErrorHandlingMiddleware(mergedConfig);
    }
    isConfigured() {
        return !!(this.config.apiKey && this.config.apiKey.trim().length > 0);
    }
    formatMessages(messages) {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content,
        }));
    }
    formatTools(tools) {
        if (!tools || tools.length === 0) {
            return [];
        }
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters,
            },
        }));
    }
    createChatMessage(content, role = 'assistant', metadata) {
        // Extract toolCalls from metadata to put at top level
        const { toolCalls, ...restMetadata } = metadata || {};
        return {
            id: this.generateId(),
            role,
            content,
            timestamp: new Date(),
            ...(toolCalls && { toolCalls }),
            ...(Object.keys(restMetadata).length > 0 && { metadata: restMetadata }),
        };
    }
    generateId() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    /**
     * Execute an API operation with comprehensive error handling
     */
    async executeWithErrorHandling(operation, operationName, options) {
        const context = {
            operationType: 'providerCall',
            operationName,
            serviceName: this.name,
            metadata: {
                provider: this.name,
                model: this.config.defaultModel,
                ...options?.metadata
            }
        };
        const result = await this.errorHandler.executeWithErrorHandling(operation, context, options?.progressCallback);
        if (!result.success) {
            // Convert error handling result to provider-specific error
            const providerError = this.enhanceError(result.error, result.category, operationName);
            throw providerError;
        }
        return result.result;
    }
    /**
     * Enhanced error handling that adds provider-specific context
     */
    enhanceError(error, category, context) {
        // First apply the original error handling logic
        const baseError = this.handleError(error, context);
        // Add category and provider-specific enhancements
        const enhancedError = new Error(baseError.message);
        enhancedError.category = category;
        enhancedError.provider = this.name;
        enhancedError.context = context;
        enhancedError.originalError = error;
        return enhancedError;
    }
    handleError(error, context) {
        console.error(`${this.name} Provider Error (${context}):`, error);
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.message;
            switch (status) {
                case 401:
                    return new Error(`Authentication failed: Invalid API key for ${this.name}`);
                case 403:
                    return new Error(`Access forbidden: Check your ${this.name} API permissions`);
                case 429:
                    return new Error(`Rate limit exceeded for ${this.name}. Please try again later.`);
                case 500:
                case 502:
                case 503:
                    return new Error(`${this.name} service is temporarily unavailable`);
                default:
                    return new Error(`${this.name} API error (${status}): ${message}`);
            }
        }
        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            return new Error(`Cannot connect to ${this.name} API. Check your internet connection.`);
        }
        return new Error(`${this.name} provider error: ${error.message || error}`);
    }
    validateConfig() {
        if (!this.config.apiKey) {
            throw new Error(`API key is required for ${this.name} provider`);
        }
        if (!this.config.defaultModel) {
            throw new Error(`Default model is required for ${this.name} provider`);
        }
    }
    getRequestOptions(options) {
        return {
            model: options?.model || this.config.defaultModel,
            maxTokens: options?.maxTokens || this.config.maxTokens || 4000,
            temperature: options?.temperature ?? this.config.temperature ?? 0.7,
            stream: options?.stream || false,
        };
    }
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }
    getConfig() {
        return { ...this.config };
    }
    async testConnection() {
        try {
            const testMessage = {
                id: 'test',
                role: 'user',
                content: 'Hello',
                timestamp: new Date(),
            };
            await this.sendMessage([testMessage], [], { maxTokens: 10 });
            return true;
        }
        catch (error) {
            console.error(`Connection test failed for ${this.name}:`, error);
            return false;
        }
    }
    /**
     * Get error handling metrics for this provider
     */
    getErrorMetrics() {
        return this.errorHandler.getMetricsSnapshot();
    }
    /**
     * Get circuit breaker status for this provider
     */
    getCircuitBreakerStatus() {
        return this.errorHandler.getCircuitBreakerMetrics();
    }
    /**
     * Reset circuit breakers for this provider
     */
    resetCircuitBreakers() {
        this.errorHandler.resetCircuitBreakers();
    }
    /**
     * Update error handling configuration
     */
    updateErrorHandlingConfig(config) {
        this.errorHandler.updateConfig(config);
    }
    /**
     * Get current error handling configuration
     */
    getErrorHandlingConfig() {
        return this.errorHandler.getConfig();
    }
    /**
     * Get recent error logs for this provider
     */
    getRecentErrorLogs(count = 50) {
        return this.errorHandler.getRecentLogs(count).filter(log => log.context?.provider === this.name);
    }
    async getAvailableModels() {
        // Default implementation returns empty array
        // Subclasses can override to fetch from API
        return [];
    }
}
exports.BaseProvider = BaseProvider;
//# sourceMappingURL=BaseProvider.js.map