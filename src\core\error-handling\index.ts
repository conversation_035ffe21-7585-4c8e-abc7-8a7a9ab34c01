/**
 * Error Handling System - Main Export File
 * 
 * Comprehensive error handling system with:
 * - Retry policies with exponential backoff and jitter
 * - Circuit breaker pattern for service protection
 * - Error classification and categorization
 * - Metrics collection and monitoring
 * - Structured logging and debugging
 * - Centralized error handling middleware
 */

// Core Components
export { RetryPolicy, RetryPolicyConfig, RetryResult, JitterType } from './RetryPolicy';
export { 
  CircuitBreaker, 
  CircuitBreakerConfig, 
  CircuitBreakerState, 
  CircuitBreakerRegistry,
  CircuitBreakerError 
} from './CircuitBreaker';
export { ErrorClassifier, ErrorCategory, ErrorClassification } from './ErrorClassifier';

// Middleware
export { 
  ErrorHandlingMiddleware,
  ErrorHandlingContext,
  ErrorHandlingResult,
  ProgressCallback
} from './ErrorHandlingMiddleware';

// Monitoring and Logging
export { 
  MetricsCollector,
  RetryMetrics,
  CircuitBreakerMetrics,
  ErrorMetrics,
  PerformanceMetrics,
  SystemHealthMetrics,
  MetricsSnapshot,
  MetricEvent
} from './MetricsCollector';

export { 
  ErrorLogger,
  LogLevel,
  LogEntry,
  LoggerConfig
} from './ErrorLogger';

// Configuration Types
export { 
  ErrorHandlingConfiguration,
  RetryConfiguration,
  CircuitBreakerConfiguration
} from '../types';

// Utility Functions and Constants
export const DEFAULT_RETRY_CONFIG = {
  maxAttempts: 3,
  baseDelay: 1000,
  backoffMultiplier: 2,
  maxDelay: 60000,
  backoffStrategy: 'exponential' as const,
  enableJitter: true,
  jitterType: 'full' as const,
  attemptTimeout: 30000
};

export const DEFAULT_CIRCUIT_BREAKER_CONFIG = {
  failureThreshold: 5,
  recoveryTimeout: 30000,
  successThreshold: 2,
  monitoringWindow: 60000,
  enabled: true
};

export const PRESET_RETRY_POLICIES = {
  AGGRESSIVE: {
    maxAttempts: 5,
    baseDelay: 500,
    backoffMultiplier: 1.5,
    maxDelay: 10000,
    backoffStrategy: 'exponential' as const,
    enableJitter: true,
    jitterType: 'full' as const
  },
  CONSERVATIVE: {
    maxAttempts: 2,
    baseDelay: 2000,
    backoffMultiplier: 2,
    maxDelay: 30000,
    backoffStrategy: 'exponential' as const,
    enableJitter: true,
    jitterType: 'decorrelated' as const
  },
  NETWORK_OPTIMIZED: {
    maxAttempts: 3,
    baseDelay: 1000,
    backoffMultiplier: 2,
    maxDelay: 60000,
    backoffStrategy: 'exponential' as const,
    enableJitter: true,
    jitterType: 'full' as const,
    attemptTimeout: 15000
  },
  FILE_OPERATIONS: {
    maxAttempts: 2,
    baseDelay: 500,
    backoffMultiplier: 1.5,
    maxDelay: 5000,
    backoffStrategy: 'linear' as const,
    enableJitter: false
  }
};

/**
 * Create a complete error handling middleware with default configuration
 */
export function createErrorHandlingMiddleware(
  config?: Partial<ErrorHandlingConfiguration>
): ErrorHandlingMiddleware {
  const defaultConfig: ErrorHandlingConfiguration = {
    retry: DEFAULT_RETRY_CONFIG,
    circuitBreaker: DEFAULT_CIRCUIT_BREAKER_CONFIG,
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: true,
    allowRetryCancel: true,
    operationPolicies: {
      toolExecution: PRESET_RETRY_POLICIES.CONSERVATIVE,
      providerCalls: PRESET_RETRY_POLICIES.NETWORK_OPTIMIZED,
      fileOperations: PRESET_RETRY_POLICIES.FILE_OPERATIONS,
      networkRequests: PRESET_RETRY_POLICIES.NETWORK_OPTIMIZED
    }
  };

  const mergedConfig = { ...defaultConfig, ...config };
  return new ErrorHandlingMiddleware(mergedConfig);
}

/**
 * Create a retry policy with preset configuration
 */
export function createRetryPolicy(
  preset: keyof typeof PRESET_RETRY_POLICIES | RetryPolicyConfig
): RetryPolicy {
  if (typeof preset === 'string') {
    return new RetryPolicy(PRESET_RETRY_POLICIES[preset]);
  }
  return new RetryPolicy(preset);
}

/**
 * Create a circuit breaker with default configuration
 */
export function createCircuitBreaker(
  serviceName: string,
  config?: Partial<CircuitBreakerConfig>
): CircuitBreaker {
  const mergedConfig = { ...DEFAULT_CIRCUIT_BREAKER_CONFIG, ...config };
  return new CircuitBreaker(serviceName, mergedConfig);
}

/**
 * Utility function to determine if an error is retryable
 */
export function isRetryableError(error: Error): boolean {
  const classifier = new ErrorClassifier();
  const classification = classifier.classify(error);
  return classification.shouldRetry;
}

/**
 * Utility function to get error category
 */
export function getErrorCategory(error: Error): ErrorCategory {
  const classifier = new ErrorClassifier();
  const classification = classifier.classify(error);
  return classification.category;
}

/**
 * Utility function to create an error handling context
 */
export function createErrorHandlingContext(
  operationType: 'toolExecution' | 'providerCall' | 'fileOperation' | 'networkRequest' | 'general',
  operationName: string,
  options?: {
    serviceName?: string;
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
  }
): ErrorHandlingContext {
  return {
    operationType,
    operationName,
    serviceName: options?.serviceName,
    userId: options?.userId,
    sessionId: options?.sessionId,
    metadata: options?.metadata
  };
}

/**
 * Error handling system version and metadata
 */
export const ERROR_HANDLING_SYSTEM = {
  version: '1.0.0',
  name: 'CLI Agent Error Handling System',
  description: 'Comprehensive error handling with retry policies, circuit breakers, and monitoring',
  features: [
    'Exponential backoff with jitter',
    'Circuit breaker pattern',
    'Error classification',
    'Metrics collection',
    'Structured logging',
    'Progress callbacks',
    'Cancellation support'
  ]
} as const;
