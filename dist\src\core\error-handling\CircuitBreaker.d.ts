/**
 * Circuit Breaker Pattern Implementation
 *
 * Provides fault tolerance by monitoring failures and preventing calls to failing services:
 * - CLOSED: Normal operation, calls pass through
 * - OPEN: Service is failing, calls are rejected immediately
 * - HALF_OPEN: Testing if service has recovered
 */
export declare enum CircuitBreakerState {
    CLOSED = "CLOSED",
    OPEN = "OPEN",
    HALF_OPEN = "HALF_OPEN"
}
export interface CircuitBreakerConfig {
    /** Number of consecutive failures to trigger OPEN state (default: 5) */
    failureThreshold: number;
    /** Time in milliseconds to wait before transitioning to HALF_OPEN (default: 30000) */
    recoveryTimeout: number;
    /** Number of successful calls in HALF_OPEN to transition to CLOSED (default: 2) */
    successThreshold: number;
    /** Time window in milliseconds for failure counting (default: 60000) */
    monitoringWindow: number;
    /** Custom function to determine if an error should count as a failure */
    isFailure?: (error: Error) => boolean;
    /** Callback when state changes */
    onStateChange?: (oldState: CircuitBreakerState, newState: CircuitBreakerState, serviceName: string) => void;
}
export interface CircuitBreakerMetrics {
    state: CircuitBreakerState;
    failureCount: number;
    successCount: number;
    lastFailureTime?: Date;
    lastSuccessTime?: Date;
    stateChangedAt: Date;
    totalCalls: number;
    totalFailures: number;
    totalSuccesses: number;
}
export declare class CircuitBreakerError extends Error {
    constructor(serviceName: string, state: CircuitBreakerState);
}
export declare class CircuitBreaker {
    private config;
    private state;
    private failureCount;
    private successCount;
    private lastFailureTime?;
    private lastSuccessTime?;
    private stateChangedAt;
    private totalCalls;
    private totalFailures;
    private totalSuccesses;
    private serviceName;
    constructor(serviceName: string, config?: Partial<CircuitBreakerConfig>);
    /**
     * Execute a function with circuit breaker protection
     */
    execute<T>(operation: () => Promise<T>): Promise<T>;
    /**
     * Check if the circuit breaker allows execution
     */
    private canExecute;
    /**
     * Handle successful operation
     */
    private onSuccess;
    /**
     * Handle failed operation
     */
    private onFailure;
    /**
     * Transition to a new state
     */
    private transitionTo;
    /**
     * Check if we should attempt recovery from OPEN state
     */
    private shouldAttemptRecovery;
    /**
     * Get current metrics
     */
    getMetrics(): CircuitBreakerMetrics;
    /**
     * Get current state
     */
    getState(): CircuitBreakerState;
    /**
     * Force state transition (for testing or manual intervention)
     */
    forceState(state: CircuitBreakerState): void;
    /**
     * Reset circuit breaker to initial state
     */
    reset(): void;
    /**
     * Update configuration
     */
    updateConfig(updates: Partial<CircuitBreakerConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): CircuitBreakerConfig;
    /**
     * Get service name
     */
    getServiceName(): string;
}
/**
 * Circuit Breaker Registry for managing multiple circuit breakers
 */
export declare class CircuitBreakerRegistry {
    private breakers;
    private defaultConfig;
    constructor(defaultConfig?: Partial<CircuitBreakerConfig>);
    /**
     * Get or create a circuit breaker for a service
     */
    getBreaker(serviceName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker;
    /**
     * Execute operation with circuit breaker protection
     */
    execute<T>(serviceName: string, operation: () => Promise<T>, config?: Partial<CircuitBreakerConfig>): Promise<T>;
    /**
     * Get all circuit breakers
     */
    getAllBreakers(): Map<string, CircuitBreaker>;
    /**
     * Get metrics for all circuit breakers
     */
    getAllMetrics(): Record<string, CircuitBreakerMetrics>;
    /**
     * Reset all circuit breakers
     */
    resetAll(): void;
    /**
     * Remove a circuit breaker
     */
    removeBreaker(serviceName: string): boolean;
}
//# sourceMappingURL=CircuitBreaker.d.ts.map