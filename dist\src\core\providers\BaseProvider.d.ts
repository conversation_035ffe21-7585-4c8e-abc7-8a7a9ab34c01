import { ProviderClient, ChatMessage, BaseTool } from '../types';
import { ErrorHandlingMiddleware, ProgressCallback } from '../error-handling/ErrorHandlingMiddleware';
import { ErrorHandlingConfiguration } from '../types';
export interface ProviderConfig {
    apiKey: string;
    baseUrl?: string;
    defaultModel: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
    errorHandling?: Partial<ErrorHandlingConfiguration>;
}
export interface ProviderOptions {
    model?: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
    tools?: BaseTool[];
}
export interface ProviderResponse {
    content: string;
    model: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    toolCalls?: Array<{
        id: string;
        name: string;
        parameters: Record<string, any>;
    }>;
    finishReason?: string;
}
export declare abstract class BaseProvider implements ProviderClient {
    protected config: ProviderConfig;
    protected errorHandler: ErrorHandlingMiddleware;
    abstract name: string;
    constructor(config: ProviderConfig);
    /**
     * Initialize error handling middleware with provider-specific configuration
     */
    private initializeErrorHandling;
    isConfigured(): boolean;
    abstract sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    abstract streamMessage?(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    protected formatMessages(messages: ChatMessage[]): any[] | {
        systemMessage?: string;
        formattedMessages: any[];
    };
    protected formatTools(tools?: BaseTool[]): any[];
    protected createChatMessage(content: string, role?: 'assistant', metadata?: any): ChatMessage;
    protected generateId(): string;
    /**
     * Execute an API operation with comprehensive error handling
     */
    protected executeWithErrorHandling<T>(operation: () => Promise<T>, operationName: string, options?: {
        progressCallback?: ProgressCallback;
        metadata?: Record<string, any>;
    }): Promise<T>;
    /**
     * Enhanced error handling that adds provider-specific context
     */
    private enhanceError;
    protected handleError(error: any, context: string): Error;
    protected validateConfig(): void;
    protected getRequestOptions(options?: ProviderOptions): {
        model: string;
        maxTokens: number;
        temperature: number;
        stream: boolean;
    };
    updateConfig(updates: Partial<ProviderConfig>): void;
    getConfig(): ProviderConfig;
    testConnection(): Promise<boolean>;
    /**
     * Get error handling metrics for this provider
     */
    getErrorMetrics(): import("../error-handling").MetricsSnapshot;
    /**
     * Get circuit breaker status for this provider
     */
    getCircuitBreakerStatus(): Record<string, any>;
    /**
     * Reset circuit breakers for this provider
     */
    resetCircuitBreakers(): void;
    /**
     * Update error handling configuration
     */
    updateErrorHandlingConfig(config: Partial<ErrorHandlingConfiguration>): void;
    /**
     * Get current error handling configuration
     */
    getErrorHandlingConfig(): ErrorHandlingConfiguration;
    /**
     * Get recent error logs for this provider
     */
    getRecentErrorLogs(count?: number): import("../error-handling").LogEntry[];
    getAvailableModels(): Promise<string[]>;
}
//# sourceMappingURL=BaseProvider.d.ts.map