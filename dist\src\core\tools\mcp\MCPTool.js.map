{"version": 3, "file": "MCPTool.js", "sourceRoot": "", "sources": ["../../../../../src/core/tools/mcp/MCPTool.ts"], "names": [], "mappings": ";;;AAAA,+CAAkE;AAwBlE,MAAa,OAAQ,SAAQ,mBAAQ;IAC5B,IAAI,GAAG,KAAK,CAAC;IACb,WAAW,GAAG,gFAAgF,CAAC;IAC/F,oBAAoB,GAAG,IAAI,CAAC;IAE5B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,CAAC;gBAC7G,WAAW,EAAE,uBAAuB;aACrC;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oDAAoD;aAClE;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,yCAAyC;gBACtD,UAAU,EAAE;oBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAClD,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;oBACjE,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBACxB;aACF;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,6BAA6B;aAC3C;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0BAA0B;aACxC;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,+BAA+B;aAC7C;SACF;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB,CAAC;IAEM,gBAAgB,GAAqC,IAAI,GAAG,EAAE,CAAC;IAEvE;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC;YACtC,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,MAAM,YAAY,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;QAE7H,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,0BAA0B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,wCAAwC;QACxC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,SAAS;gBACZ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAC9D,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC;YAClB,KAAK,gBAAgB,CAAC;YACtB,KAAK,YAAY;gBACf,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,SAAS,CAAC,CAAC;gBAC9D,CAAC;gBACD,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBAClE,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBACnE,CAAC;gBACD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAC9D,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;oBACtB,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBAC5D,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;YAE1B,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,cAAc;oBACjB,OAAO,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClC,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC1C,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC7C,KAAK,gBAAgB;oBACnB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC1C,KAAK,eAAe;oBAClB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzC,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACtC,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACrC;oBACE,OAAO,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAClF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC;YAClC,CAAC,CAAC,2BAA2B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC1E,CAAC,CAAC,oCAAoC,CAAC;QAEzC,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EACvB,SAAS,EACT,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,CAAC,MAAM,EAAE,CACxD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAA2B;QACrD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;QAE9C,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,uBAAuB,CAAC,CAAC;QAC9E,CAAC;QAED,2CAA2C;QAC3C,0EAA0E;QAC1E,MAAM,UAAU,GAAwB;YACtC,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,aAAa;YACrB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAEnD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC1B,yCAAyC,WAAW,EAAE,EACtD,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,CAC/C,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAA2B;QACxD,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC,mBAAmB,CAC7B,qBAAqB,WAAW,IAAI,EACpC,8CAA8C,WAAW,EAAE,EAC3D,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,CAClD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAA2B;QACrD,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,mCAAmC;QACnC,MAAM,SAAS,GAAkB;YAC/B;gBACE,GAAG,EAAE,qBAAqB;gBAC1B,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,sBAAsB;gBACnC,QAAQ,EAAE,YAAY;aACvB;SACF,CAAC;QAEF,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;YACpC,CAAC,CAAC,kBAAkB,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC9F,CAAC,CAAC,+BAA+B,WAAW,EAAE,CAAC;QAEjD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EACzB,SAAS,EACT,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,CAAC,MAAM,EAAE,CACvF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAA2B;QACpD,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,0CAA0C;QAC1C,MAAM,OAAO,GAAG,8BAA8B,YAAY,EAAE,CAAC;QAE7D,OAAO,IAAI,CAAC,mBAAmB,CAC7B,OAAO,EACP,yBAAyB,WAAW,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,OAAO,EAAE,EACvF,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,CAChF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,MAA2B;QACjD,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,+BAA+B;QAC/B,MAAM,KAAK,GAA+C;YACxD;gBACE,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,qBAAqB;aACnC;SACF,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;YAChC,CAAC,CAAC,cAAc,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7F,CAAC,CAAC,2BAA2B,WAAW,EAAE,CAAC;QAE7C,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EACrB,SAAS,EACT,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAC3E,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAChD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,WAAW,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,wCAAwC;QACxC,MAAM,MAAM,GAAG;YACb,QAAQ,EAAE,SAAS;YACnB,SAAS,EAAE,cAAc;YACzB,MAAM,EAAE,oBAAoB,SAAS,OAAO,WAAW,EAAE;YACzD,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,mCAAmC,WAAW,WAAW,SAAS,aAAa,MAAM,CAAC,MAAM,EAAE,EAC9F,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,CACtE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,MAA2B;QAC3D,0EAA0E;QAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE7B,wDAAwD;QACxD,MAAM,WAAW,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACrE,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAxTD,0BAwTC"}