/**
 * Comprehensive Error Handling Middleware
 *
 * Provides centralized error handling with:
 * - Error classification and categorization
 * - Retry policy integration
 * - Circuit breaker protection
 * - User-friendly error messages
 * - Progress indicators and cancellation support
 */
import { ErrorCategory } from './ErrorClassifier';
import { MetricsCollector } from './MetricsCollector';
import { ErrorLogger } from './ErrorLogger';
import { ErrorHandlingConfiguration } from '../types';
export interface ErrorHandlingContext {
    operationType: 'toolExecution' | 'providerCall' | 'fileOperation' | 'networkRequest' | 'general';
    operationName: string;
    serviceName?: string;
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
}
export interface ErrorHandlingResult<T> {
    success: boolean;
    result?: T;
    error?: Error;
    category?: ErrorCategory;
    retryAttempts: number;
    totalDuration: number;
    circuitBreakerTriggered: boolean;
    userMessage: string;
    technicalDetails?: string;
    suggestedAction?: string;
    canRetryManually: boolean;
}
export interface ProgressCallback {
    onRetryAttempt?: (attempt: number, maxAttempts: number, delay: number, error: Error) => void;
    onCircuitBreakerOpen?: (serviceName: string) => void;
    onCircuitBreakerHalfOpen?: (serviceName: string) => void;
    onCircuitBreakerClosed?: (serviceName: string) => void;
    onCancel?: () => void;
}
export declare class ErrorHandlingMiddleware {
    private config;
    private classifier;
    private circuitBreakerRegistry;
    private retryPolicies;
    private cancellationTokens;
    private metricsCollector;
    private logger;
    constructor(config: ErrorHandlingConfiguration);
    /**
     * Execute operation with comprehensive error handling
     */
    executeWithErrorHandling<T>(operation: () => Promise<T>, context: ErrorHandlingContext, progressCallback?: ProgressCallback): Promise<ErrorHandlingResult<T>>;
    /**
     * Execute operation with retry policy and circuit breaker protection
     */
    private executeWithProtection;
    /**
     * Handle and classify errors
     */
    private handleError;
    /**
     * Generate user-friendly error messages
     */
    private generateUserMessage;
    /**
     * Cancel ongoing operation
     */
    cancelOperation(operationId: string): void;
    /**
     * Get retry policy for operation type
     */
    private getRetryPolicy;
    /**
     * Initialize retry policies for different operation types
     */
    private initializeRetryPolicies;
    /**
     * Calculate next delay for progress callback
     */
    private calculateNextDelay;
    /**
     * Generate unique operation ID
     */
    private generateOperationId;
    /**
     * Get circuit breaker metrics
     */
    getCircuitBreakerMetrics(): Record<string, any>;
    /**
     * Get comprehensive metrics snapshot
     */
    getMetricsSnapshot(): import("./MetricsCollector").MetricsSnapshot;
    /**
     * Get metrics collector instance
     */
    getMetricsCollector(): MetricsCollector;
    /**
     * Get logger instance
     */
    getLogger(): ErrorLogger;
    /**
     * Get recent logs
     */
    getRecentLogs(count?: number): import("./ErrorLogger").LogEntry[];
    /**
     * Export metrics for monitoring
     */
    exportMetrics(): string;
    /**
     * Export logs for analysis
     */
    exportLogs(): string;
    /**
     * Reset circuit breakers
     */
    resetCircuitBreakers(): void;
    /**
     * Clear all metrics and logs
     */
    clearMetricsAndLogs(): void;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<ErrorHandlingConfiguration>): void;
    /**
     * Get current configuration
     */
    getConfig(): ErrorHandlingConfiguration;
}
//# sourceMappingURL=ErrorHandlingMiddleware.d.ts.map