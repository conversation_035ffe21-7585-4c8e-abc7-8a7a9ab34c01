/**
 * Comprehensive Error Handling Middleware
 * 
 * Provides centralized error handling with:
 * - Error classification and categorization
 * - Retry policy integration
 * - Circuit breaker protection
 * - User-friendly error messages
 * - Progress indicators and cancellation support
 */

import { RetryPolicy, RetryResult } from './RetryPolicy';
import { CircuitBreaker, CircuitBreakerRegistry, CircuitBreakerError } from './CircuitBreaker';
import { ErrorClassifier, ErrorCategory } from './ErrorClassifier';
import { MetricsCollector } from './MetricsCollector';
import { ErrorLogger, LogLevel } from './ErrorLogger';
import { ErrorHandlingConfiguration } from '../types';

export interface ErrorHandlingContext {
  operationType: 'toolExecution' | 'providerCall' | 'fileOperation' | 'networkRequest' | 'general';
  operationName: string;
  serviceName?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorHandlingResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  category?: ErrorCategory;
  retryAttempts: number;
  totalDuration: number;
  circuitBreakerTriggered: boolean;
  userMessage: string;
  technicalDetails?: string;
  suggestedAction?: string;
  canRetryManually: boolean;
}

export interface ProgressCallback {
  onRetryAttempt?: (attempt: number, maxAttempts: number, delay: number, error: Error) => void;
  onCircuitBreakerOpen?: (serviceName: string) => void;
  onCircuitBreakerHalfOpen?: (serviceName: string) => void;
  onCircuitBreakerClosed?: (serviceName: string) => void;
  onCancel?: () => void;
}

export class ErrorHandlingMiddleware {
  private config: ErrorHandlingConfiguration;
  private classifier: ErrorClassifier;
  private circuitBreakerRegistry: CircuitBreakerRegistry;
  private retryPolicies: Map<string, RetryPolicy> = new Map();
  private cancellationTokens: Map<string, boolean> = new Map();
  private metricsCollector: MetricsCollector;
  private logger: ErrorLogger;

  constructor(config: ErrorHandlingConfiguration) {
    this.config = config;
    this.classifier = new ErrorClassifier();
    this.metricsCollector = new MetricsCollector();
    this.logger = new ErrorLogger({
      level: config.enableErrorLogging ? LogLevel.DEBUG : LogLevel.ERROR,
      enableConsoleOutput: config.enableErrorLogging,
      enableStructuredLogging: true,
      includeStackTrace: true
    });

    this.circuitBreakerRegistry = new CircuitBreakerRegistry({
      failureThreshold: config.circuitBreaker.failureThreshold,
      recoveryTimeout: config.circuitBreaker.recoveryTimeout,
      successThreshold: config.circuitBreaker.successThreshold,
      monitoringWindow: config.circuitBreaker.monitoringWindow,
      onStateChange: (oldState, newState, serviceName) => {
        this.logger.logCircuitBreakerStateChange(serviceName, oldState, newState);
        this.metricsCollector.recordCircuitBreakerStateChange(serviceName, oldState, newState);
      }
    });

    this.initializeRetryPolicies();
  }

  /**
   * Execute operation with comprehensive error handling
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: ErrorHandlingContext,
    progressCallback?: ProgressCallback
  ): Promise<ErrorHandlingResult<T>> {
    const startTime = Date.now();
    const operationId = this.generateOperationId(context);

    // Log operation start
    this.logger.logOperationStart(context.operationName, context.metadata || {}, operationId);
    this.metricsCollector.startOperation(operationId);

    try {
      // Get appropriate retry policy and circuit breaker
      const retryPolicy = this.getRetryPolicy(context.operationType);
      const circuitBreaker = context.serviceName
        ? this.circuitBreakerRegistry.getBreaker(context.serviceName)
        : null;

      // Execute with retry and circuit breaker protection
      const result = await this.executeWithProtection(
        operation,
        retryPolicy,
        circuitBreaker,
        context,
        operationId,
        progressCallback
      );

      const duration = Date.now() - startTime;

      // Log successful completion
      this.logger.logOperationComplete(context.operationName, true, duration, undefined, operationId);
      this.metricsCollector.recordPerformance(context.operationName, duration, true);

      return {
        success: true,
        result: result.result,
        retryAttempts: result.finalAttempt,
        totalDuration: result.totalDuration,
        circuitBreakerTriggered: false,
        userMessage: 'Operation completed successfully',
        canRetryManually: false
      };

    } catch (error) {
      const handledError = error instanceof Error ? error : new Error(String(error));
      const duration = Date.now() - startTime;

      // Log error
      this.logger.logOperationComplete(context.operationName, false, duration, handledError.message, operationId);
      this.metricsCollector.recordPerformance(context.operationName, duration, false);

      return this.handleError(handledError, context, duration, operationId);
    } finally {
      this.cancellationTokens.delete(operationId);
      this.metricsCollector.endOperation(operationId);
    }
  }

  /**
   * Execute operation with retry policy and circuit breaker protection
   */
  private async executeWithProtection<T>(
    operation: () => Promise<T>,
    retryPolicy: RetryPolicy,
    circuitBreaker: CircuitBreaker | null,
    context: ErrorHandlingContext,
    operationId: string,
    progressCallback?: ProgressCallback
  ): Promise<RetryResult<T>> {
    // Create wrapped operation with circuit breaker and cancellation
    const protectedOperation = async (): Promise<T> => {
      // Check for cancellation
      if (this.cancellationTokens.get(operationId)) {
        throw new Error('Operation was cancelled by user');
      }

      // Execute with circuit breaker if available
      if (circuitBreaker && this.config.circuitBreaker.enabled) {
        return await circuitBreaker.execute(operation);
      } else {
        return await operation();
      }
    };

    // Configure retry policy with progress callbacks
    const retryPolicyWithCallbacks = new RetryPolicy({
      ...retryPolicy.getConfig(),
      shouldRetry: (error: Error, attempt: number) => {
        // Check for cancellation
        if (this.cancellationTokens.get(operationId)) {
          this.logger.info(`Operation ${context.operationName} cancelled by user`, { attempt }, operationId);
          return false;
        }

        // Use classifier to determine if we should retry
        const classification = this.classifier.classify(error);

        // Log retry attempt
        if (classification.shouldRetry) {
          const delay = this.calculateNextDelay(retryPolicy, attempt);
          this.logger.logRetryAttempt(
            context.operationName,
            attempt,
            retryPolicy.getConfig().maxAttempts,
            delay,
            error,
            operationId
          );

          // Record metrics
          this.metricsCollector.recordRetryAttempt(context.operationName, attempt, delay, false);

          // Call progress callback
          if (progressCallback?.onRetryAttempt) {
            progressCallback.onRetryAttempt(attempt, retryPolicy.getConfig().maxAttempts, delay, error);
          }
        } else {
          this.logger.warn(`Not retrying ${context.operationName} due to error classification`, {
            errorType: error.constructor.name,
            errorMessage: error.message,
            category: classification.category
          }, operationId);
        }

        return classification.shouldRetry;
      }
    });

    return await retryPolicyWithCallbacks.execute(protectedOperation, context.operationName);
  }

  /**
   * Handle and classify errors
   */
  private handleError(
    error: Error,
    context: ErrorHandlingContext,
    duration: number,
    operationId?: string
  ): ErrorHandlingResult<any> {
    const classification = this.classifier.classify(error);
    const isCircuitBreakerError = error instanceof CircuitBreakerError;

    // Log error
    this.logger.error(
      `Error in ${context.operationType}: ${error.message}`,
      error,
      {
        category: classification.category,
        context,
        duration,
        isCircuitBreakerError
      },
      operationId
    );

    // Record error metrics
    this.metricsCollector.recordError(
      classification.category,
      context.serviceName || 'unknown',
      context.operationName
    );

    // Generate user-friendly message
    const userMessage = this.generateUserMessage(error, classification, context);
    const technicalDetails = this.config.enableErrorLogging ? error.stack : undefined;

    return {
      success: false,
      error,
      category: classification.category,
      retryAttempts: 0,
      totalDuration: duration,
      circuitBreakerTriggered: isCircuitBreakerError,
      userMessage,
      technicalDetails,
      suggestedAction: classification.suggestedAction,
      canRetryManually: classification.shouldRetry && !isCircuitBreakerError
    };
  }

  /**
   * Generate user-friendly error messages
   */
  private generateUserMessage(
    error: Error,
    classification: any,
    context: ErrorHandlingContext
  ): string {
    const operation = context.operationName || context.operationType;

    switch (classification.category) {
      case ErrorCategory.NETWORK_ERROR:
        return `Network connection failed while ${operation}. Please check your internet connection and try again.`;
      
      case ErrorCategory.TIMEOUT_ERROR:
        return `Operation timed out while ${operation}. The service may be slow or unavailable.`;
      
      case ErrorCategory.RATE_LIMIT_ERROR:
        return `Rate limit exceeded for ${operation}. Please wait a moment before trying again.`;
      
      case ErrorCategory.AUTHENTICATION_ERROR:
        return `Authentication failed for ${operation}. Please check your API credentials.`;
      
      case ErrorCategory.AUTHORIZATION_ERROR:
        return `Access denied for ${operation}. You may not have the required permissions.`;
      
      case ErrorCategory.VALIDATION_ERROR:
        return `Invalid input for ${operation}. Please check your parameters and try again.`;
      
      case ErrorCategory.NOT_FOUND_ERROR:
        return `Resource not found for ${operation}. Please verify the resource exists.`;
      
      case ErrorCategory.SERVER_ERROR:
        return `Server error occurred during ${operation}. Please try again later.`;
      
      case ErrorCategory.SERVICE_UNAVAILABLE:
        return `Service is temporarily unavailable for ${operation}. Please try again later.`;
      
      case ErrorCategory.CIRCUIT_BREAKER_ERROR:
        return `Service is temporarily blocked due to repeated failures. Please wait for recovery.`;
      
      case ErrorCategory.CONFIGURATION_ERROR:
        return `Configuration error for ${operation}. Please check your settings.`;
      
      default:
        return `An unexpected error occurred during ${operation}. Please try again or contact support.`;
    }
  }

  /**
   * Cancel ongoing operation
   */
  cancelOperation(operationId: string): void {
    this.cancellationTokens.set(operationId, true);
  }

  /**
   * Get retry policy for operation type
   */
  private getRetryPolicy(operationType: string): RetryPolicy {
    if (!this.retryPolicies.has(operationType)) {
      const baseConfig = this.config.retry;
      const operationConfig = this.config.operationPolicies[operationType as keyof typeof this.config.operationPolicies];
      
      const mergedConfig = { ...baseConfig, ...operationConfig };
      this.retryPolicies.set(operationType, new RetryPolicy(mergedConfig));
    }

    return this.retryPolicies.get(operationType)!;
  }

  /**
   * Initialize retry policies for different operation types
   */
  private initializeRetryPolicies(): void {
    const operationTypes = ['toolExecution', 'providerCall', 'fileOperation', 'networkRequest', 'general'];
    
    for (const operationType of operationTypes) {
      this.getRetryPolicy(operationType); // This will create and cache the policy
    }
  }

  /**
   * Calculate next delay for progress callback
   */
  private calculateNextDelay(retryPolicy: RetryPolicy, attempt: number): number {
    const config = retryPolicy.getConfig();
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
    return Math.min(exponentialDelay, config.maxDelay);
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(context: ErrorHandlingContext): string {
    return `${context.operationType}-${context.operationName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get circuit breaker metrics
   */
  getCircuitBreakerMetrics(): Record<string, any> {
    return this.circuitBreakerRegistry.getAllMetrics();
  }

  /**
   * Get comprehensive metrics snapshot
   */
  getMetricsSnapshot() {
    return this.metricsCollector.getMetricsSnapshot();
  }

  /**
   * Get metrics collector instance
   */
  getMetricsCollector(): MetricsCollector {
    return this.metricsCollector;
  }

  /**
   * Get logger instance
   */
  getLogger(): ErrorLogger {
    return this.logger;
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count: number = 100) {
    return this.logger.getRecentLogs(count);
  }

  /**
   * Export metrics for monitoring
   */
  exportMetrics(): string {
    return this.metricsCollector.exportMetrics();
  }

  /**
   * Export logs for analysis
   */
  exportLogs(): string {
    return this.logger.exportLogs();
  }

  /**
   * Reset circuit breakers
   */
  resetCircuitBreakers(): void {
    this.circuitBreakerRegistry.resetAll();
  }

  /**
   * Clear all metrics and logs
   */
  clearMetricsAndLogs(): void {
    this.metricsCollector.clearMetrics();
    this.logger.clearLogs();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ErrorHandlingConfiguration>): void {
    this.config = { ...this.config, ...config };
    this.retryPolicies.clear(); // Clear cache to force recreation with new config
    this.initializeRetryPolicies();

    // Update logger configuration
    this.logger.updateConfig({
      level: config.enableErrorLogging ? LogLevel.DEBUG : LogLevel.ERROR,
      enableConsoleOutput: config.enableErrorLogging
    });
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorHandlingConfiguration {
    return { ...this.config };
  }
}
