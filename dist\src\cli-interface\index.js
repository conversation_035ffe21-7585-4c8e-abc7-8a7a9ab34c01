#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIApplication = void 0;
const CLIService_1 = require("./services/CLIService");
const ConfigManager_1 = require("../core/config/ConfigManager");
const CLIUtils_1 = require("./utils/CLIUtils");
const types_1 = require("./types");
class CLIApplication {
    cliService = null;
    configManager = null;
    utils;
    constructor() {
        this.utils = new CLIUtils_1.CLIUtils(types_1.DEFAULT_CLI_THEME);
        this.setupErrorHandlers();
    }
    setupErrorHandlers() {
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error('\n💥 Uncaught Exception:', error.message);
            console.error(error.stack);
            this.gracefulShutdown(1);
        });
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error('\n💥 Unhandled Rejection at:', promise, 'reason:', reason);
            this.gracefulShutdown(1);
        });
        // Handle SIGINT (Ctrl+C)
        process.on('SIGINT', () => {
            console.log('\n\n👋 Goodbye!');
            this.gracefulShutdown(0);
        });
        // Handle SIGTERM
        process.on('SIGTERM', () => {
            console.log('\n\n👋 Terminating gracefully...');
            this.gracefulShutdown(0);
        });
    }
    async gracefulShutdown(exitCode) {
        try {
            if (this.cliService) {
                await this.cliService.shutdown();
            }
        }
        catch (error) {
            console.error('Error during shutdown:', error);
        }
        finally {
            process.exit(exitCode);
        }
    }
    async start() {
        try {
            // Show startup banner
            this.utils.clearScreen();
            this.utils.showBanner('🤖 AI CLI Terminal', 'Your intelligent command-line assistant');
            // Initialize configuration manager
            this.utils.showInfo('Initializing configuration...');
            this.configManager = new ConfigManager_1.ConfigManager();
            await this.configManager.initialize();
            // Initialize CLI service
            this.utils.showInfo('Starting CLI service...');
            this.cliService = new CLIService_1.CLIService(this.configManager);
            // Start the application
            this.utils.showSuccess('AI CLI Terminal ready!');
            await this.cliService.start();
        }
        catch (error) {
            this.utils.showError(`Failed to start AI CLI Terminal: ${error instanceof Error ? error.message : String(error)}`);
            console.error(error);
            process.exit(1);
        }
    }
    static async main() {
        const app = new CLIApplication();
        await app.start();
    }
}
exports.CLIApplication = CLIApplication;
// Run if this file is executed directly
if (require.main === module) {
    CLIApplication.main().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map