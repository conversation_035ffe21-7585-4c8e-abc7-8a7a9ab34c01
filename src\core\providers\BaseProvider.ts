import { ProviderClient, ChatMessage, BaseTool } from '../types';
import { ErrorHandlingMiddleware, ErrorHandlingContext, ProgressCallback } from '../error-handling/ErrorHandlingMiddleware';
import { ErrorHandlingConfiguration } from '../types';
import { mergeProviderErrorConfig } from './ProviderErrorHandling';

export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
  errorHandling?: Partial<ErrorHandlingConfiguration>;
}

export interface ProviderOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  stream?: boolean;
  tools?: BaseTool[];
}

export interface ProviderResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  toolCalls?: Array<{
    id: string;
    name: string;
    parameters: Record<string, any>;
  }>;
  finishReason?: string;
}

export abstract class BaseProvider implements ProviderClient {
  protected config: ProviderConfig;
  protected errorHandler: ErrorHandlingMiddleware;
  public abstract name: string;

  constructor(config: ProviderConfig) {
    this.config = config;
    this.initializeErrorHandling();
  }

  /**
   * Initialize error handling middleware with provider-specific configuration
   */
  private initializeErrorHandling(): void {
    // Use provider-specific error handling configuration
    const mergedConfig = mergeProviderErrorConfig(this.name, this.config.errorHandling);
    this.errorHandler = new ErrorHandlingMiddleware(mergedConfig);
  }

  public isConfigured(): boolean {
    return !!(this.config.apiKey && this.config.apiKey.trim().length > 0);
  }

  public abstract sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage>;

  public abstract streamMessage?(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage>;

  protected formatMessages(messages: ChatMessage[]): any[] | { systemMessage?: string; formattedMessages: any[] } {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
    }));
  }

  protected formatTools(tools?: BaseTool[]): any[] {
    if (!tools || tools.length === 0) {
      return [];
    }

    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  protected createChatMessage(
    content: string,
    role: 'assistant' = 'assistant',
    metadata?: any
  ): ChatMessage {
    // Extract toolCalls from metadata to put at top level
    const { toolCalls, ...restMetadata } = metadata || {};

    return {
      id: this.generateId(),
      role,
      content,
      timestamp: new Date(),
      ...(toolCalls && { toolCalls }),
      ...(Object.keys(restMetadata).length > 0 && { metadata: restMetadata }),
    };
  }

  protected generateId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Execute an API operation with comprehensive error handling
   */
  protected async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    operationName: string,
    options?: {
      progressCallback?: ProgressCallback;
      metadata?: Record<string, any>;
    }
  ): Promise<T> {
    const context: ErrorHandlingContext = {
      operationType: 'providerCall',
      operationName,
      serviceName: this.name,
      metadata: {
        provider: this.name,
        model: this.config.defaultModel,
        ...options?.metadata
      }
    };

    const result = await this.errorHandler.executeWithErrorHandling(
      operation,
      context,
      options?.progressCallback
    );

    if (!result.success) {
      // Convert error handling result to provider-specific error
      const providerError = this.enhanceError(result.error!, result.category, operationName);
      throw providerError;
    }

    return result.result!;
  }

  /**
   * Enhanced error handling that adds provider-specific context
   */
  private enhanceError(error: Error, category: string, context: string): Error {
    // First apply the original error handling logic
    const baseError = this.handleError(error, context);

    // Add category and provider-specific enhancements
    const enhancedError = new Error(baseError.message);
    (enhancedError as any).category = category;
    (enhancedError as any).provider = this.name;
    (enhancedError as any).context = context;
    (enhancedError as any).originalError = error;

    return enhancedError;
  }

  protected handleError(error: any, context: string): Error {
    console.error(`${this.name} Provider Error (${context}):`, error);

    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;

      switch (status) {
        case 401:
          return new Error(`Authentication failed: Invalid API key for ${this.name}`);
        case 403:
          return new Error(`Access forbidden: Check your ${this.name} API permissions`);
        case 429:
          return new Error(`Rate limit exceeded for ${this.name}. Please try again later.`);
        case 500:
        case 502:
        case 503:
          return new Error(`${this.name} service is temporarily unavailable`);
        default:
          return new Error(`${this.name} API error (${status}): ${message}`);
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new Error(`Cannot connect to ${this.name} API. Check your internet connection.`);
    }

    return new Error(`${this.name} provider error: ${error.message || error}`);
  }

  protected validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error(`API key is required for ${this.name} provider`);
    }
    
    if (!this.config.defaultModel) {
      throw new Error(`Default model is required for ${this.name} provider`);
    }
  }

  protected getRequestOptions(options?: ProviderOptions) {
    return {
      model: options?.model || this.config.defaultModel,
      maxTokens: options?.maxTokens || this.config.maxTokens || 4000,
      temperature: options?.temperature ?? this.config.temperature ?? 0.7,
      stream: options?.stream || false,
    };
  }

  public updateConfig(updates: Partial<ProviderConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public getConfig(): ProviderConfig {
    return { ...this.config };
  }

  public async testConnection(): Promise<boolean> {
    try {
      const testMessage: ChatMessage = {
        id: 'test',
        role: 'user',
        content: 'Hello',
        timestamp: new Date(),
      };

      await this.sendMessage([testMessage], [], { maxTokens: 10 });
      return true;
    } catch (error) {
      console.error(`Connection test failed for ${this.name}:`, error);
      return false;
    }
  }

  /**
   * Get error handling metrics for this provider
   */
  public getErrorMetrics() {
    return this.errorHandler.getMetricsSnapshot();
  }

  /**
   * Get circuit breaker status for this provider
   */
  public getCircuitBreakerStatus() {
    return this.errorHandler.getCircuitBreakerMetrics();
  }

  /**
   * Reset circuit breakers for this provider
   */
  public resetCircuitBreakers(): void {
    this.errorHandler.resetCircuitBreakers();
  }

  /**
   * Update error handling configuration
   */
  public updateErrorHandlingConfig(config: Partial<ErrorHandlingConfiguration>): void {
    this.errorHandler.updateConfig(config);
  }

  /**
   * Get current error handling configuration
   */
  public getErrorHandlingConfig(): ErrorHandlingConfiguration {
    return this.errorHandler.getConfig();
  }

  /**
   * Get recent error logs for this provider
   */
  public getRecentErrorLogs(count: number = 50) {
    return this.errorHandler.getRecentLogs(count).filter(
      log => log.context?.provider === this.name
    );
  }

  public async getAvailableModels(): Promise<string[]> {
    // Default implementation returns empty array
    // Subclasses can override to fetch from API
    return [];
  }
}
