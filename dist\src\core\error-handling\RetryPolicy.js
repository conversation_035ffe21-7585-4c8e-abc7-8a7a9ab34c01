"use strict";
/**
 * Retry Policy System with Exponential Backoff and Jitter
 *
 * Provides configurable retry mechanisms with:
 * - Exponential backoff with customizable base delay and multiplier
 * - Jitter implementation (full jitter and decorrelated jitter)
 * - Maximum retry attempts and delay caps
 * - Conditional retry logic based on error types
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RetryPolicy = void 0;
class RetryPolicy {
    config;
    lastDelay = 0; // For decorrelated jitter
    constructor(config = {}) {
        this.config = {
            maxAttempts: 3,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 60000,
            backoffStrategy: 'exponential',
            enableJitter: true,
            jitterType: 'full',
            ...config
        };
    }
    /**
     * Execute a function with retry logic
     */
    async execute(operation, operationName) {
        const startTime = Date.now();
        const attempts = [];
        let lastError;
        for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
            const attemptStart = Date.now();
            const attemptInfo = {
                attempt,
                delay: 0,
                timestamp: new Date(attemptStart)
            };
            try {
                // Add timeout wrapper if specified
                const result = this.config.attemptTimeout
                    ? await this.withTimeout(operation(), this.config.attemptTimeout)
                    : await operation();
                attemptInfo.duration = Date.now() - attemptStart;
                attempts.push(attemptInfo);
                return {
                    success: true,
                    result,
                    attempts,
                    totalDuration: Date.now() - startTime,
                    finalAttempt: attempt
                };
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                attemptInfo.error = lastError;
                attemptInfo.duration = Date.now() - attemptStart;
                // Check if we should retry this error
                if (!this.shouldRetryError(lastError, attempt)) {
                    attempts.push(attemptInfo);
                    break;
                }
                // If this is the last attempt, don't delay
                if (attempt === this.config.maxAttempts) {
                    attempts.push(attemptInfo);
                    break;
                }
                // Calculate delay for next attempt
                const delay = this.calculateDelay(attempt);
                attemptInfo.delay = delay;
                attempts.push(attemptInfo);
                // Wait before next attempt
                await this.delay(delay);
            }
        }
        return {
            success: false,
            error: lastError,
            attempts,
            totalDuration: Date.now() - startTime,
            finalAttempt: attempts.length
        };
    }
    /**
     * Calculate delay with exponential backoff and jitter
     */
    calculateDelay(attempt) {
        // Calculate base exponential backoff
        const exponentialDelay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1);
        const cappedDelay = Math.min(exponentialDelay, this.config.maxDelay);
        if (!this.config.enableJitter) {
            return cappedDelay;
        }
        return this.applyJitter(cappedDelay);
    }
    /**
     * Apply jitter to delay
     */
    applyJitter(delay) {
        switch (this.config.jitterType) {
            case 'full':
                // Full jitter: random(0, delay)
                return Math.random() * delay;
            case 'decorrelated':
                // Decorrelated jitter: random(baseDelay, lastDelay * 3)
                const min = this.config.baseDelay;
                const max = Math.min(this.lastDelay * 3, this.config.maxDelay);
                const jitteredDelay = Math.random() * (max - min) + min;
                this.lastDelay = jitteredDelay;
                return jitteredDelay;
            default:
                return delay;
        }
    }
    /**
     * Determine if an error should trigger a retry
     */
    shouldRetryError(error, attempt) {
        // Use custom retry condition if provided
        if (this.config.shouldRetry) {
            return this.config.shouldRetry(error, attempt);
        }
        // Default retry logic - will be enhanced by ErrorClassifier
        return this.isTransientError(error);
    }
    /**
     * Basic transient error detection (will be enhanced by ErrorClassifier)
     */
    isTransientError(error) {
        const message = error.message.toLowerCase();
        const transientPatterns = [
            'timeout',
            'network',
            'connection',
            'econnreset',
            'enotfound',
            'econnrefused',
            'socket hang up',
            'rate limit',
            'too many requests',
            'service unavailable',
            'internal server error',
            'bad gateway',
            'gateway timeout'
        ];
        return transientPatterns.some(pattern => message.includes(pattern));
    }
    /**
     * Add timeout to a promise
     */
    withTimeout(promise, timeoutMs) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Operation timed out after ${timeoutMs}ms`));
            }, timeoutMs);
            promise
                .then(result => {
                clearTimeout(timeoutId);
                resolve(result);
            })
                .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    /**
     * Simple delay utility
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Update retry policy configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Create a new RetryPolicy with different configuration
     */
    static create(config = {}) {
        return new RetryPolicy(config);
    }
    /**
     * Predefined retry policies for common scenarios
     */
    static presets = {
        /** Fast retry for quick operations */
        fast: new RetryPolicy({
            maxAttempts: 3,
            baseDelay: 500,
            backoffMultiplier: 1.5,
            maxDelay: 5000
        }),
        /** Standard retry for most operations */
        standard: new RetryPolicy({
            maxAttempts: 3,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 30000
        }),
        /** Aggressive retry for critical operations */
        aggressive: new RetryPolicy({
            maxAttempts: 5,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 60000
        }),
        /** Conservative retry for expensive operations */
        conservative: new RetryPolicy({
            maxAttempts: 2,
            baseDelay: 2000,
            backoffMultiplier: 3,
            maxDelay: 30000
        })
    };
}
exports.RetryPolicy = RetryPolicy;
//# sourceMappingURL=RetryPolicy.js.map