/**
 * Error Classification System
 *
 * Categorizes errors into different types and determines retry strategies:
 * - Transient vs Permanent errors
 * - Network, Authentication, Rate Limiting, etc.
 * - Configurable retry rules per error type
 */
export declare enum ErrorCategory {
    NETWORK_ERROR = "NETWORK_ERROR",
    TIMEOUT_ERROR = "TIMEOUT_ERROR",
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
    SERVER_ERROR = "SERVER_ERROR",
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    NOT_FOUND_ERROR = "NOT_FOUND_ERROR",
    MALFORMED_REQUEST = "MALFORMED_REQUEST",
    CIRCUIT_BREAKER_ERROR = "CIRCUIT_BREAKER_ERROR",
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
export interface ErrorClassificationRule {
    category: ErrorCategory;
    shouldRetry: boolean;
    shouldCircuitBreak: boolean;
    patterns: {
        messagePatterns?: string[];
        statusCodes?: number[];
        errorTypes?: string[];
        customMatcher?: (error: Error) => boolean;
    };
    retryConfig?: {
        maxAttempts?: number;
        baseDelay?: number;
        backoffMultiplier?: number;
        maxDelay?: number;
    };
}
export interface ErrorClassification {
    category: ErrorCategory;
    shouldRetry: boolean;
    shouldCircuitBreak: boolean;
    confidence: number;
    matchedRule?: ErrorClassificationRule;
    suggestedAction?: string;
}
export declare class ErrorClassifier {
    private rules;
    constructor();
    /**
     * Classify an error and determine retry strategy
     */
    classify(error: Error): ErrorClassification;
    /**
     * Check if error matches a specific rule
     */
    private matchRule;
    /**
     * Initialize default error classification rules
     */
    private initializeDefaultRules;
    /**
     * Get suggested action for error category
     */
    private getSuggestedAction;
    /**
     * Add custom classification rule
     */
    addRule(rule: ErrorClassificationRule): void;
    /**
     * Remove classification rule
     */
    removeRule(category: ErrorCategory): void;
    /**
     * Get all classification rules
     */
    getRules(): ErrorClassificationRule[];
    /**
     * Update existing rule
     */
    updateRule(category: ErrorCategory, updates: Partial<ErrorClassificationRule>): void;
    /**
     * Check if error should be retried based on classification
     */
    shouldRetry(error: Error): boolean;
    /**
     * Check if error should trigger circuit breaker
     */
    shouldCircuitBreak(error: Error): boolean;
    /**
     * Get retry configuration for error
     */
    getRetryConfig(error: Error): ErrorClassificationRule['retryConfig'] | undefined;
}
//# sourceMappingURL=ErrorClassifier.d.ts.map