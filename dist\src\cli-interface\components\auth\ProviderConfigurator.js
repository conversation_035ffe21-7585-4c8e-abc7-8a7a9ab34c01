"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderConfigurator = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
const types_1 = require("../../../core/types");
class ProviderConfigurator extends BaseComponent_1.BaseComponent {
    configManager;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
    }
    async render() {
        // This component is used inline, no standalone render needed
    }
    async configureProvider(providerName) {
        const provider = types_1.SUPPORTED_PROVIDERS[providerName];
        if (!provider) {
            await this.showError(`Unknown provider: ${providerName}`);
            return false;
        }
        console.log(this.utils.formatHeader(`🔧 Configuring ${provider.displayName}`));
        try {
            // Check if provider is already configured, if not, use undefined
            let existingConfig;
            try {
                existingConfig = this.configManager.getProviderConfig(providerName);
            }
            catch (error) {
                // Provider not configured yet, which is expected for new configurations
                existingConfig = undefined;
            }
            const configuration = await this.gatherProviderConfiguration(provider, existingConfig);
            if (!configuration) {
                return false; // User cancelled
            }
            await this.saveProviderConfiguration(providerName, configuration);
            await this.handleFirstProviderSetup(providerName);
            await this.showSuccess(`${provider.displayName} configured successfully!`);
            return true;
        }
        catch (error) {
            await this.showError(`Failed to configure provider: ${error}`);
            return false;
        }
    }
    async gatherProviderConfiguration(provider, existingConfig) {
        const questions = [
            {
                type: 'password',
                name: 'apiKey',
                message: `Enter your ${provider.displayName} API key:`,
                default: existingConfig?.apiKey,
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'API key is required';
                    }
                    return true;
                },
                mask: '*',
            },
            {
                type: 'list',
                name: 'defaultModel',
                message: 'Choose default model:',
                choices: provider.models.map((model) => ({
                    name: this.formatModelChoice(model),
                    value: model,
                })),
                default: existingConfig?.defaultModel || provider.models[0],
            },
        ];
        // Add base URL configuration for providers that support it
        if (provider.baseUrl) {
            questions.push({
                type: 'input',
                name: 'baseUrl',
                message: 'Base URL (optional):',
                default: existingConfig?.baseUrl || provider.baseUrl,
                validate: (input) => {
                    if (input && !this.isValidUrl(input)) {
                        return 'Please enter a valid URL';
                    }
                    return true;
                },
            });
        }
        // Add advanced configuration option
        questions.push({
            type: 'confirm',
            name: 'configureAdvanced',
            message: 'Configure advanced settings?',
            default: false,
        });
        const answers = await inquirer_1.default.prompt(questions);
        if (answers.configureAdvanced) {
            const advancedConfig = await this.gatherAdvancedConfiguration();
            Object.assign(answers, advancedConfig);
        }
        return answers;
    }
    async gatherAdvancedConfiguration() {
        return await inquirer_1.default.prompt([
            {
                type: 'number',
                name: 'maxTokens',
                message: 'Maximum tokens (optional):',
                validate: (input) => {
                    if (input && (input < 1 || input > 100000)) {
                        return 'Max tokens must be between 1 and 100000';
                    }
                    return true;
                },
            },
            {
                type: 'number',
                name: 'temperature',
                message: 'Temperature (0.0 - 2.0, optional):',
                validate: (input) => {
                    if (input && (input < 0 || input > 2)) {
                        return 'Temperature must be between 0.0 and 2.0';
                    }
                    return true;
                },
            },
            {
                type: 'input',
                name: 'systemPrompt',
                message: 'Custom system prompt (optional):',
            },
        ]);
    }
    async saveProviderConfiguration(providerName, configuration) {
        await this.configManager.setProvider(providerName, {
            apiKey: configuration.apiKey,
            defaultModel: configuration.defaultModel,
            baseUrl: configuration.baseUrl,
            maxTokens: configuration.maxTokens,
            temperature: configuration.temperature,
            enabled: true,
            advanced: configuration.advanced || {}
        });
        // Save advanced configuration if provided
        if (configuration.maxTokens || configuration.temperature || configuration.systemPrompt) {
            const config = this.configManager.getConfig();
            if (!config.providers[providerName].advanced) {
                config.providers[providerName].advanced = {};
            }
            if (configuration.maxTokens) {
                config.providers[providerName].advanced.maxTokens = configuration.maxTokens;
            }
            if (configuration.temperature) {
                config.providers[providerName].advanced.temperature = configuration.temperature;
            }
            if (configuration.systemPrompt) {
                config.providers[providerName].advanced.systemPrompt = configuration.systemPrompt;
            }
            await this.configManager.saveConfig();
        }
    }
    async handleFirstProviderSetup(providerName) {
        const configuredProviders = this.configManager.getConfiguredProviders();
        // Set as default if it's the first provider
        if (configuredProviders.length === 1) {
            await this.configManager.setDefaultProvider(providerName);
            this.utils.showInfo(`Set ${types_1.SUPPORTED_PROVIDERS[providerName].displayName} as default provider`);
        }
    }
    async testProviderConnection(providerName) {
        this.utils.startSpinner(`Testing connection to ${types_1.SUPPORTED_PROVIDERS[providerName].displayName}...`);
        try {
            // TODO: Implement actual connection test
            // This would involve creating a provider client and making a test request
            await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate test
            this.utils.stopSpinner(true, 'Connection test successful');
            return true;
        }
        catch (error) {
            this.utils.stopSpinner(false, `Connection test failed: ${error}`);
            return false;
        }
    }
    formatModelChoice(model) {
        // Add model descriptions or capabilities if available
        const modelDescriptions = {
            'gpt-4': 'Most capable, best for complex tasks',
            'gpt-4-turbo': 'Fast and capable, good balance',
            'gpt-3.5-turbo': 'Fast and efficient, good for simple tasks',
            'claude-3-opus-20240229': 'Most capable Claude model',
            'claude-3-sonnet-20240229': 'Balanced performance and speed',
            'claude-3-haiku-20240307': 'Fastest Claude model',
            'gemini-pro': 'Google\'s most capable model',
            'gemini-pro-vision': 'Supports images and text',
            'deepseek-chat': 'General conversation model',
            'deepseek-coder': 'Specialized for coding tasks',
        };
        const description = modelDescriptions[model];
        return description ? `${model} - ${description}` : model;
    }
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.ProviderConfigurator = ProviderConfigurator;
//# sourceMappingURL=ProviderConfigurator.js.map