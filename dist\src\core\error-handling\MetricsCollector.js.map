{"version": 3, "file": "MetricsCollector.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/MetricsCollector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAgEH,MAAa,gBAAgB;IACnB,WAAW,GAAmF,EAAE,CAAC;IACjG,oBAAoB,GAAwF,EAAE,CAAC;IAC/G,WAAW,GAAiI,EAAE,CAAC;IAC/I,iBAAiB,GAAsF,EAAE,CAAC;IAE1G,SAAS,GAAS,IAAI,IAAI,EAAE,CAAC;IAC7B,gBAAgB,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC1C,gBAAgB,GAAW,CAAC,CAAC;IAErC;;OAEG;IACH,kBAAkB,CAAC,SAAiB,EAAE,OAAe,EAAE,KAAa,EAAE,OAAgB;QACpF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO;YACP,KAAK;YACL,SAAS;SACV,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,+BAA+B,CAAC,WAAmB,EAAE,QAAgB,EAAE,QAAgB;QACrF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,WAAW;YACX,QAAQ;YACR,QAAQ;SACT,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAgB,EAAE,OAAe,EAAE,SAAiB;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ;YACR,OAAO;YACP,SAAS;YACT,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAAe;QACnC,8CAA8C;QAC9C,8DAA8D;QAC9D,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC1F,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB,EAAE,QAAgB,EAAE,OAAgB;QACrE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS;YACT,QAAQ;YACR,OAAO;SACR,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAmB;QAChC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,WAAmB;QAC9B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAAa;QAC/B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;QAC9C,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC;QAEpD,OAAO;YACL,SAAS,EAAE,GAAG;YACd,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACzC,eAAe,EAAE,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC;YAC5D,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YAC1C,WAAW,EAAE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;YACrD,YAAY,EAAE,IAAI,CAAC,4BAA4B,EAAE;SAClD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,MAAY;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;QAElF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,iBAAiB,EAAE,CAAC;gBACpB,aAAa,EAAE,CAAC;gBAChB,iBAAiB,EAAE,CAAC;gBACpB,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;aACpB,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC/D,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE/C,OAAO;YACL,aAAa,EAAE,aAAa,CAAC,MAAM;YACnC,iBAAiB,EAAE,UAAU;YAC7B,aAAa,EAAE,MAAM;YACrB,iBAAiB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;YACpE,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;YAClC,gBAAgB,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,MAAY;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;QAC1F,MAAM,cAAc,GAAG,IAAI,GAAG,EAAiC,CAAC;QAEhE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE;oBACpC,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,YAAY,EAAE,KAAK,CAAC,QAAQ;oBAC5B,iBAAiB,EAAE,CAAC;oBACpB,UAAU,EAAE,CAAC;oBACb,cAAc,EAAE,CAAC;oBACjB,YAAY,EAAE,CAAC;oBACf,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,KAAK,CAAC,SAAS;iBACjC,CAAC,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAE,CAAC;YACvD,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;YACtC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;QAC5C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,MAAY;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;QAEjF,MAAM,gBAAgB,GAA2B,EAAE,CAAC;QACpD,MAAM,eAAe,GAA2B,EAAE,CAAC;QACnD,MAAM,iBAAiB,GAA2B,EAAE,CAAC;QAErD,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/E,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3E,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBAC3C,mBAAmB,IAAI,KAAK,CAAC,cAAc,CAAC;gBAC5C,cAAc,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC,MAAM,CAAC;QAEjG,OAAO;YACL,WAAW,EAAE,YAAY,CAAC,MAAM;YAChC,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,SAAS,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1E,0BAA0B,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;SAC1F,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,MAAY;QAC9C,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;QAE5F,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,UAAU,EAAE,CAAC;gBACb,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;gBAChD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;aACxC,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/E,MAAM,iBAAiB,GAAG,CAAC,CAAC;QAE5B,OAAO;YACL,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;YAC5E,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC;YACpE,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC;YACpE,UAAU,EAAE,iBAAiB,CAAC,MAAM,GAAG,iBAAiB;YACxD,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAChD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;SACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAErD,4DAA4D;QAC5D,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,kBAAkB,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAEhF,kDAAkD;QAClD,IAAI,WAAW,GAAG,GAAG,CAAC;QAEtB,mCAAmC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACjD,IAAI,cAAc,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG;YAAE,WAAW,IAAI,EAAE,CAAC;QAC7D,IAAI,cAAc,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG;YAAE,WAAW,IAAI,EAAE,CAAC;QAE7D,qCAAqC;QACrC,IAAI,kBAAkB,GAAG,EAAE;YAAE,WAAW,IAAI,EAAE,CAAC;QAC/C,IAAI,kBAAkB,GAAG,EAAE;YAAE,WAAW,IAAI,EAAE,CAAC;QAE/C,OAAO;YACL,MAAM;YACN,WAAW,EAAE,kBAAkB;YAC/B,QAAQ,EAAE,CAAC,EAAE,mCAAmC;YAChD,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC7C,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC;CACF;AAhUD,4CAgUC"}