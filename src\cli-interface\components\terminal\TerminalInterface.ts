import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig, ProgressIndicator } from '../../types';
import { CLIProgressIndicator } from '../common/ProgressIndicator';

export class TerminalInterface extends BaseComponent {
  private progressIndicator: CLIProgressIndicator;

  constructor(state: CLIState, config: CLIConfig) {
    super(state, config);
    this.progressIndicator = new CLIProgressIndicator(config.theme);
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.renderHeader();
    this.renderStatusBar();
    console.log();
  }

  private renderHeader(): void {
    const title = '🤖 AI CLI Terminal';
    const subtitle = 'Interactive AI Assistant';
    
    this.utils.showBanner(title, subtitle);
  }

  private renderStatusBar(): void {
    const statusItems = [
      `Provider: ${this.utils.colorize(this.state.currentProvider, this.config.theme.accent)}`,
      `Model: ${this.utils.colorize(this.state.currentModel, this.config.theme.accent)}`,
      `Status: ${this.utils.colorize('Connected', this.config.theme.success)}`,
    ];

    const statusBar = '│ ' + statusItems.join(' │ ') + ' │';
    const separator = '─'.repeat(statusBar.length - 2);
    
    console.log(this.utils.colorize('┌' + separator + '┐', this.config.theme.muted));
    console.log(this.utils.colorize(statusBar, this.config.theme.muted));
    console.log(this.utils.colorize('└' + separator + '┘', this.config.theme.muted));
  }

  public renderPrompt(): string {
    const promptSymbol = this.utils.colorize('❯', this.config.theme.primary);
    return `${promptSymbol} `;
  }

  public renderTypingIndicator(): void {
    this.utils.startSpinner('AI is thinking...');
  }

  public stopTypingIndicator(): void {
    this.utils.stopSpinner();
  }

  public renderError(error: string): void {
    console.log();
    this.utils.showError(error);
    console.log();
  }

  public renderWarning(warning: string): void {
    console.log();
    this.utils.showWarning(warning);
    console.log();
  }

  public renderInfo(info: string): void {
    console.log();
    this.utils.showInfo(info);
    console.log();
  }

  public renderSeparator(): void {
    this.utils.printSeparator('─', 60);
  }

  /**
   * Show retry progress to user
   */
  public showRetryProgress(attempt: number, maxAttempts: number, delay: number, error: Error): void {
    const delaySeconds = Math.round(delay / 1000);
    this.progressIndicator.update(
      `Request failed (attempt ${attempt}/${maxAttempts}). Retrying in ${delaySeconds}s... (${error.message})`
    );
  }

  /**
   * Show operation progress
   */
  public showOperationProgress(message: string): void {
    this.progressIndicator.start(message);
  }

  /**
   * Update operation progress
   */
  public updateOperationProgress(message: string): void {
    this.progressIndicator.update(message);
  }

  /**
   * Show operation success
   */
  public showOperationSuccess(message?: string): void {
    this.progressIndicator.succeed(message);
  }

  /**
   * Show operation failure
   */
  public showOperationFailure(message?: string): void {
    this.progressIndicator.fail(message);
  }

  /**
   * Stop any ongoing progress indicators
   */
  public stopProgress(): void {
    this.progressIndicator.stop();
  }

  /**
   * Show circuit breaker status
   */
  public showCircuitBreakerStatus(serviceName: string, status: 'open' | 'half-open' | 'closed'): void {
    const statusMessages = {
      open: `⚠️  Service ${serviceName} is temporarily unavailable. Please try again later.`,
      'half-open': `🔄 Testing ${serviceName} service recovery...`,
      closed: `✅ ${serviceName} service recovered successfully.`
    };

    const message = statusMessages[status];
    if (status === 'open') {
      this.renderWarning(message);
    } else if (status === 'closed') {
      this.renderInfo(message);
    } else {
      this.renderInfo(message);
    }
  }

  /**
   * Show cancellation prompt
   */
  public async showCancellationPrompt(): Promise<boolean> {
    console.log();
    this.utils.showWarning('Operation is taking longer than expected.');

    const inquirer = require('inquirer');
    const { shouldCancel } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldCancel',
        message: 'Would you like to cancel this operation?',
        default: false
      }
    ]);

    return shouldCancel;
  }

  public async renderToolConfirmation(toolName: string, parameters: any): Promise<boolean> {
    console.log();
    console.log(this.utils.colorize('🔧 Tool Execution Request', this.config.theme.warning, true));
    console.log();
    console.log(`Tool: ${this.utils.colorize(toolName, this.config.theme.accent)}`);
    console.log('Parameters:');
    
    for (const [key, value] of Object.entries(parameters)) {
      const formattedValue = typeof value === 'string' && value.length > 100 
        ? this.utils.truncateText(value, 100)
        : JSON.stringify(value);
      
      console.log(`  ${this.utils.colorize(key, this.config.theme.secondary)}: ${formattedValue}`);
    }
    
    console.log();
    return await this.utils.confirmAction('Allow this tool execution?', false);
  }

  public renderToolResult(toolName: string, result: any, duration?: number): void {
    console.log();
    console.log(this.utils.colorize(`🔧 Tool Result: ${toolName}`, this.config.theme.success));
    
    if (duration) {
      console.log(this.utils.colorize(`⏱️  Duration: ${this.utils.formatDuration(duration)}`, this.config.theme.muted));
    }
    
    if (result.error) {
      console.log(this.utils.colorize(`❌ Error: ${result.error}`, this.config.theme.error));
    } else if (result.content) {
      console.log();
      console.log(result.content);
    }
    
    console.log();
  }

  public async renderStreamingResponse(content: string): Promise<void> {
    // For now, just print the content
    // In a full implementation, this would handle character-by-character streaming
    process.stdout.write(content);
  }

  public renderMessageTimestamp(timestamp: Date): string {
    if (!this.config.showTimestamps) {
      return '';
    }
    
    const timeStr = timestamp.toLocaleTimeString();
    return this.utils.colorize(`[${timeStr}]`, this.config.theme.muted) + ' ';
  }

  public renderTokenCount(tokens?: number): string {
    if (!this.config.showTokenCounts || !tokens) {
      return '';
    }
    
    return this.utils.colorize(`(${tokens} tokens)`, this.config.theme.muted) + ' ';
  }
}
