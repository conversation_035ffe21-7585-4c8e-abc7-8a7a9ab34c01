import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export declare class EditTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            file_path: {
                type: string;
                description: string;
            };
            old_string: {
                type: string;
                description: string;
            };
            new_string: {
                type: string;
                description: string;
            };
            expected_replacements: {
                type: string;
                description: string;
                minimum: number;
                default: number;
            };
            case_sensitive: {
                type: string;
                description: string;
                default: boolean;
            };
            whole_words_only: {
                type: string;
                description: string;
                default: boolean;
            };
            backup_original: {
                type: string;
                description: string;
                default: boolean;
            };
            dry_run: {
                type: string;
                description: string;
                default: boolean;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private performReplacement;
    private createBackup;
    private createDisplayContent;
}
//# sourceMappingURL=EditTool.d.ts.map