/**
 * Error Handling System - Main Export File
 *
 * Comprehensive error handling system with:
 * - Retry policies with exponential backoff and jitter
 * - Circuit breaker pattern for service protection
 * - Error classification and categorization
 * - Metrics collection and monitoring
 * - Structured logging and debugging
 * - Centralized error handling middleware
 */
export { RetryPolicy, RetryPolicyConfig, RetryResult, JitterType } from './RetryPolicy';
export { CircuitBreaker, CircuitBreakerConfig, CircuitBreakerState, CircuitBreakerRegistry, CircuitBreakerError } from './CircuitBreaker';
export { ErrorClassifier, ErrorCategory, ErrorClassification } from './ErrorClassifier';
export { ErrorHandlingMiddleware, ErrorHandlingContext, ErrorHandlingResult, ProgressCallback } from './ErrorHandlingMiddleware';
export { MetricsCollector, RetryMetrics, CircuitBreakerMetrics, ErrorMetrics, PerformanceMetrics, SystemHealthMetrics, MetricsSnapshot, MetricEvent } from './MetricsCollector';
export { ErrorLogger, LogLevel, LogEntry, LoggerConfig } from './ErrorLogger';
export { ErrorHandlingConfiguration, RetryConfiguration, CircuitBreakerConfiguration } from '../types';
export declare const DEFAULT_RETRY_CONFIG: {
    maxAttempts: number;
    baseDelay: number;
    backoffMultiplier: number;
    maxDelay: number;
    backoffStrategy: "exponential";
    enableJitter: boolean;
    jitterType: "full";
    attemptTimeout: number;
};
export declare const DEFAULT_CIRCUIT_BREAKER_CONFIG: {
    failureThreshold: number;
    recoveryTimeout: number;
    successThreshold: number;
    monitoringWindow: number;
    enabled: boolean;
};
export declare const PRESET_RETRY_POLICIES: {
    AGGRESSIVE: {
        maxAttempts: number;
        baseDelay: number;
        backoffMultiplier: number;
        maxDelay: number;
        backoffStrategy: "exponential";
        enableJitter: boolean;
        jitterType: "full";
    };
    CONSERVATIVE: {
        maxAttempts: number;
        baseDelay: number;
        backoffMultiplier: number;
        maxDelay: number;
        backoffStrategy: "exponential";
        enableJitter: boolean;
        jitterType: "decorrelated";
    };
    NETWORK_OPTIMIZED: {
        maxAttempts: number;
        baseDelay: number;
        backoffMultiplier: number;
        maxDelay: number;
        backoffStrategy: "exponential";
        enableJitter: boolean;
        jitterType: "full";
        attemptTimeout: number;
    };
    FILE_OPERATIONS: {
        maxAttempts: number;
        baseDelay: number;
        backoffMultiplier: number;
        maxDelay: number;
        backoffStrategy: "linear";
        enableJitter: boolean;
    };
};
/**
 * Create a complete error handling middleware with default configuration
 */
export declare function createErrorHandlingMiddleware(config?: Partial<ErrorHandlingConfiguration>): ErrorHandlingMiddleware;
/**
 * Create a retry policy with preset configuration
 */
export declare function createRetryPolicy(preset: keyof typeof PRESET_RETRY_POLICIES | RetryPolicyConfig): RetryPolicy;
/**
 * Create a circuit breaker with default configuration
 */
export declare function createCircuitBreaker(serviceName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker;
/**
 * Utility function to determine if an error is retryable
 */
export declare function isRetryableError(error: Error): boolean;
/**
 * Utility function to get error category
 */
export declare function getErrorCategory(error: Error): ErrorCategory;
/**
 * Utility function to create an error handling context
 */
export declare function createErrorHandlingContext(operationType: 'toolExecution' | 'providerCall' | 'fileOperation' | 'networkRequest' | 'general', operationName: string, options?: {
    serviceName?: string;
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
}): ErrorHandlingContext;
/**
 * Error handling system version and metadata
 */
export declare const ERROR_HANDLING_SYSTEM: {
    readonly version: "1.0.0";
    readonly name: "CLI Agent Error Handling System";
    readonly description: "Comprehensive error handling with retry policies, circuit breakers, and monitoring";
    readonly features: readonly ["Exponential backoff with jitter", "Circuit breaker pattern", "Error classification", "Metrics collection", "Structured logging", "Progress callbacks", "Cancellation support"];
};
//# sourceMappingURL=index.d.ts.map