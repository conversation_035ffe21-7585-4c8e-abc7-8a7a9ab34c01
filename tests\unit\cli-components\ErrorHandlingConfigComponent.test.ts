import { ErrorHandlingConfigComponent } from '../../../src/cli-interface/components/config/ErrorHandlingConfigComponent';
import { createMockCLIState, createMockCLIConfig, createMockConfigManager, createMockErrorHandler, createMockCLIUtils } from '../../utils/mocks';

// Mock the dependencies
jest.mock('../../../src/core/config/ConfigManager');
jest.mock('../../../src/core/error-handling');
jest.mock('../../../src/cli-interface/utils/CLIUtils');

describe('ErrorHandlingConfigComponent', () => {
  let component: ErrorHandlingConfigComponent;
  let mockConfigManager: any;
  let mockErrorHandler: any;
  let mockCLIUtils: any;
  let mockState: any;
  let mockConfig: any;

  beforeEach(() => {
    mockConfigManager = createMockConfigManager();
    mockErrorHandler = createMockErrorHandler();
    mockCLIUtils = createMockCLIUtils();
    mockState = createMockCLIState();
    mockConfig = createMockCLIConfig();

    component = new ErrorHandlingConfigComponent(
      mockConfigManager,
      mockErrorHandler,
      mockCLIUtils
    );
  });

  describe('constructor', () => {
    it('should create component with dependencies', () => {
      expect(component).toBeInstanceOf(ErrorHandlingConfigComponent);
    });
  });

  describe('render', () => {
    it('should display main configuration menu', async () => {
      mockCLIUtils.select.mockResolvedValue('retry');
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.clearScreen).toHaveBeenCalled();
      expect(mockCLIUtils.showBanner).toHaveBeenCalledWith('Error Handling Configuration');
      expect(mockCLIUtils.select).toHaveBeenCalledWith(
        'Select configuration area:',
        expect.arrayContaining([
          expect.objectContaining({ name: 'Retry Policy Configuration' }),
          expect.objectContaining({ name: 'Circuit Breaker Configuration' }),
          expect.objectContaining({ name: 'View Current Metrics' }),
          expect.objectContaining({ name: 'Configuration Presets' }),
          expect.objectContaining({ name: 'Test Error Handling' }),
          expect.objectContaining({ name: 'Back to Main Menu' })
        ])
      );
    });

    it('should handle retry policy configuration selection', async () => {
      mockCLIUtils.select.mockResolvedValue('retry');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result.action).toBe('navigate');
      expect(result.data.component).toBe('RetryPolicyConfig');
    });

    it('should handle circuit breaker configuration selection', async () => {
      mockCLIUtils.select.mockResolvedValue('circuitBreaker');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result.action).toBe('navigate');
      expect(result.data.component).toBe('CircuitBreakerConfig');
    });

    it('should handle metrics viewing selection', async () => {
      mockCLIUtils.select.mockResolvedValue('metrics');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result.action).toBe('navigate');
      expect(result.data.component).toBe('ErrorHandlingMetrics');
    });

    it('should handle presets selection', async () => {
      mockCLIUtils.select.mockResolvedValue('presets');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result.action).toBe('navigate');
      expect(result.data.component).toBe('ConfigurationPresets');
    });

    it('should handle test error handling selection', async () => {
      mockCLIUtils.select.mockResolvedValue('test');
      mockCLIUtils.confirm.mockResolvedValue(true);
      
      const result = await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Testing error handling system...');
      expect(result.action).toBe('stay');
    });

    it('should handle back to main menu selection', async () => {
      mockCLIUtils.select.mockResolvedValue('back');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result.action).toBe('back');
    });
  });

  describe('testErrorHandling', () => {
    it('should test network error scenario', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test') // Main menu selection
        .mockResolvedValueOnce('network'); // Test scenario selection
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Testing network error scenario...');
    });

    it('should test timeout error scenario', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('timeout');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Testing timeout error scenario...');
    });

    it('should test rate limit error scenario', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('rateLimit');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Testing rate limit error scenario...');
    });

    it('should test circuit breaker scenario', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('circuitBreaker');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Testing circuit breaker scenario...');
    });

    it('should handle test cancellation', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('network');
      
      mockCLIUtils.confirm.mockResolvedValue(false);
      
      const result = await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showInfo).toHaveBeenCalledWith('Test cancelled.');
      expect(result.action).toBe('stay');
    });
  });

  describe('error handling', () => {
    it('should handle configuration loading errors', async () => {
      mockConfigManager.getErrorHandlingConfig.mockRejectedValue(new Error('Config load failed'));
      
      const result = await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showError).toHaveBeenCalledWith('Failed to load error handling configuration: Config load failed');
      expect(result.action).toBe('back');
    });

    it('should handle test execution errors', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('network');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      mockErrorHandler.executeWithRetry.mockRejectedValue(new Error('Test failed'));
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showError).toHaveBeenCalledWith('Test failed: Test failed');
    });

    it('should handle user input errors', async () => {
      mockCLIUtils.select.mockRejectedValue(new Error('Input error'));
      
      const result = await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showError).toHaveBeenCalledWith('An error occurred: Input error');
      expect(result.action).toBe('back');
    });
  });

  describe('navigation', () => {
    it('should provide correct navigation data for retry policy', async () => {
      mockCLIUtils.select.mockResolvedValue('retry');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result).toEqual({
        action: 'navigate',
        data: {
          component: 'RetryPolicyConfig',
          props: {
            configManager: mockConfigManager,
            errorHandler: mockErrorHandler
          }
        }
      });
    });

    it('should provide correct navigation data for circuit breaker', async () => {
      mockCLIUtils.select.mockResolvedValue('circuitBreaker');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result).toEqual({
        action: 'navigate',
        data: {
          component: 'CircuitBreakerConfig',
          props: {
            configManager: mockConfigManager,
            errorHandler: mockErrorHandler
          }
        }
      });
    });

    it('should provide correct navigation data for metrics', async () => {
      mockCLIUtils.select.mockResolvedValue('metrics');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result).toEqual({
        action: 'navigate',
        data: {
          component: 'ErrorHandlingMetrics',
          props: {
            errorHandler: mockErrorHandler
          }
        }
      });
    });

    it('should provide correct navigation data for presets', async () => {
      mockCLIUtils.select.mockResolvedValue('presets');
      
      const result = await component.render(mockState, mockConfig);
      
      expect(result).toEqual({
        action: 'navigate',
        data: {
          component: 'ConfigurationPresets',
          props: {
            configManager: mockConfigManager,
            errorHandler: mockErrorHandler
          }
        }
      });
    });
  });

  describe('integration with error handler', () => {
    it('should use error handler for test operations', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('network');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      mockErrorHandler.executeWithRetry.mockResolvedValue('Test completed');
      
      await component.render(mockState, mockConfig);
      
      expect(mockErrorHandler.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'test-network-error'
      );
    });

    it('should display test results correctly', async () => {
      mockCLIUtils.select
        .mockResolvedValueOnce('test')
        .mockResolvedValueOnce('network');
      
      mockCLIUtils.confirm.mockResolvedValue(true);
      mockErrorHandler.executeWithRetry.mockResolvedValue('Test completed successfully');
      
      await component.render(mockState, mockConfig);
      
      expect(mockCLIUtils.showSuccess).toHaveBeenCalledWith('Test completed: Test completed successfully');
    });
  });
});
