{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;GAUG;;;AA6GH,sEAoBC;AAKD,8CAOC;AAKD,oDAMC;AAKD,4CAIC;AAKD,4CAIC;AAKD,gEAkBC;AA/LD,kBAAkB;AAClB,6CAAwF;AAA/E,0GAAA,WAAW,OAAA;AAAkC,yGAAA,UAAU,OAAA;AAChE,mDAM0B;AALxB,gHAAA,cAAc,OAAA;AAEd,qHAAA,mBAAmB,OAAA;AACnB,wHAAA,sBAAsB,OAAA;AACtB,qHAAA,mBAAmB,OAAA;AAErB,qDAAwF;AAA/E,kHAAA,eAAe,OAAA;AAAE,gHAAA,aAAa,OAAA;AAEvC,aAAa;AACb,qEAKmC;AAJjC,kIAAA,uBAAuB,OAAA;AAMzB,yBAAyB;AACzB,uDAS4B;AAR1B,oHAAA,gBAAgB,OAAA;AAUlB,6CAKuB;AAJrB,0GAAA,WAAW,OAAA;AACX,uGAAA,QAAQ,OAAA;AAYV,kCAAkC;AACrB,QAAA,oBAAoB,GAAG;IAClC,WAAW,EAAE,CAAC;IACd,SAAS,EAAE,IAAI;IACf,iBAAiB,EAAE,CAAC;IACpB,QAAQ,EAAE,KAAK;IACf,eAAe,EAAE,aAAsB;IACvC,YAAY,EAAE,IAAI;IAClB,UAAU,EAAE,MAAe;IAC3B,cAAc,EAAE,KAAK;CACtB,CAAC;AAEW,QAAA,8BAA8B,GAAG;IAC5C,gBAAgB,EAAE,CAAC;IACnB,eAAe,EAAE,KAAK;IACtB,gBAAgB,EAAE,CAAC;IACnB,gBAAgB,EAAE,KAAK;IACvB,OAAO,EAAE,IAAI;CACd,CAAC;AAEW,QAAA,qBAAqB,GAAG;IACnC,UAAU,EAAE;QACV,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,GAAG;QACd,iBAAiB,EAAE,GAAG;QACtB,QAAQ,EAAE,KAAK;QACf,eAAe,EAAE,aAAsB;QACvC,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,MAAe;KAC5B;IACD,YAAY,EAAE;QACZ,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI;QACf,iBAAiB,EAAE,CAAC;QACpB,QAAQ,EAAE,KAAK;QACf,eAAe,EAAE,aAAsB;QACvC,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,cAAuB;KACpC;IACD,iBAAiB,EAAE;QACjB,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI;QACf,iBAAiB,EAAE,CAAC;QACpB,QAAQ,EAAE,KAAK;QACf,eAAe,EAAE,aAAsB;QACvC,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,MAAe;QAC3B,cAAc,EAAE,KAAK;KACtB;IACD,eAAe,EAAE;QACf,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,GAAG;QACd,iBAAiB,EAAE,GAAG;QACtB,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,QAAiB;QAClC,YAAY,EAAE,KAAK;KACpB;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,6BAA6B,CAC3C,MAA4C;IAE5C,MAAM,aAAa,GAA+B;QAChD,KAAK,EAAE,4BAAoB;QAC3B,cAAc,EAAE,sCAA8B;QAC9C,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,IAAI;QACvB,gBAAgB,EAAE,IAAI;QACtB,iBAAiB,EAAE;YACjB,aAAa,EAAE,6BAAqB,CAAC,YAAY;YACjD,aAAa,EAAE,6BAAqB,CAAC,iBAAiB;YACtD,cAAc,EAAE,6BAAqB,CAAC,eAAe;YACrD,eAAe,EAAE,6BAAqB,CAAC,iBAAiB;SACzD;KACF,CAAC;IAEF,MAAM,YAAY,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,MAAM,EAAE,CAAC;IACrD,OAAO,IAAI,uBAAuB,CAAC,YAAY,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAC/B,MAA8D;IAE9D,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,IAAI,WAAW,CAAC,6BAAqB,CAAC,MAAM,CAAC,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,WAAmB,EACnB,MAAsC;IAEtC,MAAM,YAAY,GAAG,EAAE,GAAG,sCAA8B,EAAE,GAAG,MAAM,EAAE,CAAC;IACtE,OAAO,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAY;IAC3C,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,MAAM,cAAc,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,OAAO,cAAc,CAAC,WAAW,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAY;IAC3C,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,MAAM,cAAc,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,OAAO,cAAc,CAAC,QAAQ,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CACxC,aAAgG,EAChG,aAAqB,EACrB,OAKC;IAED,OAAO;QACL,aAAa;QACb,aAAa;QACb,WAAW,EAAE,OAAO,EAAE,WAAW;QACjC,MAAM,EAAE,OAAO,EAAE,MAAM;QACvB,SAAS,EAAE,OAAO,EAAE,SAAS;QAC7B,QAAQ,EAAE,OAAO,EAAE,QAAQ;KAC5B,CAAC;AACJ,CAAC;AAED;;GAEG;AACU,QAAA,qBAAqB,GAAG;IACnC,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE,iCAAiC;IACvC,WAAW,EAAE,oFAAoF;IACjG,QAAQ,EAAE;QACR,iCAAiC;QACjC,yBAAyB;QACzB,sBAAsB;QACtB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;KACvB;CACO,CAAC"}