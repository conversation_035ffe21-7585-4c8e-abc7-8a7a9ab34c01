export declare class StringUtils {
    /**
     * Truncate a string to a maximum length with ellipsis
     */
    static truncate(str: string, maxLength: number, ellipsis?: string): string;
    /**
     * Capitalize the first letter of a string
     */
    static capitalize(str: string): string;
    /**
     * Convert string to camelCase
     */
    static toCamelCase(str: string): string;
    /**
     * Convert string to kebab-case
     */
    static toKebabCase(str: string): string;
    /**
     * Convert string to snake_case
     */
    static toSnakeCase(str: string): string;
    /**
     * Convert string to PascalCase
     */
    static toPascalCase(str: string): string;
    /**
     * Remove extra whitespace and normalize line endings
     */
    static normalize(str: string): string;
    /**
     * Escape HTML special characters
     */
    static escapeHtml(str: string): string;
    /**
     * Unescape HTML special characters
     */
    static unescapeHtml(str: string): string;
    /**
     * Escape regex special characters
     */
    static escapeRegex(str: string): string;
    /**
     * Generate a random string
     */
    static random(length: number, charset?: string): string;
    /**
     * Generate a UUID v4
     */
    static uuid(): string;
    /**
     * Check if string is empty or only whitespace
     */
    static isEmpty(str: string | null | undefined): boolean;
    /**
     * Check if string is a valid email
     */
    static isEmail(str: string): boolean;
    /**
     * Check if string is a valid URL
     */
    static isUrl(str: string): boolean;
    /**
     * Check if string is numeric
     */
    static isNumeric(str: string): boolean;
    /**
     * Extract numbers from string
     */
    static extractNumbers(str: string): number[];
    /**
     * Count occurrences of substring
     */
    static countOccurrences(str: string, substring: string): number;
    /**
     * Replace all occurrences of substring
     */
    static replaceAll(str: string, search: string, replace: string): string;
    /**
     * Insert string at specific position
     */
    static insertAt(str: string, index: number, insertion: string): string;
    /**
     * Remove string at specific position and length
     */
    static removeAt(str: string, index: number, length?: number): string;
    /**
     * Reverse a string
     */
    static reverse(str: string): string;
    /**
     * Check if string is palindrome
     */
    static isPalindrome(str: string): boolean;
    /**
     * Calculate Levenshtein distance between two strings
     */
    static levenshteinDistance(str1: string, str2: string): number;
    /**
     * Calculate string similarity (0-1)
     */
    static similarity(str1: string, str2: string): number;
    /**
     * Find best match from array of strings
     */
    static findBestMatch(target: string, candidates: string[]): {
        match: string;
        score: number;
    } | null;
    /**
     * Wrap text to specified width
     */
    static wordWrap(str: string, width: number): string;
    /**
     * Pad string to specified length
     */
    static pad(str: string, length: number, char?: string, direction?: 'left' | 'right' | 'both'): string;
    /**
     * Extract text between delimiters
     */
    static extractBetween(str: string, start: string, end: string): string[];
    /**
     * Format template string with variables
     */
    static template(template: string, variables: Record<string, any>): string;
    /**
     * Convert string to title case
     */
    static toTitleCase(str: string): string;
    /**
     * Remove diacritics/accents from string
     */
    static removeDiacritics(str: string): string;
    /**
     * Slugify string for URLs
     */
    static slugify(str: string): string;
}
//# sourceMappingURL=StringUtils.d.ts.map