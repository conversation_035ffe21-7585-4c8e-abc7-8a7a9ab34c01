{"version": 3, "file": "AnthropicProvider.js", "sourceRoot": "", "sources": ["../../../../src/core/providers/AnthropicProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA0C;AAC1C,iDAA+E;AAG/E,MAAa,iBAAkB,SAAQ,2BAAY;IAC1C,IAAI,GAAG,WAAW,CAAC;IAClB,MAAM,CAAY;IAE1B,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,MAAM,GAAG,IAAI,aAAS,CAAC;YAC1B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;SAC7B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CACtB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,OAAO,IAAI,CAAC,wBAAwB,CAClC,KAAK,IAAI,EAAE;YACT,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,aAAa,GAAkC;gBACnD,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,GAAG,CAAC,aAAa,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;gBAC/C,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;aAC5D,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAElE,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;QAC9D,CAAC,EACD,aAAa,EACb;YACE,QAAQ,EAAE;gBACR,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,SAAS,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC;gBAC7B,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBACjD,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;aACvD;SACF,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,CAAC,aAAa,CACzB,QAAuB,EACvB,KAAkB,EAClB,OAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,MAAM,aAAa,GAAkC;gBACnD,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,cAAc,CAAC,SAAS;gBACpC,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,MAAM,EAAE,IAAI;gBACZ,GAAG,CAAC,aAAa,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;gBAC/C,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;aAC5D,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAEhE,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAU,EAAE,CAAC;YAC1B,IAAI,KAAK,GAAQ,IAAI,CAAC;YAEtB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9E,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B,WAAW,IAAI,IAAI,CAAC;oBACpB,MAAM,IAAI,CAAC;gBACb,CAAC;gBAED,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACpF,SAAS,CAAC,IAAI,CAAC;wBACb,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE;wBAC1B,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI;wBAC9B,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK;qBACjC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAClD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,qCAAqC,SAAS,CAAC,MAAM,yBAAyB,CAAC,CAAC;gBAC5F,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YACvF,CAAC;YAED,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE9F,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,EAAE;gBACtD,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,KAAK,EAAE,cAAc,CAAC,KAAK;gBAC3B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,gBAAgB,EAAE,KAAK,CAAC,aAAa;oBACrC,WAAW,EAAE,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa;iBACtD,CAAC,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,kBAAkB,EAAE,qDAAqD;aACrF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAA2B,EAAE,KAAa;QAChE,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,SAAS,GAAU,EAAE,CAAC;QAE1B,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC1B,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC;YACxB,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACrC,SAAS,CAAC,IAAI,CAAC;oBACb,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,CAAC,MAAM,yBAAyB,CAAC,CAAC;YACnF,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9F,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE;YAClD,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;gBACzC,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;gBAC9C,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa;aACxE,CAAC,CAAC,CAAC,SAAS;YACb,SAAS,EAAE,kBAAkB,EAAE,qDAAqD;YACpF,YAAY,EAAE,QAAQ,CAAC,WAAW;SACnC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,SAAgB;QACtC,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,KAAK;SACvB,CAAC,CAAC,CAAC;IACN,CAAC;IAES,cAAc,CAAC,QAAuB;QAC9C,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC7C,MAAM,iBAAiB,GAAU,EAAE,CAAC;QAEpC,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC1B,+CAA+C;gBAC/C,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;gBACnF,SAAS;YACX,CAAC;YAED,MAAM,SAAS,GAAQ;gBACrB,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;gBACrD,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,mBAAmB;YACnB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,GAAG,CAAC,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,0CAA0C;YAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAC9C,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;oBACjC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;wBACrB,IAAI,EAAE,UAAU;wBAChB,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,KAAK,EAAE,IAAI,CAAC,UAAU;qBACvB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,wBAAwB;YACxB,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,SAAS,CAAC,OAAO,GAAG,CAAC;wBACnB,IAAI,EAAE,aAAa;wBACnB,WAAW,EAAE,GAAG,CAAC,QAAQ,EAAE,UAAU;wBACrC,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;YACL,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,CAAC;IAC9C,CAAC;IAES,WAAW,CAAC,KAAkB;QACtC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE;gBAC5C,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,EAAE;aACzC;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAES,cAAc;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;CACF;AA/PD,8CA+PC"}