import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig, TerminalMessage } from '../../types';
export declare class Message<PERSON><PERSON>er extends BaseComponent {
    constructor(state: CLIState, config: CLIConfig);
    render(): Promise<void>;
    renderMessage(message: TerminalMessage, compact?: boolean): Promise<void>;
    private renderContent;
    private renderContentWithCodeBlocks;
    private renderToolCalls;
    private renderToolCall;
    private formatTimestamp;
    private getRoleIcon;
    private getRoleColor;
    private capitalizeRole;
    private formatContentLine;
    private containsCodeBlocks;
    private getToolStatusIcon;
    private getToolStatusColor;
    renderStreamingContent(content: string): Promise<void>;
    renderNewLine(): void;
}
//# sourceMappingURL=MessageRenderer.d.ts.map