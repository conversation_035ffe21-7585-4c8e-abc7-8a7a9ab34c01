{"version": 3, "file": "mocks.js", "sourceRoot": "", "sources": ["../../../tests/utils/mocks.ts"], "names": [], "mappings": ";;;AAAA,2CAAqC;AAIrC,iBAAiB;AACV,MAAM,kBAAkB,GAAG,CAAC,YAA+B,EAAE,EAAY,EAAE,CAAC,CAAC;IAClF,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,MAAM;IACnB,eAAe,EAAE,QAAQ;IACzB,SAAS,EAAE,kBAAkB;IAC7B,GAAG,SAAS;CACb,CAAC,CAAC;AANU,QAAA,kBAAkB,sBAM5B;AAEH,kBAAkB;AACX,MAAM,mBAAmB,GAAG,CAAC,YAAgC,EAAE,EAAa,EAAE,CAAC,CAAC;IACrF,KAAK,EAAE;QACL,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,OAAO;QAClB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,OAAO;QACnB,IAAI,EAAE,OAAO;KACd;IACD,GAAG,SAAS;CACb,CAAC,CAAC;AATU,QAAA,mBAAmB,uBAS7B;AAEH,kBAAkB;AACX,MAAM,mBAAmB,GAAG,CAAC,YAAgC,EAAE,EAAa,EAAE,CAAC,CAAC;IACrF,eAAe,EAAE,QAAQ;IACzB,SAAS,EAAE,oBAAoB;IAC/B,SAAS,EAAE,EAAE;IACb,KAAK,EAAE;QACL,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;QACrB,iBAAiB,EAAE,IAAI;QACvB,mBAAmB,EAAE,IAAI;QACzB,WAAW,EAAE,KAAK;QAClB,WAAW,EAAE,MAAM;QACnB,eAAe,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;QACtC,eAAe,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;KAChC;IACD,QAAQ,EAAE;QACR,uBAAuB,EAAE,IAAI;QAC7B,WAAW,EAAE,KAAK;QAClB,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QAC7B,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,EAAE;QAChB,cAAc,EAAE,EAAE;QAClB,cAAc,EAAE,EAAE;KACnB;IACD,EAAE,EAAE;QACF,KAAK,EAAE,SAAS;QAChB,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;QACrB,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE,IAAI;QACd,eAAe,EAAE,IAAI;KACtB;IACD,aAAa,EAAE;QACb,OAAO,EAAE,EAAE;QACX,UAAU,EAAE,CAAC,SAAS,CAAC;QACvB,cAAc,EAAE,SAAS;QACzB,eAAe,EAAE,SAAS;KAC3B;IACD,aAAa,EAAE,IAAA,qCAA6B,GAAE;IAC9C,WAAW,EAAE;QACX,uBAAuB,EAAE,IAAI;KAC9B;IACD,GAAG,SAAS;CACb,CAAC,CAAC;AA3CU,QAAA,mBAAmB,uBA2C7B;AAEH,oCAAoC;AAC7B,MAAM,6BAA6B,GAAG,CAAC,YAAiD,EAAE,EAA8B,EAAE,CAAC,CAAC;IACjI,KAAK,EAAE;QACL,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,KAAK;QACf,iBAAiB,EAAE,CAAC;QACpB,eAAe,EAAE,aAAa;QAC9B,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,MAAM;QAClB,cAAc,EAAE,KAAK;QACrB,GAAG,SAAS,CAAC,KAAK;KACnB;IACD,cAAc,EAAE;QACd,OAAO,EAAE,IAAI;QACb,gBAAgB,EAAE,CAAC;QACnB,eAAe,EAAE,KAAK;QACtB,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,KAAK;QACvB,eAAe,EAAE,KAAK;QACtB,oBAAoB,EAAE,EAAE;QACxB,eAAe,EAAE,EAAE;QACnB,wBAAwB,EAAE,IAAI;QAC9B,mBAAmB,EAAE,KAAK;QAC1B,GAAG,SAAS,CAAC,cAAc;KAC5B;IACD,kBAAkB,EAAE,IAAI;IACxB,aAAa,EAAE,IAAI;IACnB,iBAAiB,EAAE,IAAI;IACvB,gBAAgB,EAAE,IAAI;IACtB,iBAAiB,EAAE;QACjB,aAAa,EAAE;YACb,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;SACrB;QACD,aAAa,EAAE;YACb,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;SAChB;QACD,cAAc,EAAE;YACd,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,iBAAiB,EAAE,GAAG;SACvB;QACD,eAAe,EAAE;YACf,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;SAChB;KACF;IACD,GAAG,SAAS;CACb,CAAC,CAAC;AAtDU,QAAA,6BAA6B,iCAsDvC;AAEH,qBAAqB;AACd,MAAM,uBAAuB,GAAG,GAAG,EAAE,CAAC,CAAC;IAC5C,SAAS,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAA,2BAAmB,GAAE,CAAC;IAC3D,sBAAsB,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAA,qCAA6B,GAAE,CAAC;IAClF,yBAAyB,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACjE,wBAAwB,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAChE,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IAClD,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAA,2BAAmB,GAAE,CAAC;CAC/D,CAAC,CAAC;AAPU,QAAA,uBAAuB,2BAOjC;AAEH,qBAAqB;AACd,MAAM,sBAAsB,GAAG,GAAG,EAAE,CAAC,CAAC;IAC3C,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;QACtC,eAAe,EAAE,GAAG;QACpB,oBAAoB,EAAE,EAAE;QACxB,gBAAgB,EAAE,CAAC;QACnB,qBAAqB,EAAE,CAAC;QACxB,YAAY,EAAE;YACZ,kBAAkB,EAAE,EAAE;YACtB,iBAAiB,EAAE,EAAE;YACrB,aAAa,EAAE,CAAC;YAChB,iBAAiB,EAAE,IAAI;YACvB,wBAAwB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;YACpD,YAAY,EAAE;gBACZ,SAAS,EAAE,GAAG;gBACd,YAAY,EAAE,IAAI;gBAClB,aAAa,EAAE,qBAAqB;aACrC;SACF;QACD,qBAAqB,EAAE;YACrB,YAAY,EAAE,QAAQ;YACtB,gBAAgB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE;YAC3D,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,KAAK;YAC1B,YAAY,EAAE,EAAE;SACjB;QACD,iBAAiB,EAAE;YACjB,MAAM,EAAE;gBACN,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,mBAAmB,EAAE,IAAI;gBACzB,SAAS,EAAE,CAAC;gBACZ,oBAAoB,EAAE,QAAQ;aAC/B;SACF;QACD,kBAAkB,EAAE;YAClB,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,GAAG;YACzB,eAAe,EAAE,IAAI;YACrB,eAAe,EAAE,IAAI;YACrB,MAAM,EAAE;gBACN,iBAAiB,EAAE,QAAQ;gBAC3B,cAAc,EAAE,WAAW;gBAC3B,eAAe,EAAE,YAAY;aAC9B;YACD,mBAAmB,EAAE;gBACnB,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,EAAE;gBACZ,iBAAiB,EAAE,CAAC;aACrB;SACF;QACD,YAAY,EAAE,EAAE;KACjB,CAAC;IACF,YAAY,EAAE,cAAI,CAAC,EAAE,EAAE;IACvB,gBAAgB,EAAE,cAAI,CAAC,EAAE,EAAE;IAC3B,yBAAyB,EAAE,cAAI,CAAC,EAAE,EAAE;CACrC,CAAC,CAAC;AA1DU,QAAA,sBAAsB,0BA0DhC;AAEH,gBAAgB;AACT,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC,CAAC;IACvC,WAAW,EAAE,cAAI,CAAC,EAAE,EAAE;IACtB,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE;IACrB,WAAW,EAAE,cAAI,CAAC,EAAE,EAAE;IACtB,SAAS,EAAE,cAAI,CAAC,EAAE,EAAE;IACpB,WAAW,EAAE,cAAI,CAAC,EAAE,EAAE;IACtB,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE;IACnB,eAAe,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACvD,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;IACjB,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;IAC1C,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;IACjB,WAAW,EAAE,cAAI,CAAC,EAAE,EAAE;IACtB,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE;IAChB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;IACjB,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE;CACpB,CAAC,CAAC;AAfU,QAAA,kBAAkB,sBAe5B;AAEH,gBAAgB;AACT,MAAM,kBAAkB,GAAG,GAAG,EAAE,CAAC,CAAC;IACvC,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;IACvC,kBAAkB,EAAE,cAAI,CAAC,EAAE,EAAE;CAC9B,CAAC,CAAC;AAHU,QAAA,kBAAkB,sBAG5B;AAEH,4BAA4B;AAC5B,MAAa,SAAU,SAAQ,KAAK;IACE;IAAsB;IAA1D,YAAY,OAAe,EAAS,IAAa,EAAS,YAAqB,IAAI;QACjF,KAAK,CAAC,OAAO,CAAC,CAAC;QADmB,SAAI,GAAJ,IAAI,CAAS;QAAS,cAAS,GAAT,SAAS,CAAgB;QAEjF,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC1B,CAAC;CACF;AALD,8BAKC;AAED,MAAa,YAAa,SAAQ,KAAK;IACrC,YAAY,UAAkB,eAAe;QAC3C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AALD,oCAKC;AAED,MAAa,YAAa,SAAQ,KAAK;IACrC,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AALD,oCAKC;AAED,MAAa,cAAe,SAAQ,KAAK;IACvC,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,wCAKC"}