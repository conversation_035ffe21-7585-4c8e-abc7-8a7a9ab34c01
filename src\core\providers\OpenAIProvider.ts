import OpenAI from 'openai';
import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';

export class OpenAIProvider extends BaseProvider {
  public name = 'openai';
  private client: OpenAI;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateConfig();
    
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseUrl,
    });
  }

  public async sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage> {
    return this.executeWithErrorHandling(
      async () => {
        const requestOptions = this.getRequestOptions(options);
        const formattedMessages = this.formatMessages(messages);
        const formattedTools = this.formatTools(tools);

        const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
          model: requestOptions.model,
          messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
          max_tokens: requestOptions.maxTokens,
          temperature: requestOptions.temperature,
          ...(formattedTools.length > 0 && { tools: formattedTools }),
        };

        // Note: System prompt is now handled by AIEngine, not here
        // if (this.config.systemPrompt) {
        //   requestParams.messages.unshift({
        //     role: 'system',
        //     content: this.config.systemPrompt,
        //   });
        // }

        const response = await this.client.chat.completions.create(requestParams);

        // Debug: Log raw response
        console.log('OpenAIProvider: Raw API response:', JSON.stringify(response, null, 2));

        return this.processResponse(response);
      },
      'sendMessage',
      {
        metadata: {
          messageCount: messages.length,
          toolCount: tools?.length || 0,
          model: options?.model || this.config.defaultModel,
          maxTokens: options?.maxTokens || this.config.maxTokens
        }
      }
    );
  }

  public async *streamMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage> {
    // For streaming, we need to handle errors differently since it's a generator
    // We'll wrap the stream creation in error handling, but let the stream itself flow
    const streamOperation = await this.executeWithErrorHandling(
      async () => {
        const requestOptions = this.getRequestOptions(options);
        const formattedMessages = this.formatMessages(messages);
        const formattedTools = this.formatTools(tools);

        const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
          model: requestOptions.model,
          messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
          max_tokens: requestOptions.maxTokens,
          temperature: requestOptions.temperature,
          stream: true,
          ...(formattedTools.length > 0 && { tools: formattedTools }),
        };

        // Note: System prompt is now handled by AIEngine, not here
        // if (this.config.systemPrompt) {
        //   requestParams.messages.unshift({
        //     role: 'system',
        //     content: this.config.systemPrompt,
        //   });
        // }

        return await this.client.chat.completions.create(requestParams);
      },
      'streamMessage',
      {
        metadata: {
          messageCount: messages.length,
          toolCount: tools?.length || 0,
          model: options?.model || this.config.defaultModel,
          streaming: true
        }
      }
    );

    let fullContent = '';
    let toolCalls: any[] = [];
    let usage: any = null;

    try {
      for await (const chunk of streamOperation) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          fullContent += delta.content;
          yield delta.content;
        }

        if (delta?.tool_calls) {
          toolCalls.push(...delta.tool_calls);
        }

        if (chunk.usage) {
          usage = chunk.usage;
        }
      }

      // Debug: Log if content is empty
      if (!fullContent || fullContent.trim().length === 0) {
        console.warn('OpenAIProvider (stream): Received empty content from OpenAI');
        console.warn(`Tool calls: ${toolCalls.length}`);
      }

      const requestOptions = this.getRequestOptions(options);
      return this.createChatMessage(fullContent, 'assistant', {
        provider: this.name,
        model: requestOptions.model,
        usage: usage ? {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
        } : undefined,
        toolCalls: toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined,
      });
    } catch (error) {
      // Handle streaming errors
      throw this.handleError(error, 'streamMessage');
    }
  }

  private processResponse(response: OpenAI.Chat.Completions.ChatCompletion): ChatMessage {
    const choice = response.choices[0];
    const message = choice.message;

    const content = message.content || '';
    const toolCalls = message.tool_calls ? this.formatToolCalls(message.tool_calls) : undefined;

    // Debug: Log if content is empty OR if finish reason is tool_calls
    if (!content || content.trim().length === 0 || choice.finish_reason === 'tool_calls') {
      console.warn('OpenAIProvider: Debug response details:');
      console.warn(`Content: "${content}"`);
      console.warn(`Finish reason: ${choice.finish_reason}`);
      console.warn(`Raw tool_calls from API:`, message.tool_calls);
      console.warn(`tool_calls type:`, typeof message.tool_calls);
      console.warn(`tool_calls is array:`, Array.isArray(message.tool_calls));
      console.warn(`Formatted tool calls: ${toolCalls?.length || 0}`);
      if (toolCalls && toolCalls.length > 0) {
        console.warn(`Tool calls:`, toolCalls);
      }
    }

    return this.createChatMessage(content, 'assistant', {
      provider: this.name,
      model: response.model,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
      toolCalls,
      finishReason: choice.finish_reason,
    });
  }

  private formatToolCalls(toolCalls: any[]): any[] {
    return toolCalls.map(call => ({
      id: call.id,
      name: call.function.name,
      parameters: JSON.parse(call.function.arguments || '{}'),
    }));
  }

  protected formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formatted: any = {
        role: msg.role,
        content: msg.content,
      };

      // Handle tool calls in assistant messages
      if (msg.role === 'assistant' && msg.toolCalls) {
        formatted.tool_calls = msg.toolCalls.map((call: any) => ({
          id: call.id,
          type: 'function',
          function: {
            name: call.name,
            arguments: JSON.stringify(call.parameters),
          },
        }));
      }

      // Handle tool responses
      if (msg.role === 'tool') {
        formatted.tool_call_id = msg.metadata?.toolCallId;
      }

      return formatted;
    });
  }

  public async testConnection(): Promise<boolean> {
    try {
      const response = await this.executeWithErrorHandling(
        () => this.client.models.list(),
        'testConnection'
      );
      return response.data.length > 0;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.executeWithErrorHandling(
        () => this.client.models.list(),
        'getAvailableModels'
      );
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      console.error('Failed to fetch OpenAI models:', error);
      return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']; // Fallback to known models
    }
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.apiKey.startsWith('sk-')) {
      throw new Error('OpenAI API key should start with "sk-"');
    }
  }
}
