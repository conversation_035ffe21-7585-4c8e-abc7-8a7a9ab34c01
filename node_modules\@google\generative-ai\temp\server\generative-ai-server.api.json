{"metadata": {"toolPackage": "@microsoft/api-extractor", "toolVersion": "7.38.2", "schemaVersion": 1011, "oldestForwardsCompatibleVersion": 1001, "tsdocConfig": {"$schema": "https://developer.microsoft.com/json-schemas/tsdoc/v0/tsdoc.schema.json", "noStandardTags": true, "tagDefinitions": [{"tagName": "@alpha", "syntaxKind": "modifier"}, {"tagName": "@beta", "syntaxKind": "modifier"}, {"tagName": "@defaultValue", "syntaxKind": "block"}, {"tagName": "@decorator", "syntaxKind": "block", "allowMultiple": true}, {"tagName": "@deprecated", "syntaxKind": "block"}, {"tagName": "@eventProperty", "syntaxKind": "modifier"}, {"tagName": "@example", "syntaxKind": "block", "allowMultiple": true}, {"tagName": "@experimental", "syntaxKind": "modifier"}, {"tagName": "@inheritDoc", "syntaxKind": "inline"}, {"tagName": "@internal", "syntaxKind": "modifier"}, {"tagName": "@label", "syntaxKind": "inline"}, {"tagName": "@link", "syntaxKind": "inline", "allowMultiple": true}, {"tagName": "@override", "syntaxKind": "modifier"}, {"tagName": "@packageDocumentation", "syntaxKind": "modifier"}, {"tagName": "@param", "syntaxKind": "block", "allowMultiple": true}, {"tagName": "@privateRemarks", "syntaxKind": "block"}, {"tagName": "@public", "syntaxKind": "modifier"}, {"tagName": "@readonly", "syntaxKind": "modifier"}, {"tagName": "@remarks", "syntaxKind": "block"}, {"tagName": "@returns", "syntaxKind": "block"}, {"tagName": "@sealed", "syntaxKind": "modifier"}, {"tagName": "@see", "syntaxKind": "block"}, {"tagName": "@throws", "syntaxKind": "block", "allowMultiple": true}, {"tagName": "@typeParam", "syntaxKind": "block", "allowMultiple": true}, {"tagName": "@virtual", "syntaxKind": "modifier"}, {"tagName": "@betaDocumentation", "syntaxKind": "modifier"}, {"tagName": "@internalRemarks", "syntaxKind": "block"}, {"tagName": "@preapproved", "syntaxKind": "modifier"}], "supportForTags": {"@alpha": true, "@beta": true, "@defaultValue": true, "@decorator": true, "@deprecated": true, "@eventProperty": true, "@example": true, "@experimental": true, "@inheritDoc": true, "@internal": true, "@label": true, "@link": true, "@override": true, "@packageDocumentation": true, "@param": true, "@privateRemarks": true, "@public": true, "@readonly": true, "@remarks": true, "@returns": true, "@sealed": true, "@see": true, "@throws": true, "@typeParam": true, "@virtual": true, "@betaDocumentation": true, "@internalRemarks": true, "@preapproved": true}, "reportUnsupportedHtmlElements": false}}, "kind": "Package", "canonicalReference": "@google/generative-ai!", "docComment": "", "name": "@google/generative-ai", "preserveMemberOrder": false, "members": [{"kind": "EntryPoint", "canonicalReference": "@google/generative-ai!", "name": "", "preserveMemberOrder": false, "members": [{"kind": "Interface", "canonicalReference": "@google/generative-ai!CachedContent:interface", "docComment": "/**\n * Describes `CachedContent` interface for sending to the server (if creating) or received from the server (using getters or list methods).\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CachedContent extends "}, {"kind": "Reference", "text": "CachedContentBase", "canonicalReference": "@google/generative-ai!CachedContentBase:interface"}, {"kind": "Content", "text": " "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContent#createTime:member", "docComment": "/**\n * `CachedContent` creation time in ISO string format.\n */\n", "excerptTokens": [{"kind": "Content", "text": "createTime?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "createTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContent#name:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "name?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContent#ttl:member", "docComment": "/**\n * protobuf.Duration format (ex. \"3.0001s\").\n */\n", "excerptTokens": [{"kind": "Content", "text": "ttl?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "ttl", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContent#updateTime:member", "docComment": "/**\n * `CachedContent` update time in ISO string format.\n */\n", "excerptTokens": [{"kind": "Content", "text": "updateTime?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "updateTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": [{"startIndex": 1, "endIndex": 2}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CachedContentBase:interface", "docComment": "/**\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CachedContentBase "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "CachedContentBase", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#contents:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "contents: "}, {"kind": "Reference", "text": "Content", "canonicalReference": "@google/generative-ai!Content:interface"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "contents", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#displayName:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "displayName?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "displayName", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#expireTime:member", "docComment": "/**\n * Expiration time in ISO string format. Specify either this or `ttlSeconds` when creating a `CachedContent`.\n */\n", "excerptTokens": [{"kind": "Content", "text": "expireTime?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "expireTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#model:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "model?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "model", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#systemInstruction:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "systemInstruction?: "}, {"kind": "Content", "text": "string | "}, {"kind": "Reference", "text": "Part", "canonicalReference": "@google/generative-ai!Part:type"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "Content", "canonicalReference": "@google/generative-ai!Content:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "systemInstruction", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 5}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#toolConfig:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "toolConfig?: "}, {"kind": "Reference", "text": "ToolConfig", "canonicalReference": "@google/generative-ai!ToolConfig:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "toolConfig", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentBase#tools:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "tools?: "}, {"kind": "Reference", "text": "Tool", "canonicalReference": "@google/generative-ai!Tool:type"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "tools", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CachedContentCreateParams:interface", "docComment": "/**\n * Params to pass to {@link GoogleAICacheManager.create}.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CachedContentCreateParams extends "}, {"kind": "Reference", "text": "CachedContentBase", "canonicalReference": "@google/generative-ai!CachedContentBase:interface"}, {"kind": "Content", "text": " "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "CachedContentCreateParams", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentCreateParams#ttlSeconds:member", "docComment": "/**\n * `CachedContent` ttl in seconds. Specify either this or `expireTime` when creating a `CachedContent`.\n */\n", "excerptTokens": [{"kind": "Content", "text": "ttlSeconds?: "}, {"kind": "Content", "text": "number"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "ttlSeconds", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": [{"startIndex": 1, "endIndex": 2}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CachedContentUpdateInputFields:interface", "docComment": "/**\n * Fields that can be updated in an existing content cache.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CachedContentUpdateInputFields "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "CachedContentUpdateInputFields", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentUpdateInputFields#expireTime:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "expireTime?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "expireTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentUpdateInputFields#ttlSeconds:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "ttlSeconds?: "}, {"kind": "Content", "text": "number"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "ttlSeconds", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CachedContentUpdateParams:interface", "docComment": "/**\n * Params to pass to {@link GoogleAICacheManager.update}.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CachedContentUpdateParams "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "CachedContentUpdateParams", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentUpdateParams#cachedContent:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "cachedContent: "}, {"kind": "Reference", "text": "CachedContentUpdateInputFields", "canonicalReference": "@google/generative-ai!CachedContentUpdateInputFields:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "cachedContent", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CachedContentUpdateParams#updateMask:member", "docComment": "/**\n * protobuf FieldMask. If not specified, updates all provided fields.\n */\n", "excerptTokens": [{"kind": "Content", "text": "updateMask?: "}, {"kind": "Content", "text": "string[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "updateMask", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CodeExecutionResult:interface", "docComment": "/**\n * Result of executing the `ExecutableCode`. Only generated when using code execution, and always follows a `Part` containing the `ExecutableCode`.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CodeExecutionResult "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "CodeExecutionResult", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResult#outcome:member", "docComment": "/**\n * Outcome of the code execution.\n */\n", "excerptTokens": [{"kind": "Content", "text": "outcome: "}, {"kind": "Reference", "text": "Outcome", "canonicalReference": "@google/generative-ai!Outcome:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "outcome", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResult#output:member", "docComment": "/**\n * Contains stdout when code execution is successful, stderr or other description otherwise.\n */\n", "excerptTokens": [{"kind": "Content", "text": "output: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "output", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart:interface", "docComment": "/**\n * Content part containing the result of executed code.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CodeExecutionResultPart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "CodeExecutionResultPart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult: "}, {"kind": "Reference", "text": "CodeExecutionResult", "canonicalReference": "@google/generative-ai!CodeExecutionResult:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!CodeExecutionTool:interface", "docComment": "/**\n * Enables the model to execute code as part of generation.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface CodeExecutionTool "}], "fileUrlPath": "dist/types/requests.d.ts", "releaseTag": "Public", "name": "CodeExecutionTool", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!CodeExecutionTool#codeExecution:member", "docComment": "/**\n * Provide an empty object to enable code execution. This field may have subfields added in the future.\n */\n", "excerptTokens": [{"kind": "Content", "text": "codeExecution: "}, {"kind": "Content", "text": "{}"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "codeExecution", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!Content:interface", "docComment": "/**\n * Content type for both prompts and response candidates.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface Content "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "Content", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Content#parts:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "parts: "}, {"kind": "Reference", "text": "Part", "canonicalReference": "@google/generative-ai!Part:type"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "parts", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Content#role:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "role: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "role", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ErrorDetails:interface", "docComment": "/**\n * Details object that may be included in an error response.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ErrorDetails "}], "fileUrlPath": "dist/types/responses.d.ts", "releaseTag": "Public", "name": "ErrorDetails", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ErrorDetails#\"@type\":member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "\"@type\"?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "\"@type\"", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "IndexSignature", "canonicalReference": "@google/generative-ai!ErrorDetails:index(1)", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "[key: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": "]: "}, {"kind": "Content", "text": "unknown"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 4}, "releaseTag": "Public", "overloadIndex": 1, "parameters": [{"parameterName": "key", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}]}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ErrorDetails#domain:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "domain?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "domain", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ErrorDetails#metadata:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "metadata?: "}, {"kind": "Reference", "text": "Record", "canonicalReference": "!Record:type"}, {"kind": "Content", "text": "<string, unknown>"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "metadata", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ErrorDetails#reason:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "reason?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "reason", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ExecutableCode:interface", "docComment": "/**\n * Code generated by the model that is meant to be executed, where the result is returned to the model. Only generated when using the code execution tool, in which the code will be automatically executed, and a corresponding `CodeExecutionResult` will also be generated.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ExecutableCode "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "ExecutableCode", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCode#code:member", "docComment": "/**\n * The code to be executed.\n */\n", "excerptTokens": [{"kind": "Content", "text": "code: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "code", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCode#language:member", "docComment": "/**\n * Programming language of the `code`.\n */\n", "excerptTokens": [{"kind": "Content", "text": "language: "}, {"kind": "Reference", "text": "ExecutableCodeLanguage", "canonicalReference": "@google/generative-ai!ExecutableCodeLanguage:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "language", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Enum", "canonicalReference": "@google/generative-ai!ExecutableCodeLanguage:enum", "docComment": "/**\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare enum ExecutableCodeLanguage "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "ExecutableCodeLanguage", "preserveMemberOrder": false, "members": [{"kind": "EnumMember", "canonicalReference": "@google/generative-ai!ExecutableCodeLanguage.LANGUAGE_UNSPECIFIED:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "LANGUAGE_UNSPECIFIED = "}, {"kind": "Content", "text": "\"language_unspecified\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "LANGUAGE_UNSPECIFIED"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!ExecutableCodeLanguage.PYTHON:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "PYTHON = "}, {"kind": "Content", "text": "\"python\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "PYTHON"}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ExecutableCodePart:interface", "docComment": "/**\n * Content part containing executable code generated by the model.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ExecutableCodePart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "ExecutableCodePart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode: "}, {"kind": "Reference", "text": "ExecutableCode", "canonicalReference": "@google/generative-ai!ExecutableCode:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ExecutableCodePart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FileData:interface", "docComment": "/**\n * Data pointing to a file uploaded with the Files API.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FileData "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FileData", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileData#fileUri:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileUri: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "fileUri", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileData#mimeType:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "mimeType: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "mimeType", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FileDataPart:interface", "docComment": "/**\n * Content part interface if the part represents FileData.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FileDataPart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FileDataPart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData: "}, {"kind": "Reference", "text": "FileData", "canonicalReference": "@google/generative-ai!FileData:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileDataPart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FileMetadata:interface", "docComment": "/**\n * Metadata to provide alongside a file upload\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FileMetadata "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "FileMetadata", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadata#displayName:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "displayName?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "displayName", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadata#mimeType:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "mimeType: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "mimeType", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadata#name:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "name?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FileMetadataResponse:interface", "docComment": "/**\n * File metadata response from server.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FileMetadataResponse "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "FileMetadataResponse", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#createTime:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "createTime: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "createTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#displayName:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "displayName?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "displayName", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#error:member", "docComment": "/**\n * Error populated if file processing has failed.\n */\n", "excerptTokens": [{"kind": "Content", "text": "error?: "}, {"kind": "Reference", "text": "RpcStatus", "canonicalReference": "@google/generative-ai!RpcStatus:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "error", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#expirationTime:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "expirationTime: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "expirationTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#mimeType:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "mimeType: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "mimeType", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#name:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#sha256Hash:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "sha256Hash: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "sha256Hash", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#sizeBytes:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "sizeBytes: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "sizeBytes", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#state:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "state: "}, {"kind": "Reference", "text": "FileState", "canonicalReference": "@google/generative-ai!FileState:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "state", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#updateTime:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "updateTime: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "updateTime", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#uri:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "uri: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "uri", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FileMetadataResponse#videoMetadata:member", "docComment": "/**\n * Video metadata populated after processing is complete.\n */\n", "excerptTokens": [{"kind": "Content", "text": "videoMetadata?: "}, {"kind": "Reference", "text": "VideoMetadata", "canonicalReference": "@google/generative-ai!VideoMetadata:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "videoMetadata", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Enum", "canonicalReference": "@google/generative-ai!FileState:enum", "docComment": "/**\n * Processing state of the `File`.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare enum FileState "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "FileState", "preserveMemberOrder": false, "members": [{"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FileState.ACTIVE:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "ACTIVE = "}, {"kind": "Content", "text": "\"ACTIVE\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "ACTIVE"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FileState.FAILED:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "FAILED = "}, {"kind": "Content", "text": "\"FAILED\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "FAILED"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FileState.PROCESSING:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "PROCESSING = "}, {"kind": "Content", "text": "\"PROCESSING\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "PROCESSING"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FileState.STATE_UNSPECIFIED:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "STATE_UNSPECIFIED = "}, {"kind": "Content", "text": "\"STATE_UNSPECIFIED\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "STATE_UNSPECIFIED"}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionCall:interface", "docComment": "/**\n * A predicted [FunctionCall] returned from the model that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing the parameters and their values.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionCall "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FunctionCall", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCall#args:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "args: "}, {"kind": "Content", "text": "object"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "args", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCall#name:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionCallingConfig:interface", "docComment": "/**\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionCallingConfig "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionCallingConfig", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallingConfig#allowedFunctionNames:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "allowedFunctionNames?: "}, {"kind": "Content", "text": "string[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "allowedFunctionNames", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallingConfig#mode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "mode?: "}, {"kind": "Reference", "text": "FunctionCallingMode", "canonicalReference": "@google/generative-ai!FunctionCallingMode:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "mode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Enum", "canonicalReference": "@google/generative-ai!FunctionCallingMode:enum", "docComment": "/**\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare enum FunctionCallingMode "}], "fileUrlPath": "dist/types/enums.d.ts", "releaseTag": "Public", "name": "FunctionCallingMode", "preserveMemberOrder": false, "members": [{"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionCallingMode.ANY:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "ANY = "}, {"kind": "Content", "text": "\"ANY\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "ANY"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionCallingMode.AUTO:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "AUTO = "}, {"kind": "Content", "text": "\"AUTO\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "AUTO"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionCallingMode.MODE_UNSPECIFIED:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "MODE_UNSPECIFIED = "}, {"kind": "Content", "text": "\"MODE_UNSPECIFIED\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "MODE_UNSPECIFIED"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionCallingMode.NONE:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "NONE = "}, {"kind": "Content", "text": "\"NONE\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "NONE"}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionCallPart:interface", "docComment": "/**\n * Content part interface if the part represents a FunctionCall.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionCallPart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FunctionCallPart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall: "}, {"kind": "Reference", "text": "FunctionCall", "canonicalReference": "@google/generative-ai!FunctionCall:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionCallPart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionDeclaration:interface", "docComment": "/**\n * Structured representation of a function declaration as defined by the [OpenAPI 3.0 specification](https://spec.openapis.org/oas/v3.0.3). Included in this declaration are the function name and parameters. This FunctionDeclaration is a representation of a block of code that can be used as a Tool by the model and executed by the client.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare interface FunctionDeclaration "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionDeclaration", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclaration#description:member", "docComment": "/**\n * Optional. Description and purpose of the function. Model uses it to decide how and whether to call the function.\n */\n", "excerptTokens": [{"kind": "Content", "text": "description?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "description", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclaration#name:member", "docComment": "/**\n * The name of the function to call. Must start with a letter or an underscore. Must be a-z, A-Z, 0-9, or contain underscores and dashes, with a max length of 64.\n */\n", "excerptTokens": [{"kind": "Content", "text": "name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclaration#parameters:member", "docComment": "/**\n * Optional. Describes the parameters to this function in JSON Schema Object format. Reflects the Open API 3.03 Parameter Object. string Key: the name of the parameter. Parameter names are case sensitive. Schema Value: the Schema defining the type used for the parameter. For function with no parameters, this can be left unset.\n *\n * @example\n *\n * with 1 required and 1 optional parameter: type: OBJECT properties:\n * ```\n * param1:\n *\n *   type: STRING\n * param2:\n *\n *  type: INTEGER\n * required:\n *\n *   - param1\n * ```\n *\n */\n", "excerptTokens": [{"kind": "Content", "text": "parameters?: "}, {"kind": "Reference", "text": "FunctionDeclarationSchema", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "parameters", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema:interface", "docComment": "/**\n * Schema for parameters passed to {@link FunctionDeclaration.parameters}.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionDeclarationSchema "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionDeclarationSchema", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema#description:member", "docComment": "/**\n * Optional. Description of the parameter.\n */\n", "excerptTokens": [{"kind": "Content", "text": "description?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "description", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema#properties:member", "docComment": "/**\n * The format of the parameter.\n */\n", "excerptTokens": [{"kind": "Content", "text": "properties: "}, {"kind": "Content", "text": "{\n        [k: string]: "}, {"kind": "Reference", "text": "FunctionDeclarationSchemaProperty", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaProperty:interface"}, {"kind": "Content", "text": ";\n    }"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "properties", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 4}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema#required:member", "docComment": "/**\n * Optional. Array of required parameters.\n */\n", "excerptTokens": [{"kind": "Content", "text": "required?: "}, {"kind": "Content", "text": "string[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "required", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema#type:member", "docComment": "/**\n * The type of the parameter.\n */\n", "excerptTokens": [{"kind": "Content", "text": "type: "}, {"kind": "Reference", "text": "FunctionDeclarationSchemaType", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "type", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaProperty:interface", "docComment": "/**\n * Schema for top-level function declaration\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionDeclarationSchemaProperty extends "}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!Schema:interface"}, {"kind": "Content", "text": " "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionDeclarationSchemaProperty", "preserveMemberOrder": false, "members": [], "extendsTokenRanges": [{"startIndex": 1, "endIndex": 2}]}, {"kind": "Enum", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType:enum", "docComment": "/**\n * Contains the list of OpenAPI data types as defined by https://swagger.io/docs/specification/data-models/data-types/\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare enum FunctionDeclarationSchemaType "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionDeclarationSchemaType", "preserveMemberOrder": false, "members": [{"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.ARRAY:member", "docComment": "/**\n * Array type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "ARRAY = "}, {"kind": "Content", "text": "\"ARRAY\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "ARRAY"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.BOOLEAN:member", "docComment": "/**\n * Boolean type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "BOOLEAN = "}, {"kind": "Content", "text": "\"BOOLEAN\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "BOOLEAN"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.INTEGER:member", "docComment": "/**\n * Integer type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "INTEGER = "}, {"kind": "Content", "text": "\"INTEGER\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "INTEGER"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.NUMBER:member", "docComment": "/**\n * Number type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "NUMBER = "}, {"kind": "Content", "text": "\"NUMBER\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "NUMBER"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.OBJECT:member", "docComment": "/**\n * Object type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "OBJECT = "}, {"kind": "Content", "text": "\"OBJECT\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "OBJECT"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType.STRING:member", "docComment": "/**\n * String type.\n */\n", "excerptTokens": [{"kind": "Content", "text": "STRING = "}, {"kind": "Content", "text": "\"STRING\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "STRING"}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionDeclarationsTool:interface", "docComment": "/**\n * A FunctionDeclarationsTool is a piece of code that enables the system to interact with external systems to perform an action, or set of actions, outside of knowledge and scope of the model.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare interface FunctionDeclarationsTool "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "FunctionDeclarationsTool", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionDeclarationsTool#functionDeclarations:member", "docComment": "/**\n * Optional. One or more function declarations to be passed to the model along with the current user query. Model may decide to call a subset of these functions by populating [FunctionCall][content.part.functionCall] in the response. User should provide a [FunctionResponse][content.part.functionResponse] for each function call in the next turn. Based on the function responses, <PERSON> will generate the final response back to the user. Maximum 64 function declarations can be provided.\n */\n", "excerptTokens": [{"kind": "Content", "text": "functionDeclarations?: "}, {"kind": "Reference", "text": "FunctionDeclaration", "canonicalReference": "@google/generative-ai!FunctionDeclaration:interface"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionDeclarations", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionResponse:interface", "docComment": "/**\n * The result output from a [FunctionCall] that contains a string representing the [FunctionDeclaration.name] and a structured JSON object containing any output from the function is used as context to the model. This should contain the result of a [FunctionCall] made based on model prediction.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionResponse "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FunctionResponse", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponse#name:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "name", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponse#response:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "response: "}, {"kind": "Content", "text": "object"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "response", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!FunctionResponsePart:interface", "docComment": "/**\n * Content part interface if the part represents FunctionResponse.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface FunctionResponsePart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "FunctionResponsePart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse: "}, {"kind": "Reference", "text": "FunctionResponse", "canonicalReference": "@google/generative-ai!FunctionResponse:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!FunctionResponsePart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!GenerativeContentBlob:interface", "docComment": "/**\n * Interface for sending an image.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface GenerativeContentBlob "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "GenerativeContentBlob", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!GenerativeContentBlob#data:member", "docComment": "/**\n * Image as a base64 string.\n */\n", "excerptTokens": [{"kind": "Content", "text": "data: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "data", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!GenerativeContentBlob#mimeType:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "mimeType: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "mimeType", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Class", "canonicalReference": "@google/generative-ai!GoogleAICacheManager:class", "docComment": "/**\n * Class for managing GoogleAI content caches.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare class GoogleAICacheManager "}], "fileUrlPath": "dist/src/server/cache-manager.d.ts", "releaseTag": "Public", "isAbstract": false, "name": "GoogleAICacheManager", "preserveMemberOrder": false, "members": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!GoogleAICacheManager:constructor(1)", "docComment": "/**\n * Constructs a new instance of the `GoogleAICacheManager` class\n */\n", "excerptTokens": [{"kind": "Content", "text": "constructor(apiKey: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ", _requestOptions?: "}, {"kind": "Reference", "text": "RequestOptions", "canonicalReference": "@google/generative-ai!RequestOptions:interface"}, {"kind": "Content", "text": ");"}], "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}, {"parameterName": "_requestOptions", "parameterTypeTokenRange": {"startIndex": 3, "endIndex": 4}, "isOptional": true}]}, {"kind": "Property", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#api<PERSON>ey:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "a<PERSON><PERSON><PERSON>: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "<PERSON><PERSON><PERSON><PERSON>", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isStatic": false, "isProtected": false, "isAbstract": false}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#create:member(1)", "docComment": "/**\n * Upload a new content cache\n */\n", "excerptTokens": [{"kind": "Content", "text": "create(createOptions: "}, {"kind": "Reference", "text": "CachedContentCreateParams", "canonicalReference": "@google/generative-ai!CachedContentCreateParams:interface"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!CachedContent:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 7}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "createOptions", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "create"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#delete:member(1)", "docComment": "/**\n * Delete content cache with given name\n */\n", "excerptTokens": [{"kind": "Content", "text": "delete(name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<void>"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 5}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "name", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "delete"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#get:member(1)", "docComment": "/**\n * Get a content cache\n */\n", "excerptTokens": [{"kind": "Content", "text": "get(name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!CachedContent:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 7}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "name", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "get"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#list:member(1)", "docComment": "/**\n * List all uploaded content caches\n */\n", "excerptTokens": [{"kind": "Content", "text": "list(listParams?: "}, {"kind": "Reference", "text": "ListParams", "canonicalReference": "@google/generative-ai!ListParams:interface"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "ListCacheResponse", "canonicalReference": "@google/generative-ai!ListCacheResponse:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 7}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "listParams", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": true}], "isOptional": false, "isAbstract": false, "name": "list"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAICacheManager#update:member(1)", "docComment": "/**\n * Update an existing content cache\n */\n", "excerptTokens": [{"kind": "Content", "text": "update(name: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ", updateParams: "}, {"kind": "Reference", "text": "CachedContentUpdateParams", "canonicalReference": "@google/generative-ai!CachedContentUpdateParams:interface"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!CachedContent:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 5, "endIndex": 9}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "name", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}, {"parameterName": "updateParams", "parameterTypeTokenRange": {"startIndex": 3, "endIndex": 4}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "update"}], "implementsTokenRanges": []}, {"kind": "Class", "canonicalReference": "@google/generative-ai!GoogleAIFileManager:class", "docComment": "/**\n * Class for managing GoogleAI file uploads.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare class GoogleAIFileManager "}], "fileUrlPath": "dist/src/server/file-manager.d.ts", "releaseTag": "Public", "isAbstract": false, "name": "GoogleAIFileManager", "preserveMemberOrder": false, "members": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!GoogleAIFileManager:constructor(1)", "docComment": "/**\n * Constructs a new instance of the `GoogleAIFileManager` class\n */\n", "excerptTokens": [{"kind": "Content", "text": "constructor(apiKey: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ", _requestOptions?: "}, {"kind": "Reference", "text": "RequestOptions", "canonicalReference": "@google/generative-ai!RequestOptions:interface"}, {"kind": "Content", "text": ");"}], "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}, {"parameterName": "_requestOptions", "parameterTypeTokenRange": {"startIndex": 3, "endIndex": 4}, "isOptional": true}]}, {"kind": "Property", "canonicalReference": "@google/generative-ai!GoogleAIFileManager#apiKey:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "a<PERSON><PERSON><PERSON>: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "<PERSON><PERSON><PERSON><PERSON>", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isStatic": false, "isProtected": false, "isAbstract": false}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAIFileManager#deleteFile:member(1)", "docComment": "/**\n * Delete file with given ID\n */\n", "excerptTokens": [{"kind": "Content", "text": "deleteFile(fileId: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<void>"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 5}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "fileId", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "deleteFile"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAIFileManager#getFile:member(1)", "docComment": "/**\n * Get metadata for file with given ID\n */\n", "excerptTokens": [{"kind": "Content", "text": "getFile(fileId: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "FileMetadataResponse", "canonicalReference": "@google/generative-ai!FileMetadataResponse:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 7}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "fileId", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "getFile"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAIFileManager#listFiles:member(1)", "docComment": "/**\n * List all uploaded files\n */\n", "excerptTokens": [{"kind": "Content", "text": "listFiles(listParams?: "}, {"kind": "Reference", "text": "ListParams", "canonicalReference": "@google/generative-ai!ListParams:interface"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "ListFilesResponse", "canonicalReference": "@google/generative-ai!ListFilesResponse:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 3, "endIndex": 7}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "listParams", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": true}], "isOptional": false, "isAbstract": false, "name": "listFiles"}, {"kind": "Method", "canonicalReference": "@google/generative-ai!GoogleAIFileManager#uploadFile:member(1)", "docComment": "/**\n * Upload a file\n */\n", "excerptTokens": [{"kind": "Content", "text": "uploadFile(filePath: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ", fileMetadata: "}, {"kind": "Reference", "text": "FileMetadata", "canonicalReference": "@google/generative-ai!FileMetadata:interface"}, {"kind": "Content", "text": "): "}, {"kind": "Reference", "text": "Promise", "canonicalReference": "!Promise:interface"}, {"kind": "Content", "text": "<"}, {"kind": "Reference", "text": "UploadFileResponse", "canonicalReference": "@google/generative-ai!UploadFileResponse:interface"}, {"kind": "Content", "text": ">"}, {"kind": "Content", "text": ";"}], "isStatic": false, "returnTypeTokenRange": {"startIndex": 5, "endIndex": 9}, "releaseTag": "Public", "isProtected": false, "overloadIndex": 1, "parameters": [{"parameterName": "filePath", "parameterTypeTokenRange": {"startIndex": 1, "endIndex": 2}, "isOptional": false}, {"parameterName": "fileMetadata", "parameterTypeTokenRange": {"startIndex": 3, "endIndex": 4}, "isOptional": false}], "isOptional": false, "isAbstract": false, "name": "uploadFile"}], "implementsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!InlineDataPart:interface", "docComment": "/**\n * Content part interface if the part represents an image.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface InlineDataPart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "InlineDataPart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData: "}, {"kind": "Reference", "text": "GenerativeContentBlob", "canonicalReference": "@google/generative-ai!GenerativeContentBlob:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!InlineDataPart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ListCacheResponse:interface", "docComment": "/**\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ListCacheResponse "}], "fileUrlPath": "dist/types/server/caching.d.ts", "releaseTag": "Public", "name": "ListCacheResponse", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListCacheResponse#cachedContents:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "cachedContents: "}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!CachedContent:interface"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "cachedContents", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListCacheResponse#nextPageToken:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "nextPageToken?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "nextPageToken", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ListFilesResponse:interface", "docComment": "/**\n * Response from calling {@link GoogleAIFileManager.listFiles}\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ListFilesResponse "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "ListFilesResponse", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListFilesResponse#files:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "files: "}, {"kind": "Reference", "text": "FileMetadataResponse", "canonicalReference": "@google/generative-ai!FileMetadataResponse:interface"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "files", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListFilesResponse#nextPageToken:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "nextPageToken?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "nextPageToken", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ListParams:interface", "docComment": "/**\n * Params to pass to {@link GoogleAIFileManager.listFiles} or {@link GoogleAICacheManager.list}\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ListParams "}], "fileUrlPath": "dist/types/server/shared.d.ts", "releaseTag": "Public", "name": "ListParams", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListParams#pageSize:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "pageSize?: "}, {"kind": "Content", "text": "number"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "pageSize", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ListParams#pageToken:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "pageToken?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "pageToken", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Enum", "canonicalReference": "@google/generative-ai!Outcome:enum", "docComment": "/**\n * Possible outcomes of code execution.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare enum Outcome "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "Outcome", "preserveMemberOrder": false, "members": [{"kind": "EnumMember", "canonicalReference": "@google/generative-ai!Outcome.OUTCOME_DEADLINE_EXCEEDED:member", "docComment": "/**\n * Code execution ran for too long, and was cancelled. There may or may not be a partial output present.\n */\n", "excerptTokens": [{"kind": "Content", "text": "OUTCOME_DEADLINE_EXCEEDED = "}, {"kind": "Content", "text": "\"outcome_deadline_exceeded\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "OUTCOME_DEADLINE_EXCEEDED"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!Outcome.OUTCOME_FAILED:member", "docComment": "/**\n * Code execution finished but with a failure. `stderr` should contain the reason.\n */\n", "excerptTokens": [{"kind": "Content", "text": "OUTCOME_FAILED = "}, {"kind": "Content", "text": "\"outcome_failed\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "OUTCOME_FAILED"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!Outcome.OUTCOME_OK:member", "docComment": "/**\n * Code execution completed successfully.\n */\n", "excerptTokens": [{"kind": "Content", "text": "OUTCOME_OK = "}, {"kind": "Content", "text": "\"outcome_ok\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "OUTCOME_OK"}, {"kind": "EnumMember", "canonicalReference": "@google/generative-ai!Outcome.OUTCOME_UNSPECIFIED:member", "docComment": "/**\n * Unspecified status. This value should not be used.\n */\n", "excerptTokens": [{"kind": "Content", "text": "OUTCOME_UNSPECIFIED = "}, {"kind": "Content", "text": "\"outcome_unspecified\""}], "initializerTokenRange": {"startIndex": 1, "endIndex": 2}, "releaseTag": "Public", "name": "OUTCOME_UNSPECIFIED"}]}, {"kind": "TypeAlias", "canonicalReference": "@google/generative-ai!Part:type", "docComment": "/**\n * Content part - includes text or image part types.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export type Part = "}, {"kind": "Reference", "text": "TextPart", "canonicalReference": "@google/generative-ai!TextPart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "InlineDataPart", "canonicalReference": "@google/generative-ai!InlineDataPart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "FunctionCallPart", "canonicalReference": "@google/generative-ai!FunctionCallPart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "FunctionResponsePart", "canonicalReference": "@google/generative-ai!FunctionResponsePart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "FileDataPart", "canonicalReference": "@google/generative-ai!FileDataPart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "ExecutableCodePart", "canonicalReference": "@google/generative-ai!ExecutableCodePart:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "CodeExecutionResultPart", "canonicalReference": "@google/generative-ai!CodeExecutionResultPart:interface"}, {"kind": "Content", "text": ";"}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "Part", "typeTokenRange": {"startIndex": 1, "endIndex": 14}}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!RequestOptions:interface", "docComment": "/**\n * Params passed to getGenerativeModel() or GoogleAIFileManager().\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface RequestOptions "}], "fileUrlPath": "dist/types/requests.d.ts", "releaseTag": "Public", "name": "RequestOptions", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RequestOptions#apiClient:member", "docComment": "/**\n * Additional attribution information to include in the x-goog-api-client header. Used by wrapper SDKs.\n */\n", "excerptTokens": [{"kind": "Content", "text": "apiClient?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "apiClient", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RequestOptions#apiVersion:member", "docComment": "/**\n * Version of API endpoint to call (e.g. \"v1\" or \"v1beta\"). If not specified, defaults to latest stable version.\n */\n", "excerptTokens": [{"kind": "Content", "text": "apiVersion?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "apiVersion", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RequestOptions#baseUrl:member", "docComment": "/**\n * Base endpoint url. Defaults to \"https://generativelanguage.googleapis.com\"\n */\n", "excerptTokens": [{"kind": "Content", "text": "baseUrl?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "baseUrl", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RequestOptions#customHeaders:member", "docComment": "/**\n * Custom HTTP request headers.\n */\n", "excerptTokens": [{"kind": "Content", "text": "customHeaders?: "}, {"kind": "Reference", "text": "Headers", "canonicalReference": "!Headers:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "Record", "canonicalReference": "!Record:type"}, {"kind": "Content", "text": "<string, string>"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "customHeaders", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 5}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RequestOptions#timeout:member", "docComment": "/**\n * Request timeout in milliseconds.\n */\n", "excerptTokens": [{"kind": "Content", "text": "timeout?: "}, {"kind": "Content", "text": "number"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "timeout", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ResponseSchema:interface", "docComment": "/**\n * <PERSON><PERSON><PERSON> passed to `GenerationConfig.responseSchema`\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ResponseSchema extends "}, {"kind": "Reference", "text": "<PERSON><PERSON><PERSON>", "canonicalReference": "@google/generative-ai!Schema:interface"}, {"kind": "Content", "text": " "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "ResponseSchema", "preserveMemberOrder": false, "members": [], "extendsTokenRanges": [{"startIndex": 1, "endIndex": 2}]}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!RpcStatus:interface", "docComment": "/**\n * Standard RPC error status object.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface RpcStatus "}], "fileUrlPath": "dist/types/server/shared.d.ts", "releaseTag": "Public", "name": "RpcStatus", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RpcStatus#code:member", "docComment": "/**\n * Error status code\n */\n", "excerptTokens": [{"kind": "Content", "text": "code: "}, {"kind": "Content", "text": "number"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "code", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RpcStatus#details:member", "docComment": "/**\n * A list of messages that carry the error details.\n */\n", "excerptTokens": [{"kind": "Content", "text": "details?: "}, {"kind": "Reference", "text": "ErrorDetails", "canonicalReference": "@google/generative-ai!ErrorDetails:interface"}, {"kind": "Content", "text": "[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "details", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 3}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!RpcStatus#message:member", "docComment": "/**\n * A developer-facing error message.\n */\n", "excerptTokens": [{"kind": "Content", "text": "message: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "message", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!Schema:interface", "docComment": "/**\n * Schema is used to define the format of input/output data. Represents a select subset of an OpenAPI 3.0 schema object. More fields may be added in the future as needed.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface Schema "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "<PERSON><PERSON><PERSON>", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#description:member", "docComment": "/**\n * Optional. The description of the property.\n */\n", "excerptTokens": [{"kind": "Content", "text": "description?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "description", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#enum:member", "docComment": "/**\n * Optional. The enum of the property.\n */\n", "excerptTokens": [{"kind": "Content", "text": "enum?: "}, {"kind": "Content", "text": "string[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "enum", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#example:member", "docComment": "/**\n * Optional. The example of the property.\n */\n", "excerptTokens": [{"kind": "Content", "text": "example?: "}, {"kind": "Content", "text": "unknown"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "example", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#format:member", "docComment": "/**\n * Optional. The format of the property.\n */\n", "excerptTokens": [{"kind": "Content", "text": "format?: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "format", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#items:member", "docComment": "/**\n * Optional. The items of the property. {@link FunctionDeclarationSchema}\n */\n", "excerptTokens": [{"kind": "Content", "text": "items?: "}, {"kind": "Reference", "text": "FunctionDeclarationSchema", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "items", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#nullable:member", "docComment": "/**\n * Optional. Whether the property is nullable.\n */\n", "excerptTokens": [{"kind": "Content", "text": "nullable?: "}, {"kind": "Content", "text": "boolean"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "nullable", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#properties:member", "docComment": "/**\n * Optional. Map of {@link FunctionDeclarationSchema}.\n */\n", "excerptTokens": [{"kind": "Content", "text": "properties?: "}, {"kind": "Content", "text": "{\n        [k: string]: "}, {"kind": "Reference", "text": "FunctionDeclarationSchema", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchema:interface"}, {"kind": "Content", "text": ";\n    }"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "properties", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 4}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#required:member", "docComment": "/**\n * Optional. Array of required property.\n */\n", "excerptTokens": [{"kind": "Content", "text": "required?: "}, {"kind": "Content", "text": "string[]"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "required", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!Schema#type:member", "docComment": "/**\n * Optional. The type of the property. {@link FunctionDeclarationSchemaType}.\n */\n", "excerptTokens": [{"kind": "Content", "text": "type?: "}, {"kind": "Reference", "text": "FunctionDeclarationSchemaType", "canonicalReference": "@google/generative-ai!FunctionDeclarationSchemaType:enum"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "type", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!TextPart:interface", "docComment": "/**\n * Content part interface if the part represents a text string.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface TextPart "}], "fileUrlPath": "dist/types/content.d.ts", "releaseTag": "Public", "name": "TextPart", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#codeExecutionResult:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "codeExecutionResult?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "codeExecutionResult", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#executableCode:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "executableCode?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "executableCode", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#fileData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "fileData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "fileData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#functionCall:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCall?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionCall", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#functionResponse:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionResponse?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "functionResponse", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#inlineData:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "inlineData?: "}, {"kind": "Content", "text": "never"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": true, "releaseTag": "Public", "name": "inlineData", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}, {"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!TextPart#text:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "text: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "text", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "TypeAlias", "canonicalReference": "@google/generative-ai!Tool:type", "docComment": "/**\n * Defines a tool that model can call to access external knowledge.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export declare type Tool = "}, {"kind": "Reference", "text": "FunctionDeclarationsTool", "canonicalReference": "@google/generative-ai!FunctionDeclarationsTool:interface"}, {"kind": "Content", "text": " | "}, {"kind": "Reference", "text": "CodeExecutionTool", "canonicalReference": "@google/generative-ai!CodeExecutionTool:interface"}, {"kind": "Content", "text": ";"}], "fileUrlPath": "dist/types/requests.d.ts", "releaseTag": "Public", "name": "Tool", "typeTokenRange": {"startIndex": 1, "endIndex": 4}}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!ToolConfig:interface", "docComment": "/**\n * Tool config. This config is shared for all tools provided in the request.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface ToolConfig "}], "fileUrlPath": "dist/types/function-calling.d.ts", "releaseTag": "Public", "name": "ToolConfig", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!ToolConfig#functionCallingConfig:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "functionCallingConfig: "}, {"kind": "Reference", "text": "FunctionCallingConfig", "canonicalReference": "@google/generative-ai!FunctionCallingConfig:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "functionCallingConfig", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!UploadFileResponse:interface", "docComment": "/**\n * Response from calling {@link GoogleAIFileManager.uploadFile}\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface UploadFileResponse "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "UploadFileResponse", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!UploadFileResponse#file:member", "docComment": "", "excerptTokens": [{"kind": "Content", "text": "file: "}, {"kind": "Reference", "text": "FileMetadataResponse", "canonicalReference": "@google/generative-ai!FileMetadataResponse:interface"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "file", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}, {"kind": "Interface", "canonicalReference": "@google/generative-ai!VideoMetadata:interface", "docComment": "/**\n * Metadata populated when video has been processed.\n *\n * @public\n */\n", "excerptTokens": [{"kind": "Content", "text": "export interface VideoMetadata "}], "fileUrlPath": "dist/types/server/files.d.ts", "releaseTag": "Public", "name": "VideoMetadata", "preserveMemberOrder": false, "members": [{"kind": "PropertySignature", "canonicalReference": "@google/generative-ai!VideoMetadata#videoDuration:member", "docComment": "/**\n * The video duration in protobuf {@link https://cloud.google.com/ruby/docs/reference/google-cloud-workflows-v1/latest/Google-Protobuf-Duration#json-mapping | Duration} format.\n */\n", "excerptTokens": [{"kind": "Content", "text": "videoDuration: "}, {"kind": "Content", "text": "string"}, {"kind": "Content", "text": ";"}], "isReadonly": false, "isOptional": false, "releaseTag": "Public", "name": "videoDuration", "propertyTypeTokenRange": {"startIndex": 1, "endIndex": 2}}], "extendsTokenRanges": []}]}]}