import { ToolRegistry, ToolExecutionContext, ToolExecutionResult } from './ToolRegistry';
import { ErrorHandlingMiddleware, ProgressCallback } from '../../error-handling/ErrorHandlingMiddleware';
import { ErrorHandlingConfiguration } from '../../types';
export interface ToolConfirmationHandler {
    confirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
}
export interface ToolExecutionOptions {
    requireConfirmation?: boolean;
    timeout?: number;
    retries?: number;
    context?: ToolExecutionContext;
    enableAdvancedErrorHandling?: boolean;
    progressCallback?: ProgressCallback;
    allowCancel?: boolean;
}
export interface ToolExecutionPlan {
    toolName: string;
    parameters: Record<string, any>;
    dependencies?: string[];
    parallel?: boolean;
}
export declare class ToolExecutionEngine {
    private registry;
    private confirmationHandler?;
    private defaultTimeout;
    private defaultRetries;
    private errorHandlingMiddleware?;
    constructor(registry: ToolRegistry, confirmationHandler?: ToolConfirmationHandler, errorHandlingConfig?: ErrorHandlingConfiguration);
    setConfirmationHandler(handler: ToolConfirmationHandler): void;
    setErrorHandlingMiddleware(middleware: ErrorHandlingMiddleware): void;
    getErrorHandlingMiddleware(): ErrorHandlingMiddleware | undefined;
    executeSingleTool(toolName: string, parameters: Record<string, any>, options?: ToolExecutionOptions): Promise<ToolExecutionResult>;
    /**
     * Execute tool with advanced error handling middleware
     */
    private executeWithAdvancedErrorHandling;
    /**
     * Execute tool with legacy retry logic (fallback)
     */
    private executeWithLegacyRetry;
    executeMultipleTools(plans: ToolExecutionPlan[], options?: ToolExecutionOptions): Promise<ToolExecutionResult[]>;
    executeToolChain(toolChain: Array<{
        toolName: string;
        parameters: Record<string, any>;
        useOutputFrom?: string;
    }>, options?: ToolExecutionOptions): Promise<ToolExecutionResult[]>;
    private executeWithTimeout;
    private delay;
    private isCriticalTool;
    setDefaultTimeout(timeout: number): void;
    setDefaultRetries(retries: number): void;
    getRegistry(): ToolRegistry;
    validateToolChain(toolChain: Array<{
        toolName: string;
        parameters: Record<string, any>;
    }>): Promise<{
        valid: boolean;
        errors: string[];
        warnings: string[];
    }>;
    /**
     * Cancel ongoing operation (if using advanced error handling)
     */
    cancelOperation(operationId: string): void;
    /**
     * Get circuit breaker metrics (if using advanced error handling)
     */
    getCircuitBreakerMetrics(): Record<string, any>;
    /**
     * Reset circuit breakers (if using advanced error handling)
     */
    resetCircuitBreakers(): void;
    /**
     * Update error handling configuration
     */
    updateErrorHandlingConfig(config: Partial<ErrorHandlingConfiguration>): void;
    /**
     * Check if advanced error handling is available
     */
    hasAdvancedErrorHandling(): boolean;
}
//# sourceMappingURL=ToolExecutionEngine.d.ts.map