"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalInterface = void 0;
const BaseComponent_1 = require("../common/BaseComponent");
const ProgressIndicator_1 = require("../common/ProgressIndicator");
class TerminalInterface extends BaseComponent_1.BaseComponent {
    progressIndicator;
    constructor(state, config) {
        super(state, config);
        this.progressIndicator = new ProgressIndicator_1.CLIProgressIndicator(config.theme);
    }
    async render() {
        this.utils.clearScreen();
        this.renderHeader();
        this.renderStatusBar();
        console.log();
    }
    renderHeader() {
        const title = '🤖 AI CLI Terminal';
        const subtitle = 'Interactive AI Assistant';
        this.utils.showBanner(title, subtitle);
    }
    renderStatusBar() {
        const statusItems = [
            `Provider: ${this.utils.colorize(this.state.currentProvider, this.config.theme.accent)}`,
            `Model: ${this.utils.colorize(this.state.currentModel, this.config.theme.accent)}`,
            `Status: ${this.utils.colorize('Connected', this.config.theme.success)}`,
        ];
        const statusBar = '│ ' + statusItems.join(' │ ') + ' │';
        const separator = '─'.repeat(statusBar.length - 2);
        console.log(this.utils.colorize('┌' + separator + '┐', this.config.theme.muted));
        console.log(this.utils.colorize(statusBar, this.config.theme.muted));
        console.log(this.utils.colorize('└' + separator + '┘', this.config.theme.muted));
    }
    renderPrompt() {
        const promptSymbol = this.utils.colorize('❯', this.config.theme.primary);
        return `${promptSymbol} `;
    }
    renderTypingIndicator() {
        this.utils.startSpinner('AI is thinking...');
    }
    stopTypingIndicator() {
        this.utils.stopSpinner();
    }
    renderError(error) {
        console.log();
        this.utils.showError(error);
        console.log();
    }
    renderWarning(warning) {
        console.log();
        this.utils.showWarning(warning);
        console.log();
    }
    renderInfo(info) {
        console.log();
        this.utils.showInfo(info);
        console.log();
    }
    renderSeparator() {
        this.utils.printSeparator('─', 60);
    }
    /**
     * Show retry progress to user
     */
    showRetryProgress(attempt, maxAttempts, delay, error) {
        const delaySeconds = Math.round(delay / 1000);
        this.progressIndicator.update(`Request failed (attempt ${attempt}/${maxAttempts}). Retrying in ${delaySeconds}s... (${error.message})`);
    }
    /**
     * Show operation progress
     */
    showOperationProgress(message) {
        this.progressIndicator.start(message);
    }
    /**
     * Update operation progress
     */
    updateOperationProgress(message) {
        this.progressIndicator.update(message);
    }
    /**
     * Show operation success
     */
    showOperationSuccess(message) {
        this.progressIndicator.succeed(message);
    }
    /**
     * Show operation failure
     */
    showOperationFailure(message) {
        this.progressIndicator.fail(message);
    }
    /**
     * Stop any ongoing progress indicators
     */
    stopProgress() {
        this.progressIndicator.stop();
    }
    /**
     * Show circuit breaker status
     */
    showCircuitBreakerStatus(serviceName, status) {
        const statusMessages = {
            open: `⚠️  Service ${serviceName} is temporarily unavailable. Please try again later.`,
            'half-open': `🔄 Testing ${serviceName} service recovery...`,
            closed: `✅ ${serviceName} service recovered successfully.`
        };
        const message = statusMessages[status];
        if (status === 'open') {
            this.renderWarning(message);
        }
        else if (status === 'closed') {
            this.renderInfo(message);
        }
        else {
            this.renderInfo(message);
        }
    }
    /**
     * Show cancellation prompt
     */
    async showCancellationPrompt() {
        console.log();
        this.utils.showWarning('Operation is taking longer than expected.');
        const inquirer = require('inquirer');
        const { shouldCancel } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'shouldCancel',
                message: 'Would you like to cancel this operation?',
                default: false
            }
        ]);
        return shouldCancel;
    }
    async renderToolConfirmation(toolName, parameters) {
        console.log();
        console.log(this.utils.colorize('🔧 Tool Execution Request', this.config.theme.warning, true));
        console.log();
        console.log(`Tool: ${this.utils.colorize(toolName, this.config.theme.accent)}`);
        console.log('Parameters:');
        for (const [key, value] of Object.entries(parameters)) {
            const formattedValue = typeof value === 'string' && value.length > 100
                ? this.utils.truncateText(value, 100)
                : JSON.stringify(value);
            console.log(`  ${this.utils.colorize(key, this.config.theme.secondary)}: ${formattedValue}`);
        }
        console.log();
        return await this.utils.confirmAction('Allow this tool execution?', false);
    }
    renderToolResult(toolName, result, duration) {
        console.log();
        console.log(this.utils.colorize(`🔧 Tool Result: ${toolName}`, this.config.theme.success));
        if (duration) {
            console.log(this.utils.colorize(`⏱️  Duration: ${this.utils.formatDuration(duration)}`, this.config.theme.muted));
        }
        if (result.error) {
            console.log(this.utils.colorize(`❌ Error: ${result.error}`, this.config.theme.error));
        }
        else if (result.content) {
            console.log();
            console.log(result.content);
        }
        console.log();
    }
    async renderStreamingResponse(content) {
        // For now, just print the content
        // In a full implementation, this would handle character-by-character streaming
        process.stdout.write(content);
    }
    renderMessageTimestamp(timestamp) {
        if (!this.config.showTimestamps) {
            return '';
        }
        const timeStr = timestamp.toLocaleTimeString();
        return this.utils.colorize(`[${timeStr}]`, this.config.theme.muted) + ' ';
    }
    renderTokenCount(tokens) {
        if (!this.config.showTokenCounts || !tokens) {
            return '';
        }
        return this.utils.colorize(`(${tokens} tokens)`, this.config.theme.muted) + ' ';
    }
}
exports.TerminalInterface = TerminalInterface;
//# sourceMappingURL=TerminalInterface.js.map