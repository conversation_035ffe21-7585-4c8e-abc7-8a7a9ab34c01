import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig } from '../../types';
import { ErrorHandlingMiddleware } from '../../../core/error-handling/ErrorHandlingMiddleware';
export declare class ErrorHandlingMetricsViewer extends BaseComponent {
    private errorHandler;
    constructor(state: CLIState, config: CLIConfig, errorHandler: ErrorHandlingMiddleware);
    render(): Promise<void>;
    private showMetricsMenu;
    private showOverallStatistics;
    private showRetryMetrics;
    private showCircuitBreakerStatus;
    private showPerServiceMetrics;
    private showPerformanceMetrics;
    private showRecentErrors;
    private exportMetrics;
}
//# sourceMappingURL=ErrorHandlingMetricsViewer.d.ts.map