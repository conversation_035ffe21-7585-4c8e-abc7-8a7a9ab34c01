"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemPromptRegistry = void 0;
const DefaultPrompts_1 = require("./DefaultPrompts");
class SystemPromptRegistry {
    configManager;
    prompts = new Map();
    activePromptId;
    defaultPromptId;
    categories = new Set();
    constructor(options) {
        this.configManager = options.configManager;
        this.initializeDefaultCategories();
    }
    initializeDefaultCategories() {
        const defaultCategories = ['General', 'Software Engineering', 'CLI Agent', 'Custom'];
        defaultCategories.forEach(category => this.categories.add(category));
    }
    async initialize() {
        await this.loadFromConfig();
        await this.initializeDefaultPrompts();
    }
    async loadFromConfig() {
        const config = this.configManager.getConfig();
        const systemPromptConfig = config.systemPrompts;
        // Load prompts from configuration
        Object.values(systemPromptConfig.prompts).forEach(prompt => {
            this.prompts.set(prompt.id, prompt);
            if (prompt.category) {
                this.categories.add(prompt.category);
            }
            if (prompt.tags) {
                prompt.tags.forEach(tag => this.categories.add(tag));
            }
        });
        // Set active and default prompts
        this.activePromptId = systemPromptConfig.activePromptId;
        this.defaultPromptId = systemPromptConfig.defaultPromptId;
        // Add categories from config
        systemPromptConfig.categories.forEach(category => this.categories.add(category));
    }
    async saveToConfig() {
        const promptsRecord = {};
        this.prompts.forEach((prompt, id) => {
            promptsRecord[id] = prompt;
        });
        const systemPromptConfig = {
            prompts: promptsRecord,
            categories: Array.from(this.categories),
            activePromptId: this.activePromptId,
            defaultPromptId: this.defaultPromptId
        };
        // Update the configuration
        const currentConfig = this.configManager.getConfig();
        currentConfig.systemPrompts = systemPromptConfig;
        await this.configManager.saveConfig();
    }
    async registerPrompt(prompt) {
        const id = this.generatePromptId(prompt.name);
        const now = new Date();
        const fullPrompt = {
            ...prompt,
            id,
            createdAt: now,
            updatedAt: now
        };
        this.prompts.set(id, fullPrompt);
        if (fullPrompt.category) {
            this.categories.add(fullPrompt.category);
        }
        if (fullPrompt.tags) {
            fullPrompt.tags.forEach(tag => this.categories.add(tag));
        }
        // If this is marked as default or it's the first prompt, set it as default
        if (fullPrompt.isDefault || this.prompts.size === 1) {
            this.defaultPromptId = id;
        }
        // If this is marked as active or it's the first prompt, set it as active
        if (fullPrompt.isActive || this.prompts.size === 1) {
            this.activePromptId = id;
        }
        await this.saveToConfig();
        return id;
    }
    async updatePrompt(id, updates) {
        const prompt = this.prompts.get(id);
        if (!prompt) {
            return false;
        }
        const updatedPrompt = {
            ...prompt,
            ...updates,
            id: prompt.id,
            createdAt: prompt.createdAt,
            updatedAt: new Date()
        };
        this.prompts.set(id, updatedPrompt);
        if (updatedPrompt.category) {
            this.categories.add(updatedPrompt.category);
        }
        if (updatedPrompt.tags) {
            updatedPrompt.tags.forEach(tag => this.categories.add(tag));
        }
        await this.saveToConfig();
        return true;
    }
    async removePrompt(id) {
        if (!this.prompts.has(id)) {
            return false;
        }
        this.prompts.delete(id);
        // If this was the active prompt, clear it
        if (this.activePromptId === id) {
            this.activePromptId = this.defaultPromptId;
        }
        // If this was the default prompt, clear it
        if (this.defaultPromptId === id) {
            this.defaultPromptId = undefined;
        }
        await this.saveToConfig();
        return true;
    }
    getPrompt(id) {
        return this.prompts.get(id);
    }
    getAllPrompts() {
        return Array.from(this.prompts.values());
    }
    getPromptsByCategory(category) {
        return this.getAllPrompts().filter(prompt => prompt.category === category || (prompt.tags && prompt.tags.includes(category)));
    }
    searchPrompts(query) {
        const lowerQuery = query.toLowerCase();
        return this.getAllPrompts().filter(prompt => prompt.name.toLowerCase().includes(lowerQuery) ||
            prompt.description.toLowerCase().includes(lowerQuery) ||
            prompt.content.toLowerCase().includes(lowerQuery) ||
            (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(lowerQuery))));
    }
    getActivePrompt() {
        return this.activePromptId ? this.prompts.get(this.activePromptId) : undefined;
    }
    getDefaultPrompt() {
        return this.defaultPromptId ? this.prompts.get(this.defaultPromptId) : undefined;
    }
    async setActivePrompt(id) {
        if (!this.prompts.has(id)) {
            return false;
        }
        this.activePromptId = id;
        await this.saveToConfig();
        return true;
    }
    async setDefaultPrompt(id) {
        if (!this.prompts.has(id)) {
            return false;
        }
        this.defaultPromptId = id;
        await this.saveToConfig();
        return true;
    }
    getCategories() {
        return Array.from(this.categories);
    }
    async addCategory(category) {
        this.categories.add(category);
        await this.saveToConfig();
    }
    generatePromptId(name) {
        const baseId = name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
        let id = baseId;
        let counter = 1;
        while (this.prompts.has(id)) {
            id = `${baseId}-${counter}`;
            counter++;
        }
        return id;
    }
    getPromptCount() {
        return this.prompts.size;
    }
    hasPrompts() {
        return this.prompts.size > 0;
    }
    async initializeDefaultPrompts() {
        // Only initialize default prompts if no prompts exist
        if (this.prompts.size > 0) {
            return;
        }
        console.log('Initializing default system prompts...');
        for (const defaultPrompt of DefaultPrompts_1.DEFAULT_SYSTEM_PROMPTS) {
            try {
                await this.registerPrompt(defaultPrompt);
                console.log(`Registered default prompt: ${defaultPrompt.name}`);
            }
            catch (error) {
                console.warn(`Failed to register default prompt ${defaultPrompt.name}:`, error);
            }
        }
    }
}
exports.SystemPromptRegistry = SystemPromptRegistry;
//# sourceMappingURL=SystemPromptRegistry.js.map