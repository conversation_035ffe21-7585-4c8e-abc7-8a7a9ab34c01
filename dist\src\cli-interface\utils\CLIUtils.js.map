{"version": 3, "file": "CLIUtils.js", "sourceRoot": "", "sources": ["../../../../src/cli-interface/utils/CLIUtils.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,kDAA0B;AAC1B,8CAA+B;AAC/B,wDAAgC;AAChC,oCAAuD;AAEvD,MAAa,QAAQ;IACX,MAAM,CAAC,OAAO,GAAe,IAAI,CAAC;IAClC,KAAK,CAAW;IAExB,YAAY,QAAkB,yBAAiB;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEM,UAAU,CAAC,KAAa,EAAE,QAAiB;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;YAC/C,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAElF,MAAM,MAAM,GAAG,IAAA,eAAK,EAAC,OAAO,EAAE;YAC5B,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,OAAc;YACtC,aAAa,EAAE,QAAQ;SACxB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAEM,WAAW,CAAC,OAAe;QAChC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,SAAS,CAAC,OAAe;QAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,WAAW,CAAC,OAAe;QAChC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAEM,QAAQ,CAAC,OAAe;QAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC;IAEM,SAAS,CAAC,OAAe;QAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC;IAEM,QAAQ,CAAC,IAAY,EAAE,KAAa,EAAE,OAAgB,KAAK;QAChE,IAAI,SAAS,GAAG,eAAK,CAAC,KAA2B,CAAQ,CAAC;QAC1D,IAAI,IAAI,EAAE,CAAC;YACT,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;QAC7B,CAAC;QACD,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEM,YAAY,CAAC,IAAY;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,YAAY,CAAC,IAAY;QAC9B,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1B,CAAC;QACD,QAAQ,CAAC,OAAO,GAAG,IAAA,aAAG,EAAC;YACrB,IAAI;YACJ,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAc;SACjC,CAAC,CAAC,KAAK,EAAE,CAAC;IACb,CAAC;IAEM,aAAa,CAAC,IAAY;QAC/B,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IAEM,WAAW,CAAC,UAAmB,IAAI,EAAE,OAAgB;QAC1D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;YACD,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,eAAwB,KAAK;QACvE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YAC1C;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,WAAW;gBACjB,OAAO;gBACP,OAAO,EAAE,YAAY;aACtB;SACF,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,OAAe,EACf,OAA0C,EAC1C,YAAgB;QAEhB,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACzC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,OAAO;gBACP,OAAO;gBACP,OAAO,EAAE,YAAY;aACtB;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,QAAQ,CACnB,OAAe,EACf,YAAqB,EACrB,QAA8C;QAE9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACtC;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO;gBACP,OAAO,EAAE,YAAY;gBACrB,QAAQ;aACT;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACzC;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,UAAU;gBAChB,OAAO;gBACP,QAAQ,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,sBAAsB;aAC/E;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,eAAe,CAAC,IAAY,EAAE,WAAmB,EAAE;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,OAAO,IAAA,eAAK,EACV,IAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAC7E;YACE,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAY;SACrC,CACF,CAAC;IACJ,CAAC;IAEM,UAAU,CAAC,UAAkB,EAAE,UAAkB;QACtD,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAExC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAElC,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,IAAI,OAAO,EAAE,CAAC;oBACZ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClE,CAAC;gBACD,IAAI,OAAO,EAAE,CAAC;oBACZ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,OAAO,IAAA,eAAK,EACV,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EACjF;YACE,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,OAAc;SACvC,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,UAAkB,4BAA4B;QACzE,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpB;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,UAAU;gBAChB,OAAO;aACR;SACF,CAAC,CAAC;IACL,CAAC;IAEM,WAAW;QAChB,OAAO,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;IAEM,cAAc,CAAC,OAAe,GAAG,EAAE,SAAiB,EAAE;QAC3D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,cAAc,CAAC,KAAa;QACjC,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,IAAI,IAAI,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,IAAI,IAAI,CAAC;YACb,SAAS,EAAE,CAAC;QACd,CAAC;QAED,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;IAClD,CAAC;IAEM,cAAc,CAAC,EAAU;QAC9B,IAAI,EAAE,GAAG,IAAI;YAAE,OAAO,GAAG,EAAE,IAAI,CAAC;QAChC,IAAI,EAAE,GAAG,KAAK;YAAE,OAAO,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACpD,OAAO,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACvC,CAAC;IAEM,YAAY,CAAC,IAAY,EAAE,SAAiB;QACjD,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS;YAAE,OAAO,IAAI,CAAC;QAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAClD,CAAC;;AAtOH,4BAuOC"}