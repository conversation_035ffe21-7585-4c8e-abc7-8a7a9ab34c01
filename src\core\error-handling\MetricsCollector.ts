/**
 * Metrics Collection and Monitoring System
 * 
 * Provides comprehensive metrics collection for:
 * - Retry attempts and success rates
 * - Circuit breaker state changes
 * - Error rates and categorization
 * - Performance monitoring
 * - System health indicators
 */

export interface RetryMetrics {
  totalAttempts: number;
  successfulRetries: number;
  failedRetries: number;
  averageRetryDelay: number;
  maxRetryDelay: number;
  retrySuccessRate: number;
}

export interface CircuitBreakerMetrics {
  serviceName: string;
  currentState: string;
  totalStateChanges: number;
  timeInOpen: number;
  timeInHalfOpen: number;
  timeInClosed: number;
  failureRate: number;
  lastStateChange: Date;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<string, number>;
  errorsByService: Record<string, number>;
  errorsByOperation: Record<string, number>;
  errorRate: number;
  averageErrorResolutionTime: number;
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number;
  concurrentOperations: number;
  queuedOperations: number;
}

export interface SystemHealthMetrics {
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
  healthScore: number; // 0-100
}

export interface MetricsSnapshot {
  timestamp: Date;
  retry: RetryMetrics;
  circuitBreakers: CircuitBreakerMetrics[];
  errors: ErrorMetrics;
  performance: PerformanceMetrics;
  systemHealth: SystemHealthMetrics;
}

export interface MetricEvent {
  type: 'retry' | 'circuit_breaker' | 'error' | 'performance' | 'system';
  timestamp: Date;
  data: any;
  metadata?: Record<string, any>;
}

export class MetricsCollector {
  private retryEvents: Array<{ timestamp: Date; success: boolean; delay: number; operation: string }> = [];
  private circuitBreakerEvents: Array<{ timestamp: Date; serviceName: string; oldState: string; newState: string }> = [];
  private errorEvents: Array<{ timestamp: Date; category: string; service: string; operation: string; resolved: boolean; resolutionTime?: number }> = [];
  private performanceEvents: Array<{ timestamp: Date; operation: string; duration: number; success: boolean }> = [];
  
  private startTime: Date = new Date();
  private activeOperations: Set<string> = new Set();
  private queuedOperations: number = 0;

  /**
   * Record a retry attempt
   */
  recordRetryAttempt(operation: string, attempt: number, delay: number, success: boolean): void {
    this.retryEvents.push({
      timestamp: new Date(),
      success,
      delay,
      operation
    });

    // Keep only recent events (last 1000)
    if (this.retryEvents.length > 1000) {
      this.retryEvents = this.retryEvents.slice(-1000);
    }
  }

  /**
   * Record circuit breaker state change
   */
  recordCircuitBreakerStateChange(serviceName: string, oldState: string, newState: string): void {
    this.circuitBreakerEvents.push({
      timestamp: new Date(),
      serviceName,
      oldState,
      newState
    });

    // Keep only recent events (last 500)
    if (this.circuitBreakerEvents.length > 500) {
      this.circuitBreakerEvents = this.circuitBreakerEvents.slice(-500);
    }
  }

  /**
   * Record an error occurrence
   */
  recordError(category: string, service: string, operation: string): string {
    const errorId = this.generateId();
    this.errorEvents.push({
      timestamp: new Date(),
      category,
      service,
      operation,
      resolved: false
    });

    // Keep only recent events (last 1000)
    if (this.errorEvents.length > 1000) {
      this.errorEvents = this.errorEvents.slice(-1000);
    }

    return errorId;
  }

  /**
   * Mark an error as resolved
   */
  recordErrorResolution(errorId: string): void {
    // In a real implementation, you'd track by ID
    // For simplicity, we'll mark the most recent unresolved error
    for (let i = this.errorEvents.length - 1; i >= 0; i--) {
      if (!this.errorEvents[i].resolved) {
        this.errorEvents[i].resolved = true;
        this.errorEvents[i].resolutionTime = Date.now() - this.errorEvents[i].timestamp.getTime();
        break;
      }
    }
  }

  /**
   * Record performance data
   */
  recordPerformance(operation: string, duration: number, success: boolean): void {
    this.performanceEvents.push({
      timestamp: new Date(),
      operation,
      duration,
      success
    });

    // Keep only recent events (last 1000)
    if (this.performanceEvents.length > 1000) {
      this.performanceEvents = this.performanceEvents.slice(-1000);
    }
  }

  /**
   * Start tracking an operation
   */
  startOperation(operationId: string): void {
    this.activeOperations.add(operationId);
  }

  /**
   * End tracking an operation
   */
  endOperation(operationId: string): void {
    this.activeOperations.delete(operationId);
  }

  /**
   * Set queued operations count
   */
  setQueuedOperations(count: number): void {
    this.queuedOperations = count;
  }

  /**
   * Get current metrics snapshot
   */
  getMetricsSnapshot(): MetricsSnapshot {
    const now = new Date();
    const timeWindow = 5 * 60 * 1000; // 5 minutes
    const cutoff = new Date(now.getTime() - timeWindow);

    return {
      timestamp: now,
      retry: this.calculateRetryMetrics(cutoff),
      circuitBreakers: this.calculateCircuitBreakerMetrics(cutoff),
      errors: this.calculateErrorMetrics(cutoff),
      performance: this.calculatePerformanceMetrics(cutoff),
      systemHealth: this.calculateSystemHealthMetrics()
    };
  }

  /**
   * Calculate retry metrics
   */
  private calculateRetryMetrics(cutoff: Date): RetryMetrics {
    const recentRetries = this.retryEvents.filter(event => event.timestamp >= cutoff);
    
    if (recentRetries.length === 0) {
      return {
        totalAttempts: 0,
        successfulRetries: 0,
        failedRetries: 0,
        averageRetryDelay: 0,
        maxRetryDelay: 0,
        retrySuccessRate: 0
      };
    }

    const successful = recentRetries.filter(r => r.success).length;
    const failed = recentRetries.length - successful;
    const delays = recentRetries.map(r => r.delay);

    return {
      totalAttempts: recentRetries.length,
      successfulRetries: successful,
      failedRetries: failed,
      averageRetryDelay: delays.reduce((a, b) => a + b, 0) / delays.length,
      maxRetryDelay: Math.max(...delays),
      retrySuccessRate: recentRetries.length > 0 ? successful / recentRetries.length : 0
    };
  }

  /**
   * Calculate circuit breaker metrics
   */
  private calculateCircuitBreakerMetrics(cutoff: Date): CircuitBreakerMetrics[] {
    const recentEvents = this.circuitBreakerEvents.filter(event => event.timestamp >= cutoff);
    const serviceMetrics = new Map<string, CircuitBreakerMetrics>();

    for (const event of recentEvents) {
      if (!serviceMetrics.has(event.serviceName)) {
        serviceMetrics.set(event.serviceName, {
          serviceName: event.serviceName,
          currentState: event.newState,
          totalStateChanges: 0,
          timeInOpen: 0,
          timeInHalfOpen: 0,
          timeInClosed: 0,
          failureRate: 0,
          lastStateChange: event.timestamp
        });
      }

      const metrics = serviceMetrics.get(event.serviceName)!;
      metrics.totalStateChanges++;
      metrics.currentState = event.newState;
      metrics.lastStateChange = event.timestamp;
    }

    return Array.from(serviceMetrics.values());
  }

  /**
   * Calculate error metrics
   */
  private calculateErrorMetrics(cutoff: Date): ErrorMetrics {
    const recentErrors = this.errorEvents.filter(event => event.timestamp >= cutoff);
    
    const errorsByCategory: Record<string, number> = {};
    const errorsByService: Record<string, number> = {};
    const errorsByOperation: Record<string, number> = {};
    
    let totalResolutionTime = 0;
    let resolvedErrors = 0;

    for (const error of recentErrors) {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
      errorsByService[error.service] = (errorsByService[error.service] || 0) + 1;
      errorsByOperation[error.operation] = (errorsByOperation[error.operation] || 0) + 1;

      if (error.resolved && error.resolutionTime) {
        totalResolutionTime += error.resolutionTime;
        resolvedErrors++;
      }
    }

    const totalOperations = this.performanceEvents.filter(event => event.timestamp >= cutoff).length;

    return {
      totalErrors: recentErrors.length,
      errorsByCategory,
      errorsByService,
      errorsByOperation,
      errorRate: totalOperations > 0 ? recentErrors.length / totalOperations : 0,
      averageErrorResolutionTime: resolvedErrors > 0 ? totalResolutionTime / resolvedErrors : 0
    };
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(cutoff: Date): PerformanceMetrics {
    const recentPerformance = this.performanceEvents.filter(event => event.timestamp >= cutoff);
    
    if (recentPerformance.length === 0) {
      return {
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        throughput: 0,
        concurrentOperations: this.activeOperations.size,
        queuedOperations: this.queuedOperations
      };
    }

    const durations = recentPerformance.map(p => p.duration).sort((a, b) => a - b);
    const timeWindowMinutes = 5;

    return {
      averageResponseTime: durations.reduce((a, b) => a + b, 0) / durations.length,
      p95ResponseTime: durations[Math.floor(durations.length * 0.95)] || 0,
      p99ResponseTime: durations[Math.floor(durations.length * 0.99)] || 0,
      throughput: recentPerformance.length / timeWindowMinutes,
      concurrentOperations: this.activeOperations.size,
      queuedOperations: this.queuedOperations
    };
  }

  /**
   * Calculate system health metrics
   */
  private calculateSystemHealthMetrics(): SystemHealthMetrics {
    const uptime = Date.now() - this.startTime.getTime();
    
    // In a real implementation, you'd get actual system metrics
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    // Calculate health score based on various factors
    let healthScore = 100;
    
    // Reduce score based on error rate
    const recentSnapshot = this.getMetricsSnapshot();
    if (recentSnapshot.errors.errorRate > 0.1) healthScore -= 20;
    if (recentSnapshot.errors.errorRate > 0.2) healthScore -= 30;
    
    // Reduce score based on memory usage
    if (memoryUsagePercent > 80) healthScore -= 15;
    if (memoryUsagePercent > 90) healthScore -= 25;

    return {
      uptime,
      memoryUsage: memoryUsagePercent,
      cpuUsage: 0, // Would need actual CPU monitoring
      activeConnections: this.activeOperations.size,
      healthScore: Math.max(0, healthScore)
    };
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.retryEvents = [];
    this.circuitBreakerEvents = [];
    this.errorEvents = [];
    this.performanceEvents = [];
    this.activeOperations.clear();
    this.queuedOperations = 0;
    this.startTime = new Date();
  }

  /**
   * Export metrics for external monitoring
   */
  exportMetrics(): string {
    return JSON.stringify(this.getMetricsSnapshot(), null, 2);
  }
}
