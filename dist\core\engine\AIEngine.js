"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIEngine = void 0;
const ProviderFactory_1 = require("../providers/ProviderFactory");
const ToolExecutionEngine_1 = require("../tools/base/ToolExecutionEngine");
const prompts_1 = require("../prompts");
class AIEngine {
    configManager;
    toolRegistry;
    toolExecutionEngine;
    providerFactory;
    systemPromptRegistry;
    toolConfirmationHandler;
    constructor(configManager, toolRegistry, toolConfirmationHandler) {
        this.configManager = configManager;
        this.toolRegistry = toolRegistry;
        this.toolExecutionEngine = new ToolExecutionEngine_1.ToolExecutionEngine(toolRegistry, toolConfirmationHandler);
        this.providerFactory = new ProviderFactory_1.ProviderFactory();
        this.systemPromptRegistry = new prompts_1.SystemPromptRegistry({ configManager });
        this.toolConfirmationHandler = toolConfirmationHandler;
    }
    setToolConfirmationHandler(handler) {
        this.toolConfirmationHandler = handler;
        this.toolExecutionEngine.setConfirmationHandler(handler);
    }
    async processMessage(messages, options = {}) {
        const provider = this.getProvider(options.provider);
        const availableTools = this.getAvailableTools(options.tools);
        console.log(`AIEngine: Starting message processing with provider: ${provider.name}`);
        console.log(`AIEngine: Available tools count: ${availableTools.length}`);
        console.log(`AIEngine: Available tool names: ${availableTools.map(t => t.name).join(', ')}`);
        try {
            // Get effective system prompt (from registry or options)
            const effectiveSystemPrompt = await this.getEffectiveSystemPrompt(options.systemPrompt);
            const messagesWithSystemPrompt = await this.injectSystemPrompt(messages, effectiveSystemPrompt);
            console.log(`AIEngine: Sending ${messagesWithSystemPrompt.length} messages to ${provider.name}`);
            // Send initial message to AI provider
            const response = await provider.sendMessage(messagesWithSystemPrompt, availableTools, {
                model: options.model,
                temperature: options.temperature,
                maxTokens: options.maxTokens,
                stream: false
            });
            console.log(`AIEngine: Response received from ${provider.name}`);
            console.log(`AIEngine: Response content length: ${response.content?.length || 0}`);
            console.log(`AIEngine: Response has toolCalls: ${!!response.toolCalls}`);
            console.log(`AIEngine: Response toolCalls count: ${response.toolCalls?.length || 0}`);
            if (response.toolCalls && response.toolCalls.length > 0) {
                console.log(`AIEngine: Tool calls found:`, response.toolCalls.map(tc => `${tc.name}(${Object.keys(tc.parameters || {}).join(', ')})`));
            }
            // Handle tool calls if present
            const toolResults = [];
            if (response.toolCalls && response.toolCalls.length > 0) {
                console.log(`AIEngine: Processing ${response.toolCalls.length} tool calls`);
                for (const toolCall of response.toolCalls) {
                    console.log(`AIEngine: Executing tool call: ${toolCall.name} with params:`, toolCall.parameters);
                    const result = await this.executeToolCall(toolCall);
                    console.log(`AIEngine: Tool call ${toolCall.name} result: ${result.success ? 'success' : 'failed'}`);
                    toolResults.push(result);
                }
            }
            else {
                console.log(`AIEngine: No tool calls found in response`);
            }
            // If tools were executed, send results back to AI for final response
            if (toolResults.length > 0) {
                console.log(`AIEngine: Creating tool messages from ${toolResults.length} tool results`);
                const toolMessages = this.createToolMessages(toolResults);
                console.log(`AIEngine: Created ${toolMessages.length} tool messages`);
                const finalMessages = [...messagesWithSystemPrompt, response, ...toolMessages];
                console.log(`AIEngine: Sending ${finalMessages.length} messages back to AI for final response`);
                const finalResponse = await provider.sendMessage(finalMessages, [], {
                    model: options.model,
                    temperature: options.temperature,
                    maxTokens: options.maxTokens,
                    stream: false
                });
                console.log(`AIEngine: Final response received from ${provider.name}`);
                console.log(`AIEngine: Final response content length: ${finalResponse.content?.length || 0}`);
                // Debug: Log if final response is empty
                if (!finalResponse.content || finalResponse.content.trim().length === 0) {
                    console.warn('AIEngine: Final response after tool execution is empty');
                    console.warn(`AIEngine: Tool results count: ${toolResults.length}`);
                    console.warn(`AIEngine: Tool results: ${toolResults.map(r => `${r.toolName}: ${r.success ? 'success' : 'failed'}`).join(', ')}`);
                    console.warn(`AIEngine: Tool messages count: ${toolMessages.length}`);
                    console.warn(`AIEngine: Final messages count: ${finalMessages.length}`);
                }
                return {
                    message: finalResponse,
                    toolCalls: toolResults,
                    usage: finalResponse.metadata?.usage,
                    finishReason: finalResponse.metadata?.finishReason
                };
            }
            console.log(`AIEngine: Returning response without tool execution`);
            return {
                message: response,
                toolCalls: toolResults,
                usage: response.metadata?.usage,
                finishReason: response.metadata?.finishReason
            };
        }
        catch (error) {
            throw new Error(`AI Engine processing failed: ${error}`);
        }
    }
    async *streamMessage(messages, options = {}) {
        const provider = this.getProvider(options.provider);
        const availableTools = this.getAvailableTools(options.tools);
        if (!provider.streamMessage) {
            throw new Error('Provider does not support streaming');
        }
        try {
            let fullContent = '';
            const generator = provider.streamMessage(messages, availableTools, {
                model: options.model,
                temperature: options.temperature,
                maxTokens: options.maxTokens,
                stream: true
            });
            let finalMessage;
            for await (const chunk of generator) {
                if (typeof chunk === 'string') {
                    fullContent += chunk;
                    yield chunk;
                }
                else {
                    finalMessage = chunk;
                }
            }
            // Handle tool calls if present
            const toolResults = [];
            if (finalMessage.toolCalls && finalMessage.toolCalls.length > 0) {
                for (const toolCall of finalMessage.toolCalls) {
                    const result = await this.executeToolCall(toolCall);
                    toolResults.push(result);
                }
                // If tools were executed, send results back to AI for final response
                if (toolResults.length > 0) {
                    const toolMessages = this.createToolMessages(toolResults);
                    const finalMessages = [...messages, finalMessage, ...toolMessages];
                    const finalResponse = await provider.sendMessage(finalMessages, [], {
                        model: options.model,
                        temperature: options.temperature,
                        maxTokens: options.maxTokens,
                        stream: false
                    });
                    // Debug: Log if final response is empty
                    if (!finalResponse.content || finalResponse.content.trim().length === 0) {
                        console.warn('AIEngine (stream): Final response after tool execution is empty');
                        console.warn(`Tool results count: ${toolResults.length}`);
                        console.warn(`Tool results: ${toolResults.map(r => `${r.toolName}: ${r.success ? 'success' : 'failed'}`).join(', ')}`);
                    }
                    return {
                        message: finalResponse,
                        toolCalls: toolResults,
                        usage: finalResponse.metadata?.usage,
                        finishReason: finalResponse.metadata?.finishReason
                    };
                }
            }
            return {
                message: finalMessage,
                toolCalls: toolResults,
                usage: finalMessage.metadata?.usage,
                finishReason: finalMessage.metadata?.finishReason
            };
        }
        catch (error) {
            throw new Error(`AI Engine streaming failed: ${error}`);
        }
    }
    getProvider(providerName) {
        const name = providerName || this.configManager.getDefaultProvider();
        const config = this.configManager.getProviderConfigForClient(name);
        return ProviderFactory_1.ProviderFactory.createProvider(name, config);
    }
    getAvailableTools(requestedTools) {
        if (requestedTools) {
            return requestedTools;
        }
        // Get all enabled tools from registry
        const allTools = this.toolRegistry.getAllTools();
        const filteredTools = allTools.filter(tool => {
            const category = tool.getCategory();
            return this.configManager.isToolCategoryEnabled(category);
        });
        // Debug: Log available tools
        console.log(`AIEngine: Available tools count: ${filteredTools.length}`);
        console.log(`AIEngine: Available tools: ${filteredTools.map(t => t.name).join(', ')}`);
        return filteredTools;
    }
    async executeToolCall(toolCall) {
        try {
            const result = await this.toolExecutionEngine.executeSingleTool(toolCall.name, toolCall.parameters, {
                requireConfirmation: this.configManager.requiresToolConfirmation(),
                timeout: 30000,
                retries: 0
            });
            // Add the tool call ID to the result for proper message formatting
            return {
                ...result,
                toolCallId: toolCall.id
            };
        }
        catch (error) {
            return {
                success: false,
                content: '',
                error: `Tool execution failed: ${error}`,
                toolName: toolCall.name,
                toolCallId: toolCall.id,
                executionTime: 0,
                context: {}
            };
        }
    }
    createToolMessages(toolResults) {
        return toolResults.map(result => ({
            id: this.generateId(),
            role: 'tool',
            content: result.success ? result.content : result.error || 'Tool execution failed',
            timestamp: new Date(),
            metadata: {
                toolCallId: result.toolCallId, // Use the actual tool call ID, not the tool name
                toolResult: result
            }
        }));
    }
    generateId() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    // Utility methods
    async testProvider(providerName) {
        try {
            const provider = this.getProvider(providerName);
            return await provider.testConnection();
        }
        catch (error) {
            return false;
        }
    }
    async getAvailableModels(providerName) {
        try {
            const provider = this.getProvider(providerName);
            return await provider.getAvailableModels();
        }
        catch (error) {
            return [];
        }
    }
    getRegisteredTools() {
        return this.toolRegistry.getAllTools();
    }
    getToolsByCategory(category) {
        return this.toolRegistry.getToolsByCategory(category);
    }
    isToolEnabled(toolName) {
        return this.toolRegistry.isToolEnabled(toolName);
    }
    enableTool(toolName) {
        this.toolRegistry.enableTool(toolName);
    }
    disableTool(toolName) {
        this.toolRegistry.disableTool(toolName);
    }
    async getEffectiveSystemPrompt(optionsSystemPrompt) {
        // Priority: options.systemPrompt > active system prompt from registry > provider default
        if (optionsSystemPrompt) {
            return optionsSystemPrompt;
        }
        try {
            await this.systemPromptRegistry.initialize();
            const activePrompt = this.systemPromptRegistry.getActivePrompt();
            return activePrompt?.content;
        }
        catch (error) {
            console.warn('Failed to get active system prompt from registry:', error);
            return undefined;
        }
    }
    async injectSystemPrompt(messages, systemPrompt) {
        if (!systemPrompt) {
            return messages;
        }
        // Check if there's already a system message
        const hasSystemMessage = messages.length > 0 && messages[0].role === 'system';
        if (hasSystemMessage) {
            // Replace existing system message
            return [
                {
                    id: `system-${Date.now()}`,
                    role: 'system',
                    content: systemPrompt,
                    timestamp: new Date()
                },
                ...messages.slice(1)
            ];
        }
        else {
            // Add system message at the beginning
            return [
                {
                    id: `system-${Date.now()}`,
                    role: 'system',
                    content: systemPrompt,
                    timestamp: new Date()
                },
                ...messages
            ];
        }
    }
    getSystemPromptRegistry() {
        return this.systemPromptRegistry;
    }
}
exports.AIEngine = AIEngine;
//# sourceMappingURL=AIEngine.js.map