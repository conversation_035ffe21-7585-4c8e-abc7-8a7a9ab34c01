"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LSTool = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const BaseTool_1 = require("../base/BaseTool");
class LSTool extends BaseTool_1.BaseTool {
    name = 'list_directory';
    description = 'Lists contents of directories with detailed information';
    requiresConfirmation = false;
    parameters = {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'Directory path to list (absolute or relative)',
            },
            recursive: {
                type: 'boolean',
                description: 'Whether to list subdirectories recursively',
                default: false,
            },
            showHidden: {
                type: 'boolean',
                description: 'Whether to show hidden files and directories',
                default: false,
            },
            sortBy: {
                type: 'string',
                enum: ['name', 'size', 'modified', 'type'],
                description: 'Sort entries by specified criteria',
                default: 'name',
            },
            maxDepth: {
                type: 'number',
                description: 'Maximum depth for recursive listing',
                default: 3,
            },
        },
        required: ['path'],
    };
    constructor() {
        super({
            category: 'filesystem',
            tags: ['directory', 'listing', 'files'],
            version: '1.0.0',
            dangerous: false,
            requiresConfirmation: false,
        });
    }
    async validate(params) {
        const errors = [];
        const warnings = [];
        // Validate required parameters
        const requiredValidation = this.validateRequiredParams(params, ['path']);
        if (!requiredValidation.valid) {
            return requiredValidation;
        }
        // Validate path parameter
        const pathErrors = this.validateStringParam(params.path, 'path');
        errors.push(...pathErrors);
        // Validate optional parameters
        if (params.recursive !== undefined) {
            errors.push(...this.validateBooleanParam(params.recursive, 'recursive'));
        }
        if (params.showHidden !== undefined) {
            errors.push(...this.validateBooleanParam(params.showHidden, 'showHidden'));
        }
        if (params.sortBy !== undefined) {
            const validSortOptions = ['name', 'size', 'modified', 'type'];
            if (!validSortOptions.includes(params.sortBy)) {
                errors.push(`Parameter 'sortBy' must be one of: ${validSortOptions.join(', ')}`);
            }
        }
        if (params.maxDepth !== undefined) {
            errors.push(...this.validateNumberParam(params.maxDepth, 'maxDepth', {
                min: 1,
                max: 10,
                integer: true,
            }));
        }
        // Check if path exists and is accessible
        try {
            const resolvedPath = path.resolve(params.path);
            const stats = await fs.stat(resolvedPath);
            if (!stats.isDirectory()) {
                errors.push(`Path '${params.path}' is not a directory`);
            }
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                errors.push(`Directory '${params.path}' does not exist`);
            }
            else if (error.code === 'EACCES') {
                errors.push(`Permission denied accessing '${params.path}'`);
            }
            else {
                errors.push(`Cannot access directory '${params.path}': ${error}`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    async execute(params) {
        try {
            const { path: dirPath, recursive = false, showHidden = false, sortBy = 'name', maxDepth = 3, } = params;
            const resolvedPath = path.resolve(dirPath);
            const entries = await this.listDirectory(resolvedPath, recursive, showHidden, sortBy, maxDepth, 0);
            const formattedOutput = this.formatDirectoryListing(entries, resolvedPath);
            const summary = this.createSummary(entries);
            return this.createSuccessResult(formattedOutput, `Listed ${entries.length} items in ${dirPath}`, {
                path: resolvedPath,
                totalItems: entries.length,
                summary,
            });
        }
        catch (error) {
            return this.createErrorResult(`Failed to list directory: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async listDirectory(dirPath, recursive, showHidden, sortBy, maxDepth, currentDepth) {
        const entries = [];
        try {
            const items = await fs.readdir(dirPath);
            for (const item of items) {
                // Skip hidden files if not requested
                if (!showHidden && item.startsWith('.')) {
                    continue;
                }
                const itemPath = path.join(dirPath, item);
                try {
                    const stats = await fs.stat(itemPath);
                    const entry = {
                        name: item,
                        path: itemPath,
                        relativePath: path.relative(process.cwd(), itemPath),
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.size,
                        modified: stats.mtime,
                        permissions: this.getPermissions(stats),
                        depth: currentDepth,
                    };
                    entries.push(entry);
                    // Recurse into subdirectories if requested
                    if (recursive && stats.isDirectory() && currentDepth < maxDepth) {
                        const subEntries = await this.listDirectory(itemPath, recursive, showHidden, sortBy, maxDepth, currentDepth + 1);
                        entries.push(...subEntries);
                    }
                }
                catch (error) {
                    // Skip items that can't be accessed
                    continue;
                }
            }
        }
        catch (error) {
            throw new Error(`Cannot read directory ${dirPath}: ${error}`);
        }
        return this.sortEntries(entries, sortBy);
    }
    sortEntries(entries, sortBy) {
        return entries.sort((a, b) => {
            switch (sortBy) {
                case 'size':
                    return b.size - a.size;
                case 'modified':
                    return b.modified.getTime() - a.modified.getTime();
                case 'type':
                    if (a.type !== b.type) {
                        return a.type === 'directory' ? -1 : 1;
                    }
                    return a.name.localeCompare(b.name);
                case 'name':
                default:
                    return a.name.localeCompare(b.name);
            }
        });
    }
    formatDirectoryListing(entries, basePath) {
        if (entries.length === 0) {
            return `Directory ${basePath} is empty`;
        }
        const lines = [];
        lines.push(`Directory listing for: ${basePath}`);
        lines.push('');
        // Group by depth for better readability
        const byDepth = entries.reduce((acc, entry) => {
            if (!acc[entry.depth])
                acc[entry.depth] = [];
            acc[entry.depth].push(entry);
            return acc;
        }, {});
        for (const depth of Object.keys(byDepth).sort((a, b) => parseInt(a) - parseInt(b))) {
            const depthEntries = byDepth[parseInt(depth)];
            for (const entry of depthEntries) {
                const indent = '  '.repeat(entry.depth);
                const typeIcon = entry.type === 'directory' ? '📁' : '📄';
                const size = entry.type === 'file' ? this.formatFileSize(entry.size) : '';
                const modified = entry.modified.toLocaleDateString();
                lines.push(`${indent}${typeIcon} ${entry.name} ${size} ${entry.permissions} ${modified}`);
            }
        }
        return lines.join('\n');
    }
    createSummary(entries) {
        const summary = {
            totalFiles: 0,
            totalDirectories: 0,
            totalSize: 0,
        };
        for (const entry of entries) {
            if (entry.type === 'file') {
                summary.totalFiles++;
                summary.totalSize += entry.size;
            }
            else {
                summary.totalDirectories++;
            }
        }
        return summary;
    }
    getPermissions(stats) {
        const mode = stats.mode;
        let permissions = '';
        // Owner permissions
        permissions += (mode & 0o400) ? 'r' : '-';
        permissions += (mode & 0o200) ? 'w' : '-';
        permissions += (mode & 0o100) ? 'x' : '-';
        // Group permissions
        permissions += (mode & 0o040) ? 'r' : '-';
        permissions += (mode & 0o020) ? 'w' : '-';
        permissions += (mode & 0o010) ? 'x' : '-';
        // Other permissions
        permissions += (mode & 0o004) ? 'r' : '-';
        permissions += (mode & 0o002) ? 'w' : '-';
        permissions += (mode & 0o001) ? 'x' : '-';
        return permissions;
    }
}
exports.LSTool = LSTool;
//# sourceMappingURL=LSTool.js.map