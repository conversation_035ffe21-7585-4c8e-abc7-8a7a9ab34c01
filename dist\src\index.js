#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AICliTerminal = void 0;
const ConfigManager_1 = require("./core/config/ConfigManager");
const AuthManager_1 = require("./cli-interface/components/auth/AuthManager");
const TerminalManager_1 = require("./cli-interface/components/terminal/TerminalManager");
const AIEngine_1 = require("./core/engine/AIEngine");
const types_1 = require("./cli-interface/types");
const tools_1 = require("./core/tools");
class AICliTerminal {
    configManager;
    toolRegistry;
    aiEngine;
    state;
    config;
    constructor() {
        this.configManager = new ConfigManager_1.ConfigManager();
        this.toolRegistry = (0, tools_1.createDefaultToolRegistry)();
        this.aiEngine = new AIEngine_1.AIEngine(this.configManager, this.toolRegistry);
        this.config = types_1.DEFAULT_CLI_CONFIG;
        this.state = {
            currentView: 'auth',
            isAuthenticated: false,
            currentProvider: '',
            currentModel: '',
        };
    }
    async start() {
        try {
            console.log('🚀 Starting AI CLI Terminal...\n');
            // Main application loop
            while (true) {
                switch (this.state.currentView) {
                    case 'auth':
                        await this.handleAuthFlow();
                        break;
                    case 'terminal':
                        await this.handleTerminalFlow();
                        break;
                    case 'config':
                        await this.handleConfigFlow();
                        break;
                    default:
                        console.error('Unknown view state:', this.state.currentView);
                        process.exit(1);
                }
            }
        }
        catch (error) {
            console.error('Fatal error:', error);
            process.exit(1);
        }
    }
    async handleAuthFlow() {
        const authManager = new AuthManager_1.AuthManager(this.state, this.config, this.configManager);
        await authManager.render();
        // Check if user completed authentication
        if (this.state.isAuthenticated) {
            this.state.currentView = 'terminal';
        }
    }
    async handleTerminalFlow() {
        const terminalManager = new TerminalManager_1.TerminalManager(this.state, this.config, this.configManager, this.aiEngine);
        await terminalManager.render();
        // Terminal manager will update state when user wants to exit or change view
    }
    async handleConfigFlow() {
        // Return to auth for configuration
        this.state.currentView = 'auth';
    }
}
exports.AICliTerminal = AICliTerminal;
// Main entry point
async function main() {
    const terminal = new AICliTerminal();
    await terminal.start();
}
// Handle process signals
process.on('SIGINT', () => {
    console.log('\n\n👋 Goodbye!');
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log('\n\n👋 Goodbye!');
    process.exit(0);
});
// Start the application
if (require.main === module) {
    main().catch(error => {
        console.error('Failed to start AI CLI Terminal:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map