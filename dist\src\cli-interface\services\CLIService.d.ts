import { CLIState, CLIConfig } from '../types';
import { ConfigManager } from '../../core/config/ConfigManager';
import { AIEngine } from '../../core/engine';
export declare class CLIService {
    private state;
    private config;
    private configManager;
    private aiEngine;
    private components;
    private toolConfirmationHandler;
    private errorHandler;
    private progressIndicator;
    constructor(configManager: ConfigManager);
    private initializeComponents;
    start(): Promise<void>;
    renderCurrentView(): Promise<void>;
    switchView(view: CLIState['currentView']): Promise<void>;
    handleInput(input: string): Promise<void>;
    getState(): CLIState;
    updateState(updates: Partial<CLIState>): void;
    getConfig(): CLIConfig;
    updateConfig(updates: Partial<CLIConfig>): void;
    getAIEngine(): AIEngine;
    getConfigManager(): ConfigManager;
    shutdown(): Promise<void>;
    goToAuth(): Promise<void>;
    goToTerminal(): Promise<void>;
    goToConfig(): Promise<void>;
    goToHistory(): Promise<void>;
    authenticate(provider: string, model: string): Promise<void>;
    logout(): Promise<void>;
    switchProvider(provider: string): Promise<void>;
    switchModel(model: string): Promise<void>;
    startNewConversation(): Promise<void>;
    loadConversation(conversationId: string): Promise<void>;
    handleError(error: Error): Promise<void>;
    /**
     * Execute an operation with comprehensive error handling and user feedback
     */
    executeWithErrorHandling<T>(operation: () => Promise<T>, operationName: string, options?: {
        showProgress?: boolean;
        allowCancel?: boolean;
        userFriendlyName?: string;
    }): Promise<T>;
    /**
     * Cancel an ongoing operation
     */
    cancelOperation(operationId: string): void;
    /**
     * Get error handling metrics for debugging
     */
    getErrorMetrics(): any;
    isAuthenticated(): boolean;
    getCurrentProvider(): string;
    getCurrentModel(): string;
    getCurrentView(): CLIState['currentView'];
}
//# sourceMappingURL=CLIService.d.ts.map