import { MenuOption, CLITheme } from '../../types';
export declare class Menu {
    private options;
    private theme;
    private title?;
    constructor(options: MenuOption[], theme: CLITheme, title?: string);
    show(): Promise<void>;
    addOption(option: MenuOption): void;
    removeOption(key: string): void;
    enableOption(key: string): void;
    disableOption(key: string): void;
}
//# sourceMappingURL=Menu.d.ts.map