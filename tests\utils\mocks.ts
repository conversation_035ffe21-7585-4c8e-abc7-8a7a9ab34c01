import { jest } from '@jest/globals';
import { CLIState, CLIConfig } from '../../src/cli-interface/types';
import { AppConfig, ErrorHandlingConfiguration } from '../../src/core/types';

// Mock CLI State
export const createMockCLIState = (overrides: Partial<CLIState> = {}): CLIState => ({
  isAuthenticated: true,
  currentView: 'main',
  currentProvider: 'openai',
  sessionId: 'test-session-123',
  ...overrides
});

// Mock CLI Config
export const createMockCLIConfig = (overrides: Partial<CLIConfig> = {}): CLIConfig => ({
  theme: {
    primary: 'blue',
    secondary: 'green',
    accent: 'yellow',
    background: 'black',
    text: 'white'
  },
  ...overrides
});

// Mock App Config
export const createMockAppConfig = (overrides: Partial<AppConfig> = {}): AppConfig => ({
  defaultProvider: 'openai',
  configDir: '~/.ai-cli-terminal',
  providers: {},
  tools: {
    enableShellTools: true,
    enableWebTools: true,
    enableFileTools: true,
    enableMemoryTools: true,
    requireConfirmation: true,
    sandboxMode: false,
    maxFileSize: '10MB',
    allowedCommands: ['ls', 'cat', 'grep'],
    blockedCommands: ['rm', 'sudo']
  },
  security: {
    requireToolConfirmation: true,
    sandboxMode: false,
    maxFileSize: 10 * 1024 * 1024,
    allowedPaths: [],
    blockedPaths: [],
    allowedDomains: [],
    blockedDomains: []
  },
  ui: {
    theme: 'default',
    showTimestamps: true,
    showTokenCounts: true,
    maxHistorySize: 1000,
    autoSave: true,
    streamResponses: true
  },
  systemPrompts: {
    prompts: {},
    categories: ['General'],
    activePromptId: undefined,
    defaultPromptId: undefined
  },
  errorHandling: createMockErrorHandlingConfig(),
  preferences: {
    requireToolConfirmation: true
  },
  ...overrides
});

// Mock Error Handling Configuration
export const createMockErrorHandlingConfig = (overrides: Partial<ErrorHandlingConfiguration> = {}): ErrorHandlingConfiguration => ({
  retry: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 60000,
    backoffMultiplier: 2,
    backoffStrategy: 'exponential',
    enableJitter: true,
    jitterType: 'full',
    attemptTimeout: 30000,
    ...overrides.retry
  },
  circuitBreaker: {
    enabled: true,
    failureThreshold: 5,
    recoveryTimeout: 30000,
    successThreshold: 2,
    monitoringWindow: 60000,
    halfOpenTimeout: 10000,
    failureRateThreshold: 50,
    minimumRequests: 10,
    enablePerServiceBreakers: true,
    enableGlobalBreaker: false,
    ...overrides.circuitBreaker
  },
  enableErrorLogging: true,
  enableMetrics: true,
  showRetryProgress: true,
  allowRetryCancel: true,
  operationPolicies: {
    toolExecution: {
      maxAttempts: 2,
      baseDelay: 1000,
      backoffMultiplier: 2
    },
    providerCalls: {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000
    },
    fileOperations: {
      maxAttempts: 2,
      baseDelay: 500,
      backoffMultiplier: 1.5
    },
    networkRequests: {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 60000
    }
  },
  ...overrides
});

// Mock ConfigManager
export const createMockConfigManager = () => ({
  getConfig: jest.fn().mockReturnValue(createMockAppConfig()),
  getErrorHandlingConfig: jest.fn().mockReturnValue(createMockErrorHandlingConfig()),
  updateErrorHandlingConfig: jest.fn().mockResolvedValue(undefined),
  resetErrorHandlingConfig: jest.fn().mockResolvedValue(undefined),
  saveConfig: jest.fn().mockResolvedValue(undefined),
  loadConfig: jest.fn().mockResolvedValue(createMockAppConfig())
});

// Mock Error Handler
export const createMockErrorHandler = () => ({
  getMetrics: jest.fn().mockResolvedValue({
    totalOperations: 100,
    successfulOperations: 95,
    failedOperations: 5,
    operationsWithRetries: 8,
    retryMetrics: {
      totalRetryAttempts: 15,
      successfulRetries: 12,
      failedRetries: 3,
      averageRetryDelay: 2000,
      retryAttemptDistribution: { '1': 5, '2': 2, '3': 1 },
      recentTrends: {
        retryRate: 8.0,
        averageDelay: 2000,
        peakRetryTime: '2024-01-15 14:30:00'
      }
    },
    circuitBreakerMetrics: {
      globalStatus: 'CLOSED',
      perServiceStatus: { openai: 'CLOSED', anthropic: 'CLOSED' },
      totalTrips: 1,
      totalRecoveries: 1,
      currentOpenCircuits: 0,
      averageRecoveryTime: 30000,
      recentEvents: []
    },
    perServiceMetrics: {
      openai: {
        totalRequests: 80,
        successRate: 96,
        averageResponseTime: 1200,
        errorRate: 4,
        circuitBreakerStatus: 'CLOSED'
      }
    },
    performanceMetrics: {
      averageOperationTime: 1200,
      slowestOperationTime: 5000,
      fastestOperationTime: 200,
      p95ResponseTime: 2500,
      p99ResponseTime: 4000,
      trends: {
        responseTimeTrend: 'stable',
        errorRateTrend: 'improving',
        throughputTrend: 'increasing'
      },
      resourceUtilization: {
        memoryUsage: 128,
        cpuUsage: 15,
        activeConnections: 3
      }
    },
    recentErrors: []
  }),
  updateConfig: jest.fn(),
  executeWithRetry: jest.fn(),
  executeWithCircuitBreaker: jest.fn()
});

// Mock CLIUtils
export const createMockCLIUtils = () => ({
  clearScreen: jest.fn(),
  showBanner: jest.fn(),
  showSuccess: jest.fn(),
  showError: jest.fn(),
  showWarning: jest.fn(),
  showInfo: jest.fn(),
  waitForKeyPress: jest.fn().mockResolvedValue(undefined),
  prompt: jest.fn(),
  confirm: jest.fn().mockResolvedValue(true),
  select: jest.fn(),
  multiSelect: jest.fn(),
  input: jest.fn(),
  number: jest.fn(),
  password: jest.fn()
});

// Mock Inquirer
export const createMockInquirer = () => ({
  prompt: jest.fn().mockResolvedValue({}),
  createPromptModule: jest.fn()
});

// Error classes for testing
export class TestError extends Error {
  constructor(message: string, public code?: string, public retryable: boolean = true) {
    super(message);
    this.name = 'TestError';
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network error') {
    super(message);
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'Operation timed out') {
    super(message);
    this.name = 'TimeoutError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
  }
}
