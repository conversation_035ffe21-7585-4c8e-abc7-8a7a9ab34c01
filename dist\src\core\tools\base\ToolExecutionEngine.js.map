{"version": 3, "file": "ToolExecutionEngine.js", "sourceRoot": "", "sources": ["../../../../../src/core/tools/base/ToolExecutionEngine.ts"], "names": [], "mappings": ";;;AAEA,0FAA+H;AAwB/H,MAAa,mBAAmB;IACtB,QAAQ,CAAe;IACvB,mBAAmB,CAA2B;IAC9C,cAAc,GAAW,KAAK,CAAC,CAAC,aAAa;IAC7C,cAAc,GAAW,CAAC,CAAC;IAC3B,uBAAuB,CAA2B;IAE1D,YACE,QAAsB,EACtB,mBAA6C,EAC7C,mBAAgD;QAEhD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,iDAAuB,CAAC,mBAAmB,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAEM,sBAAsB,CAAC,OAAgC;QAC5D,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;IACrC,CAAC;IAEM,0BAA0B,CAAC,UAAmC;QACnE,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC;IAC5C,CAAC;IAEM,0BAA0B;QAC/B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,QAAgB,EAChB,UAA+B,EAC/B,UAAgC,EAAE;QAElC,MAAM,EACJ,mBAAmB,GAAG,IAAI,EAC1B,OAAO,GAAG,IAAI,CAAC,cAAc,EAC7B,OAAO,GAAG,IAAI,CAAC,cAAc,EAC7B,OAAO,GAAG,EAAE,EACZ,2BAA2B,GAAG,IAAI,EAClC,gBAAgB,EAChB,WAAW,GAAG,IAAI,EACnB,GAAG,OAAO,CAAC;QAEZ,sCAAsC;QACtC,IAAI,mBAAmB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;YAC5F,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,EAAE;oBACX,KAAK,EAAE,+EAA+E;oBACtF,QAAQ;oBACR,aAAa,EAAE,CAAC;oBAChB,OAAO;iBACR,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACxF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,EAAE;oBACX,KAAK,EAAE,mCAAmC;oBAC1C,QAAQ;oBACR,aAAa,EAAE,CAAC;oBAChB,OAAO;iBACR,CAAC;YACJ,CAAC;QACH,CAAC;QAED,uDAAuD;QACvD,IAAI,2BAA2B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC,gCAAgC,CAC1C,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,gBAAgB,CACjB,CAAC;QACJ,CAAC;QAED,iCAAiC;QACjC,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gCAAgC,CAC5C,QAAgB,EAChB,UAA+B,EAC/B,OAA6B,EAC7B,OAAe,EACf,gBAAmC;QAEnC,MAAM,oBAAoB,GAAyB;YACjD,aAAa,EAAE,eAAe;YAC9B,aAAa,EAAE,QAAQ;YACvB,WAAW,EAAE,QAAQ,QAAQ,EAAE;YAC/B,QAAQ,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;SAClC,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,IAAI,EAAE;YAC3B,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC/E,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAwB,CAAC,wBAAwB,CACzE,SAAS,EACT,oBAAoB,EACpB,gBAAgB,CACjB,CAAC;QAEF,yDAAyD;QACzD,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,MAAM,CAAC,MAAM,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe;gBACrE,QAAQ;gBACR,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,OAAO;gBACP,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,uBAAuB,EAAE,MAAM,CAAC,uBAAuB;oBACvD,eAAe,EAAE,MAAM,CAAC,eAAe;oBACvC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;iBAC1C;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,QAAgB,EAChB,UAA+B,EAC/B,OAA6B,EAC7B,OAAe,EACf,OAAe;QAEf,IAAI,SAAS,GAAW,EAAE,CAAC;QAC3B,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC;YACpD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBAErF,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;oBAC1C,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,SAAS,GAAG,MAAM,CAAC,KAAK,IAAI,eAAe,CAAC;gBAE5C,0CAA0C;gBAC1C,IAAI,OAAO,GAAG,OAAO,EAAE,CAAC;oBACtB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAEnE,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;oBACxB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,EAAE;wBACX,KAAK,EAAE,gBAAgB,OAAO,GAAG,CAAC,cAAc,SAAS,EAAE;wBAC3D,QAAQ;wBACR,aAAa,EAAE,CAAC;wBAChB,OAAO;qBACR,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,gBAAgB,OAAO,GAAG,CAAC,cAAc,SAAS,EAAE;YAC3D,QAAQ;YACR,aAAa,EAAE,CAAC;YAChB,OAAO;SACR,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAC/B,KAA0B,EAC1B,UAAgC,EAAE;QAElC,MAAM,OAAO,GAA0B,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;QACnC,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAE3B,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC3C,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CACxE,CAAC;YAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,4CAA4C;gBAC5C,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/D,MAAM,IAAI,KAAK,CAAC,gEAAgE,cAAc,EAAE,CAAC,CAAC;YACpG,CAAC;YAED,8CAA8C;YAC9C,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtE,yBAAyB;YACzB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,gBAAgB,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAChD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAChE,CAAC;gBAEF,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;gBAEjC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC3B,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5B,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACL,CAAC;YAED,2BAA2B;YAC3B,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACrF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC5B,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEzC,0CAA0C;gBAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1D,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,CAAC,QAAQ,aAAa,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,SAIE,EACF,UAAgC,EAAE;QAElC,MAAM,OAAO,GAA0B,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,IAAI,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAExC,gDAAgD;YAChD,IAAI,IAAI,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBACtD,UAAU,GAAG,EAAE,GAAG,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACrE,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAChF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,6BAA6B;YAC7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,yBAAyB;gBACzB,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,QAAgB,EAChB,UAA+B,EAC/B,OAA6B,EAC7B,OAAe;QAEf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,kCAAkC,OAAO,IAAI,CAAC,CAAC,CAAC;YACnE,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;iBACrD,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,cAAc,CAAC,QAAgB;QACrC,uEAAuE;QACvE,MAAM,aAAa,GAAG,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;QAC1D,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAEM,iBAAiB,CAAC,OAAe;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEM,iBAAiB,CAAC,OAAe;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,SAGE;QAMF,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE7F,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,MAAM,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,WAAmB;QACxC,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC7B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,uBAAuB,CAAC,wBAAwB,EAAE,CAAC;QACjE,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,EAAE,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,MAA2C;QAC1E,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,KAAK,SAAS,CAAC;IACpD,CAAC;CACF;AA5YD,kDA4YC"}