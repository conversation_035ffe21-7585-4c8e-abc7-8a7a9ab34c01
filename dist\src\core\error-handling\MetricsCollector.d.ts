/**
 * Metrics Collection and Monitoring System
 *
 * Provides comprehensive metrics collection for:
 * - Retry attempts and success rates
 * - Circuit breaker state changes
 * - Error rates and categorization
 * - Performance monitoring
 * - System health indicators
 */
export interface RetryMetrics {
    totalAttempts: number;
    successfulRetries: number;
    failedRetries: number;
    averageRetryDelay: number;
    maxRetryDelay: number;
    retrySuccessRate: number;
}
export interface CircuitBreakerMetrics {
    serviceName: string;
    currentState: string;
    totalStateChanges: number;
    timeInOpen: number;
    timeInHalfOpen: number;
    timeInClosed: number;
    failureRate: number;
    lastStateChange: Date;
}
export interface ErrorMetrics {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsByService: Record<string, number>;
    errorsByOperation: Record<string, number>;
    errorRate: number;
    averageErrorResolutionTime: number;
}
export interface PerformanceMetrics {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    throughput: number;
    concurrentOperations: number;
    queuedOperations: number;
}
export interface SystemHealthMetrics {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
    healthScore: number;
}
export interface MetricsSnapshot {
    timestamp: Date;
    retry: RetryMetrics;
    circuitBreakers: CircuitBreakerMetrics[];
    errors: ErrorMetrics;
    performance: PerformanceMetrics;
    systemHealth: SystemHealthMetrics;
}
export interface MetricEvent {
    type: 'retry' | 'circuit_breaker' | 'error' | 'performance' | 'system';
    timestamp: Date;
    data: any;
    metadata?: Record<string, any>;
}
export declare class MetricsCollector {
    private retryEvents;
    private circuitBreakerEvents;
    private errorEvents;
    private performanceEvents;
    private startTime;
    private activeOperations;
    private queuedOperations;
    /**
     * Record a retry attempt
     */
    recordRetryAttempt(operation: string, attempt: number, delay: number, success: boolean): void;
    /**
     * Record circuit breaker state change
     */
    recordCircuitBreakerStateChange(serviceName: string, oldState: string, newState: string): void;
    /**
     * Record an error occurrence
     */
    recordError(category: string, service: string, operation: string): string;
    /**
     * Mark an error as resolved
     */
    recordErrorResolution(errorId: string): void;
    /**
     * Record performance data
     */
    recordPerformance(operation: string, duration: number, success: boolean): void;
    /**
     * Start tracking an operation
     */
    startOperation(operationId: string): void;
    /**
     * End tracking an operation
     */
    endOperation(operationId: string): void;
    /**
     * Set queued operations count
     */
    setQueuedOperations(count: number): void;
    /**
     * Get current metrics snapshot
     */
    getMetricsSnapshot(): MetricsSnapshot;
    /**
     * Calculate retry metrics
     */
    private calculateRetryMetrics;
    /**
     * Calculate circuit breaker metrics
     */
    private calculateCircuitBreakerMetrics;
    /**
     * Calculate error metrics
     */
    private calculateErrorMetrics;
    /**
     * Calculate performance metrics
     */
    private calculatePerformanceMetrics;
    /**
     * Calculate system health metrics
     */
    private calculateSystemHealthMetrics;
    /**
     * Generate unique ID
     */
    private generateId;
    /**
     * Clear all metrics
     */
    clearMetrics(): void;
    /**
     * Export metrics for external monitoring
     */
    exportMetrics(): string;
}
//# sourceMappingURL=MetricsCollector.d.ts.map