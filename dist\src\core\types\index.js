"use strict";
// Core types for the AI CLI Terminal system
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigError = exports.ToolError = exports.ProviderError = exports.AICliError = exports.DEFAULT_APP_CONFIG = exports.DEFAULT_PROVIDER_MODELS = exports.SUPPORTED_PROVIDER_NAMES = exports.SUPPORTED_PROVIDERS = void 0;
// Supported providers
exports.SUPPORTED_PROVIDERS = {
    openai: {
        name: 'openai',
        displayName: 'OpenAI',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        description: 'OpenAI GPT models'
    },
    anthropic: {
        name: 'anthropic',
        displayName: 'Anthropic',
        models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1'],
        description: 'Anthropic Claude models'
    },
    google: {
        name: 'google',
        displayName: 'Google',
        models: ['gemini-pro', 'gemini-pro-vision'],
        description: 'Google Gemini models'
    },
    deepseek: {
        name: 'deepseek',
        displayName: 'Deepseek',
        models: ['deepseek-chat', 'deepseek-coder'],
        description: 'Deepseek AI models'
    }
};
exports.SUPPORTED_PROVIDER_NAMES = [
    'openai',
    'anthropic',
    'google',
    'deepseek'
];
// Default configurations
exports.DEFAULT_PROVIDER_MODELS = {
    openai: 'gpt-4',
    anthropic: 'claude-3-sonnet-20240229',
    google: 'gemini-pro',
    deepseek: 'deepseek-chat'
};
exports.DEFAULT_APP_CONFIG = {
    defaultProvider: 'openai',
    configDir: '~/.ai-cli-terminal',
    providers: {},
    tools: {
        enableShellTools: true,
        enableWebTools: true,
        enableFileTools: true,
        enableMemoryTools: true,
        requireConfirmation: true,
        sandboxMode: false,
        maxFileSize: '10MB',
        allowedCommands: ['ls', 'cat', 'grep', 'find', 'pwd', 'echo'],
        blockedCommands: ['rm', 'sudo', 'chmod', 'chown', 'dd', 'mkfs']
    },
    security: {
        requireToolConfirmation: true,
        sandboxMode: false,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedPaths: [],
        blockedPaths: ['/etc', '/usr', '/bin', '/sbin', '/var'],
        allowedDomains: [],
        blockedDomains: []
    },
    ui: {
        theme: 'default',
        showTimestamps: true,
        showTokenCounts: true,
        maxHistorySize: 1000,
        autoSave: true,
        streamResponses: true
    },
    systemPrompts: {
        prompts: {},
        categories: ['General', 'Software Engineering', 'CLI Agent', 'Custom'],
        activePromptId: undefined,
        defaultPromptId: undefined
    },
    errorHandling: {
        retry: {
            maxAttempts: 3,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 60000,
            backoffStrategy: 'exponential',
            enableJitter: true,
            jitterType: 'full',
            attemptTimeout: 30000
        },
        circuitBreaker: {
            enabled: true,
            failureThreshold: 5,
            recoveryTimeout: 30000,
            successThreshold: 2,
            monitoringWindow: 60000,
            halfOpenTimeout: 10000,
            failureRateThreshold: 50,
            minimumRequests: 10,
            enablePerServiceBreakers: true,
            enableGlobalBreaker: false
        },
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: true,
        allowRetryCancel: true,
        operationPolicies: {
            toolExecution: {
                maxAttempts: 2,
                baseDelay: 1000,
                backoffMultiplier: 2
            },
            providerCalls: {
                maxAttempts: 3,
                baseDelay: 1000,
                backoffMultiplier: 2,
                maxDelay: 30000
            },
            fileOperations: {
                maxAttempts: 2,
                baseDelay: 500,
                backoffMultiplier: 1.5
            },
            networkRequests: {
                maxAttempts: 3,
                baseDelay: 1000,
                backoffMultiplier: 2,
                maxDelay: 60000
            }
        }
    },
    preferences: {
        requireToolConfirmation: true
    }
};
// Error types
class AICliError extends Error {
    code;
    details;
    constructor(message, code = 'UNKNOWN_ERROR', details) {
        super(message);
        this.name = 'AICliError';
        this.code = code;
        this.details = details;
    }
}
exports.AICliError = AICliError;
class ProviderError extends AICliError {
    constructor(message, provider, details) {
        super(message, 'PROVIDER_ERROR', { provider, ...details });
        this.name = 'ProviderError';
    }
}
exports.ProviderError = ProviderError;
class ToolError extends AICliError {
    constructor(message, toolName, details) {
        super(message, 'TOOL_ERROR', { toolName, ...details });
        this.name = 'ToolError';
    }
}
exports.ToolError = ToolError;
class ConfigError extends AICliError {
    constructor(message, details) {
        super(message, 'CONFIG_ERROR', details);
        this.name = 'ConfigError';
    }
}
exports.ConfigError = ConfigError;
//# sourceMappingURL=index.js.map