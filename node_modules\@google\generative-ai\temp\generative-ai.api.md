## API Report File for "@google/generative-ai"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

// @public
export interface BaseParams {
    // (undocumented)
    generationConfig?: GenerationConfig;
    // (undocumented)
    safetySettings?: SafetySetting[];
}

// @public
export interface BatchEmbedContentsRequest {
    // (undocumented)
    requests: EmbedContentRequest[];
}

// @public
export interface BatchEmbedContentsResponse {
    // (undocumented)
    embeddings: ContentEmbedding[];
}

// @public
export enum BlockReason {
    // (undocumented)
    BLOCKED_REASON_UNSPECIFIED = "BLOCKED_REASON_UNSPECIFIED",
    // (undocumented)
    OTHER = "OTHER",
    // (undocumented)
    SAFETY = "SAFETY"
}

// @public
export interface CachedContent extends CachedContentBase {
    createTime?: string;
    // (undocumented)
    name?: string;
    ttl?: string;
    updateTime?: string;
}

// @public (undocumented)
export interface CachedContentBase {
    // (undocumented)
    contents: Content[];
    // (undocumented)
    displayName?: string;
    expireTime?: string;
    // (undocumented)
    model?: string;
    // (undocumented)
    systemInstruction?: string | Part | Content;
    // (undocumented)
    toolConfig?: ToolConfig;
    // (undocumented)
    tools?: Tool[];
}

// @public
export class ChatSession {
    constructor(apiKey: string, model: string, params?: StartChatParams, requestOptions?: RequestOptions);
    getHistory(): Promise<Content[]>;
    // (undocumented)
    model: string;
    // (undocumented)
    params?: StartChatParams;
    // (undocumented)
    requestOptions?: RequestOptions;
    sendMessage(request: string | Array<string | Part>): Promise<GenerateContentResult>;
    sendMessageStream(request: string | Array<string | Part>): Promise<GenerateContentStreamResult>;
}

// @public
export interface CitationMetadata {
    // (undocumented)
    citationSources: CitationSource[];
}

// @public
export interface CitationSource {
    // (undocumented)
    endIndex?: number;
    // (undocumented)
    license?: string;
    // (undocumented)
    startIndex?: number;
    // (undocumented)
    uri?: string;
}

// @public
export interface CodeExecutionResult {
    outcome: Outcome;
    output: string;
}

// @public
export interface CodeExecutionResultPart {
    // (undocumented)
    codeExecutionResult: CodeExecutionResult;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text?: never;
}

// @public
export interface CodeExecutionTool {
    codeExecution: {};
}

// @public
export interface Content {
    // (undocumented)
    parts: Part[];
    // (undocumented)
    role: string;
}

// @public
export interface ContentEmbedding {
    // (undocumented)
    values: number[];
}

// @public
export interface CountTokensRequest {
    // (undocumented)
    contents?: Content[];
    // (undocumented)
    generateContentRequest?: GenerateContentRequest;
}

// @internal
export interface _CountTokensRequestInternal {
    // (undocumented)
    contents?: Content[];
    // (undocumented)
    generateContentRequest?: _GenerateContentRequestInternal;
}

// @public
export interface CountTokensResponse {
    // (undocumented)
    totalTokens: number;
}

// @public
export interface EmbedContentRequest {
    // (undocumented)
    content: Content;
    // (undocumented)
    taskType?: TaskType;
    // (undocumented)
    title?: string;
}

// @public
export interface EmbedContentResponse {
    // (undocumented)
    embedding: ContentEmbedding;
}

// @public
export interface EnhancedGenerateContentResponse extends GenerateContentResponse {
    // @deprecated
    functionCall: () => FunctionCall | undefined;
    functionCalls: () => FunctionCall[] | undefined;
    text: () => string;
}

// @public
export interface ErrorDetails {
    // (undocumented)
    "@type"?: string;
    // (undocumented)
    [key: string]: unknown;
    // (undocumented)
    domain?: string;
    // (undocumented)
    metadata?: Record<string, unknown>;
    // (undocumented)
    reason?: string;
}

// @public
export interface ExecutableCode {
    code: string;
    language: ExecutableCodeLanguage;
}

// @public (undocumented)
export enum ExecutableCodeLanguage {
    // (undocumented)
    LANGUAGE_UNSPECIFIED = "language_unspecified",
    // (undocumented)
    PYTHON = "python"
}

// @public
export interface ExecutableCodePart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode: ExecutableCode;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text?: never;
}

// @public
export interface FileData {
    // (undocumented)
    fileUri: string;
    // (undocumented)
    mimeType: string;
}

// @public
export interface FileDataPart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData: FileData;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text?: never;
}

// @public
export enum FinishReason {
    // (undocumented)
    FINISH_REASON_UNSPECIFIED = "FINISH_REASON_UNSPECIFIED",
    // (undocumented)
    LANGUAGE = "LANGUAGE",
    // (undocumented)
    MAX_TOKENS = "MAX_TOKENS",
    // (undocumented)
    OTHER = "OTHER",
    // (undocumented)
    RECITATION = "RECITATION",
    // (undocumented)
    SAFETY = "SAFETY",
    // (undocumented)
    STOP = "STOP"
}

// @public
export interface FunctionCall {
    // (undocumented)
    args: object;
    // (undocumented)
    name: string;
}

// @public (undocumented)
export interface FunctionCallingConfig {
    // (undocumented)
    allowedFunctionNames?: string[];
    // (undocumented)
    mode?: FunctionCallingMode;
}

// @public (undocumented)
export enum FunctionCallingMode {
    // (undocumented)
    ANY = "ANY",
    // (undocumented)
    AUTO = "AUTO",
    // (undocumented)
    MODE_UNSPECIFIED = "MODE_UNSPECIFIED",
    // (undocumented)
    NONE = "NONE"
}

// @public
export interface FunctionCallPart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall: FunctionCall;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text?: never;
}

// @public
export interface FunctionDeclaration {
    description?: string;
    name: string;
    parameters?: FunctionDeclarationSchema;
}

// @public
export interface FunctionDeclarationSchema {
    description?: string;
    properties: {
        [k: string]: FunctionDeclarationSchemaProperty;
    };
    required?: string[];
    type: FunctionDeclarationSchemaType;
}

// @public
export interface FunctionDeclarationSchemaProperty extends Schema {
}

// @public
export enum FunctionDeclarationSchemaType {
    ARRAY = "ARRAY",
    BOOLEAN = "BOOLEAN",
    INTEGER = "INTEGER",
    NUMBER = "NUMBER",
    OBJECT = "OBJECT",
    STRING = "STRING"
}

// @public
export interface FunctionDeclarationsTool {
    functionDeclarations?: FunctionDeclaration[];
}

// @public
export interface FunctionResponse {
    // (undocumented)
    name: string;
    // (undocumented)
    response: object;
}

// @public
export interface FunctionResponsePart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse: FunctionResponse;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text?: never;
}

// @public
export interface GenerateContentCandidate {
    // (undocumented)
    citationMetadata?: CitationMetadata;
    // (undocumented)
    content: Content;
    // (undocumented)
    finishMessage?: string;
    // (undocumented)
    finishReason?: FinishReason;
    // (undocumented)
    index: number;
    // (undocumented)
    safetyRatings?: SafetyRating[];
}

// @public
export interface GenerateContentRequest extends BaseParams {
    cachedContent?: string;
    // (undocumented)
    contents: Content[];
    // (undocumented)
    systemInstruction?: string | Part | Content;
    // (undocumented)
    toolConfig?: ToolConfig;
    // (undocumented)
    tools?: Tool[];
}

// @internal
export interface _GenerateContentRequestInternal extends GenerateContentRequest {
    // (undocumented)
    model?: string;
}

// @public
export interface GenerateContentResponse {
    candidates?: GenerateContentCandidate[];
    promptFeedback?: PromptFeedback;
    usageMetadata?: UsageMetadata;
}

// @public
export interface GenerateContentResult {
    // (undocumented)
    response: EnhancedGenerateContentResponse;
}

// @public
export interface GenerateContentStreamResult {
    // (undocumented)
    response: Promise<EnhancedGenerateContentResponse>;
    // (undocumented)
    stream: AsyncGenerator<EnhancedGenerateContentResponse>;
}

// @public
export interface GenerationConfig {
    // (undocumented)
    candidateCount?: number;
    // (undocumented)
    maxOutputTokens?: number;
    responseMimeType?: string;
    responseSchema?: ResponseSchema;
    // (undocumented)
    stopSequences?: string[];
    // (undocumented)
    temperature?: number;
    // (undocumented)
    topK?: number;
    // (undocumented)
    topP?: number;
}

// @public
export interface GenerativeContentBlob {
    data: string;
    // (undocumented)
    mimeType: string;
}

// @public
export class GenerativeModel {
    constructor(apiKey: string, modelParams: ModelParams, requestOptions?: RequestOptions);
    // (undocumented)
    apiKey: string;
    batchEmbedContents(batchEmbedContentRequest: BatchEmbedContentsRequest): Promise<BatchEmbedContentsResponse>;
    // (undocumented)
    cachedContent: CachedContent;
    countTokens(request: CountTokensRequest | string | Array<string | Part>): Promise<CountTokensResponse>;
    embedContent(request: EmbedContentRequest | string | Array<string | Part>): Promise<EmbedContentResponse>;
    generateContent(request: GenerateContentRequest | string | Array<string | Part>): Promise<GenerateContentResult>;
    generateContentStream(request: GenerateContentRequest | string | Array<string | Part>): Promise<GenerateContentStreamResult>;
    // (undocumented)
    generationConfig: GenerationConfig;
    // (undocumented)
    model: string;
    // (undocumented)
    requestOptions: RequestOptions;
    // (undocumented)
    safetySettings: SafetySetting[];
    startChat(startChatParams?: StartChatParams): ChatSession;
    // (undocumented)
    systemInstruction?: Content;
    // (undocumented)
    toolConfig?: ToolConfig;
    // (undocumented)
    tools?: Tool[];
}

// @public
export class GoogleGenerativeAI {
    constructor(apiKey: string);
    // (undocumented)
    apiKey: string;
    getGenerativeModel(modelParams: ModelParams, requestOptions?: RequestOptions): GenerativeModel;
    getGenerativeModelFromCachedContent(cachedContent: CachedContent, requestOptions?: RequestOptions): GenerativeModel;
}

// @public
export class GoogleGenerativeAIError extends Error {
    constructor(message: string);
}

// @public
export class GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {
    constructor(message: string, status?: number, statusText?: string, errorDetails?: ErrorDetails[]);
    // (undocumented)
    errorDetails?: ErrorDetails[];
    // (undocumented)
    status?: number;
    // (undocumented)
    statusText?: string;
}

// @public
export class GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {
}

// @public
export class GoogleGenerativeAIResponseError<T> extends GoogleGenerativeAIError {
    constructor(message: string, response?: T);
    // (undocumented)
    response?: T;
}

// @public
export enum HarmBlockThreshold {
    // (undocumented)
    BLOCK_LOW_AND_ABOVE = "BLOCK_LOW_AND_ABOVE",
    // (undocumented)
    BLOCK_MEDIUM_AND_ABOVE = "BLOCK_MEDIUM_AND_ABOVE",
    // (undocumented)
    BLOCK_NONE = "BLOCK_NONE",
    // (undocumented)
    BLOCK_ONLY_HIGH = "BLOCK_ONLY_HIGH",
    // (undocumented)
    HARM_BLOCK_THRESHOLD_UNSPECIFIED = "HARM_BLOCK_THRESHOLD_UNSPECIFIED"
}

// @public
export enum HarmCategory {
    // (undocumented)
    HARM_CATEGORY_DANGEROUS_CONTENT = "HARM_CATEGORY_DANGEROUS_CONTENT",
    // (undocumented)
    HARM_CATEGORY_HARASSMENT = "HARM_CATEGORY_HARASSMENT",
    // (undocumented)
    HARM_CATEGORY_HATE_SPEECH = "HARM_CATEGORY_HATE_SPEECH",
    // (undocumented)
    HARM_CATEGORY_SEXUALLY_EXPLICIT = "HARM_CATEGORY_SEXUALLY_EXPLICIT",
    // (undocumented)
    HARM_CATEGORY_UNSPECIFIED = "HARM_CATEGORY_UNSPECIFIED"
}

// @public
export enum HarmProbability {
    // (undocumented)
    HARM_PROBABILITY_UNSPECIFIED = "HARM_PROBABILITY_UNSPECIFIED",
    // (undocumented)
    HIGH = "HIGH",
    // (undocumented)
    LOW = "LOW",
    // (undocumented)
    MEDIUM = "MEDIUM",
    // (undocumented)
    NEGLIGIBLE = "NEGLIGIBLE"
}

// @public
export interface InlineDataPart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData: GenerativeContentBlob;
    // (undocumented)
    text?: never;
}

// @public
export interface ModelParams extends BaseParams {
    // (undocumented)
    cachedContent?: CachedContent;
    // (undocumented)
    model: string;
    // (undocumented)
    systemInstruction?: string | Part | Content;
    // (undocumented)
    toolConfig?: ToolConfig;
    // (undocumented)
    tools?: Tool[];
}

// @public
export enum Outcome {
    OUTCOME_DEADLINE_EXCEEDED = "outcome_deadline_exceeded",
    OUTCOME_FAILED = "outcome_failed",
    OUTCOME_OK = "outcome_ok",
    OUTCOME_UNSPECIFIED = "outcome_unspecified"
}

// @public
export type Part = TextPart | InlineDataPart | FunctionCallPart | FunctionResponsePart | FileDataPart | ExecutableCodePart | CodeExecutionResultPart;

// @public
export const POSSIBLE_ROLES: readonly ["user", "model", "function", "system"];

// @public
export interface PromptFeedback {
    // (undocumented)
    blockReason: BlockReason;
    // (undocumented)
    blockReasonMessage?: string;
    // (undocumented)
    safetyRatings: SafetyRating[];
}

// @public
export interface RequestOptions {
    apiClient?: string;
    apiVersion?: string;
    baseUrl?: string;
    customHeaders?: Headers | Record<string, string>;
    timeout?: number;
}

// @public
export interface ResponseSchema extends Schema {
}

// @public
export interface SafetyRating {
    // (undocumented)
    category: HarmCategory;
    // (undocumented)
    probability: HarmProbability;
}

// @public
export interface SafetySetting {
    // (undocumented)
    category: HarmCategory;
    // (undocumented)
    threshold: HarmBlockThreshold;
}

// @public
export interface Schema {
    description?: string;
    enum?: string[];
    example?: unknown;
    format?: string;
    items?: FunctionDeclarationSchema;
    nullable?: boolean;
    properties?: {
        [k: string]: FunctionDeclarationSchema;
    };
    required?: string[];
    type?: FunctionDeclarationSchemaType;
}

// @public
export interface StartChatParams extends BaseParams {
    cachedContent?: string;
    // (undocumented)
    history?: Content[];
    // (undocumented)
    systemInstruction?: string | Part | Content;
    // (undocumented)
    toolConfig?: ToolConfig;
    // (undocumented)
    tools?: Tool[];
}

// @public
export enum TaskType {
    // (undocumented)
    CLASSIFICATION = "CLASSIFICATION",
    // (undocumented)
    CLUSTERING = "CLUSTERING",
    // (undocumented)
    RETRIEVAL_DOCUMENT = "RETRIEVAL_DOCUMENT",
    // (undocumented)
    RETRIEVAL_QUERY = "RETRIEVAL_QUERY",
    // (undocumented)
    SEMANTIC_SIMILARITY = "SEMANTIC_SIMILARITY",
    // (undocumented)
    TASK_TYPE_UNSPECIFIED = "TASK_TYPE_UNSPECIFIED"
}

// @public
export interface TextPart {
    // (undocumented)
    codeExecutionResult?: never;
    // (undocumented)
    executableCode?: never;
    // (undocumented)
    fileData?: never;
    // (undocumented)
    functionCall?: never;
    // (undocumented)
    functionResponse?: never;
    // (undocumented)
    inlineData?: never;
    // (undocumented)
    text: string;
}

// @public
export type Tool = FunctionDeclarationsTool | CodeExecutionTool;

// @public
export interface ToolConfig {
    // (undocumented)
    functionCallingConfig: FunctionCallingConfig;
}

// @public
export interface UsageMetadata {
    cachedContentTokenCount?: number;
    candidatesTokenCount: number;
    promptTokenCount: number;
    totalTokenCount: number;
}

// (No @packageDocumentation comment for this package)

```
