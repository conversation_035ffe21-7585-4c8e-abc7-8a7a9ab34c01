import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class RetryPolicyConfigMenu extends BaseComponent {
    private configManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private showRetryPolicyMenu;
    private configureBasicSettings;
    private configureBackoffStrategy;
    private configureJitterSettings;
    private testRetryPolicy;
    private showPolicyDetails;
    private calculateMaxRetryTime;
    private getRecommendation;
    private resetRetryPolicy;
}
//# sourceMappingURL=RetryPolicyConfigMenu.d.ts.map