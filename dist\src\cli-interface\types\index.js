"use strict";
// CLI Interface specific types
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CLI_CONFIG = exports.DEFAULT_CLI_THEME = void 0;
exports.DEFAULT_CLI_THEME = {
    primary: 'cyan',
    secondary: 'blue',
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue',
    muted: 'gray',
    accent: 'magenta',
    background: 'black',
    text: 'white',
};
exports.DEFAULT_CLI_CONFIG = {
    theme: exports.DEFAULT_CLI_THEME,
    terminal: {
        prompt: '🤖 AI CLI',
        maxHistorySize: 1000,
        autoSave: true,
        showTimestamps: true,
        showTokenCounts: true,
        streamResponses: true,
    },
    ui: {
        animations: true,
        sounds: false,
        compactMode: false,
        showBanner: true,
    },
    showTimestamps: true,
    showTokenCounts: true,
};
//# sourceMappingURL=index.js.map