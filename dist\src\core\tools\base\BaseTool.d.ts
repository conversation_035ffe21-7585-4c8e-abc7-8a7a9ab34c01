import { BaseTool as IBaseTool, ToolResult } from '../../types';
export interface ToolMetadata {
    category: 'filesystem' | 'execution' | 'web' | 'memory' | 'mcp' | 'utility';
    tags: string[];
    version: string;
    author?: string;
    dangerous: boolean;
    requiresConfirmation: boolean;
}
export interface ToolValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export declare abstract class BaseTool implements IBaseTool {
    abstract name: string;
    abstract description: string;
    abstract parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
    abstract requiresConfirmation: boolean;
    protected metadata: ToolMetadata;
    constructor(metadata: ToolMetadata);
    abstract validate(params: Record<string, any>): Promise<ToolValidationResult>;
    abstract execute(params: Record<string, any>): Promise<ToolResult>;
    shouldConfirmExecute(params: Record<string, any>): Promise<boolean>;
    getMetadata(): ToolMetadata;
    getCategory(): string;
    getTags(): string[];
    isDangerous(): boolean;
    getVersion(): string;
    protected createSuccessResult(content: string, displayContent?: string, metadata?: Record<string, any>): ToolResult;
    protected createErrorResult(error: string, metadata?: Record<string, any>): ToolResult;
    protected validateRequiredParams(params: Record<string, any>, requiredParams: string[]): ToolValidationResult;
    protected validateStringParam(value: any, paramName: string, options?: {
        minLength?: number;
        maxLength?: number;
        pattern?: RegExp;
        allowEmpty?: boolean;
    }): string[];
    protected validateNumberParam(value: any, paramName: string, options?: {
        min?: number;
        max?: number;
        integer?: boolean;
    }): string[];
    protected validateBooleanParam(value: any, paramName: string): string[];
    protected validateArrayParam(value: any, paramName: string, options?: {
        minLength?: number;
        maxLength?: number;
        itemType?: 'string' | 'number' | 'boolean' | 'object';
    }): string[];
    protected validatePathParam(value: any, paramName: string): string[];
    protected sanitizePath(path: string): string;
    protected formatFileSize(bytes: number): string;
    protected truncateContent(content: string, maxLength?: number): string;
    toString(): string;
}
//# sourceMappingURL=BaseTool.d.ts.map