/**
 * Provider-specific error handling configurations and utilities
 *
 * This module provides specialized error handling configurations for different
 * AI providers, taking into account their specific rate limits, error patterns,
 * and service characteristics.
 */
import { ErrorHandlingConfiguration } from '../types';
/**
 * Provider-specific error handling configurations
 */
export declare const PROVIDER_ERROR_CONFIGS: Record<string, Partial<ErrorHandlingConfiguration>>;
/**
 * Get error handling configuration for a specific provider
 */
export declare function getProviderErrorConfig(providerName: string): Partial<ErrorHandlingConfiguration>;
/**
 * Merge user-provided error configuration with provider defaults
 */
export declare function mergeProviderErrorConfig(providerName: string, userConfig?: Partial<ErrorHandlingConfiguration>): ErrorHandlingConfiguration;
/**
 * Provider-specific error classification rules
 */
export declare const PROVIDER_ERROR_PATTERNS: Record<string, Record<string, RegExp[]>>;
/**
 * Check if an error matches a specific pattern for a provider
 */
export declare function matchesProviderErrorPattern(providerName: string, error: Error, patternType: 'rateLimitErrors' | 'authenticationErrors' | 'serviceUnavailableErrors' | 'networkErrors'): boolean;
/**
 * Get recommended retry delay for provider-specific rate limit errors
 */
export declare function getProviderRateLimitDelay(providerName: string, error: Error): number;
//# sourceMappingURL=ProviderErrorHandling.d.ts.map