{"version": 3, "file": "ErrorClassifier.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorClassifier.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;AAEH,IAAY,aAmBX;AAnBD,WAAY,aAAa;IACvB,kCAAkC;IAClC,gDAA+B,CAAA;IAC/B,gDAA+B,CAAA;IAC/B,sDAAqC,CAAA;IACrC,8CAA6B,CAAA;IAC7B,4DAA2C,CAAA;IAE3C,sCAAsC;IACtC,8DAA6C,CAAA;IAC7C,4DAA2C,CAAA;IAC3C,sDAAqC,CAAA;IACrC,oDAAmC,CAAA;IACnC,wDAAuC,CAAA;IAEvC,gBAAgB;IAChB,gEAA+C,CAAA;IAC/C,4DAA2C,CAAA;IAC3C,gDAA+B,CAAA;AACjC,CAAC,EAnBW,aAAa,6BAAb,aAAa,QAmBxB;AA6BD,MAAa,eAAe;IAClB,KAAK,GAA8B,EAAE,CAAC;IAE9C;QACE,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAY;QACnB,wCAAwC;QACxC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC/C,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBACnB,OAAO;oBACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;oBAC3C,UAAU;oBACV,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC;iBACxD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,OAAO;YACL,QAAQ,EAAE,aAAa,CAAC,aAAa;YACrC,WAAW,EAAE,KAAK;YAClB,kBAAkB,EAAE,KAAK;YACzB,UAAU,EAAE,GAAG;YACf,eAAe,EAAE,uDAAuD;SACzE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,KAAY,EAAE,IAA6B;QAC3D,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAE1B,yBAAyB;QACzB,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC7B,WAAW,EAAE,CAAC;YACd,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CACtD,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CACxC,CAAC;YACF,IAAI,OAAO;gBAAE,UAAU,EAAE,CAAC;QAC5B,CAAC;QAED,uCAAuC;QACvC,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC9C,WAAW,EAAE,CAAC;YACd,MAAM,MAAM,GAAI,KAAa,CAAC,MAAM,CAAC;YACrC,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1C,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxB,WAAW,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;YACzC,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC3B,WAAW,EAAE,CAAC;YACd,IAAI,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,OAAO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,KAAK,GAAG;YACX,6BAA6B;YAC7B;gBACE,QAAQ,EAAE,aAAa,CAAC,aAAa;gBACrC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE;oBACR,eAAe,EAAE;wBACf,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW;wBAClD,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM;qBAChD;oBACD,UAAU,EAAE,CAAC,cAAc,EAAE,iBAAiB,CAAC;iBAChD;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,CAAC;iBACrB;aACF;YAED,6BAA6B;YAC7B;gBACE,QAAQ,EAAE,aAAa,CAAC,aAAa;gBACrC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,CAAC;oBAC9D,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;iBACxB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,CAAC;iBACrB;aACF;YAED,+CAA+C;YAC/C;gBACE,QAAQ,EAAE,aAAa,CAAC,gBAAgB;gBACxC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;oBACtE,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,CAAC;oBACpB,QAAQ,EAAE,KAAK;iBAChB;aACF;YAED,4BAA4B;YAC5B;gBACE,QAAQ,EAAE,aAAa,CAAC,YAAY;gBACpC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE;oBACR,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;iBAClC;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,CAAC;iBACrB;aACF;YAED,kCAAkC;YAClC;gBACE,QAAQ,EAAE,aAAa,CAAC,mBAAmB;gBAC3C,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,qBAAqB,EAAE,yBAAyB,CAAC;oBACnE,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF;YAED,oCAAoC;YACpC;gBACE,QAAQ,EAAE,aAAa,CAAC,oBAAoB;gBAC5C,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE;wBACf,cAAc,EAAE,gBAAgB,EAAE,iBAAiB;wBACnD,eAAe,EAAE,eAAe;qBACjC;oBACD,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF;YAED,mCAAmC;YACnC;gBACE,QAAQ,EAAE,aAAa,CAAC,mBAAmB;gBAC3C,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,0BAA0B,CAAC;oBAC3E,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF;YAED,gCAAgC;YAChC;gBACE,QAAQ,EAAE,aAAa,CAAC,gBAAgB;gBACxC,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE;wBACf,YAAY,EAAE,eAAe,EAAE,aAAa;wBAC5C,WAAW,EAAE,mBAAmB;qBACjC;oBACD,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;iBACxB;aACF;YAED,+BAA+B;YAC/B;gBACE,QAAQ,EAAE,aAAa,CAAC,eAAe;gBACvC,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;oBAChD,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF;YAED,4CAA4C;YAC5C;gBACE,QAAQ,EAAE,aAAa,CAAC,qBAAqB;gBAC7C,WAAW,EAAE,KAAK;gBAClB,kBAAkB,EAAE,KAAK;gBACzB,QAAQ,EAAE;oBACR,eAAe,EAAE,CAAC,iBAAiB,CAAC;oBACpC,UAAU,EAAE,CAAC,qBAAqB,CAAC;iBACpC;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAuB;QAChD,MAAM,OAAO,GAAkC;YAC7C,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,0CAA0C;YACzE,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,qCAAqC;YACpE,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,kDAAkD;YACpF,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,gCAAgC;YAC9D,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,+CAA+C;YACpF,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,yCAAyC;YAC/E,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,sCAAsC;YAC3E,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,mCAAmC;YACrE,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,4CAA4C;YAC7E,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE,qCAAqC;YACxE,CAAC,aAAa,CAAC,qBAAqB,CAAC,EAAE,oDAAoD;YAC3F,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,iCAAiC;YACtE,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,uDAAuD;SACvF,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,+CAA+C,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAA6B;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;IACnE,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAuB;QAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAuB,EAAE,OAAyC;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAC3E,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAY;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,cAAc,CAAC,WAAW,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,KAAY;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,cAAc,CAAC,kBAAkB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAY;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC;IACjD,CAAC;CACF;AApTD,0CAoTC"}