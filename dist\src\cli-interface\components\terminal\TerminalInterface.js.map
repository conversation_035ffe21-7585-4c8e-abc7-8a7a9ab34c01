{"version": 3, "file": "TerminalInterface.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/terminal/TerminalInterface.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AAExD,mEAAmE;AAEnE,MAAa,iBAAkB,SAAQ,6BAAa;IAC1C,iBAAiB,CAAuB;IAEhD,YAAY,KAAe,EAAE,MAAiB;QAC5C,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,wCAAoB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEO,YAAY;QAClB,MAAM,KAAK,GAAG,oBAAoB,CAAC;QACnC,MAAM,QAAQ,GAAG,0BAA0B,CAAC;QAE5C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEO,eAAe;QACrB,MAAM,WAAW,GAAG;YAClB,aAAa,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACxF,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YAClF,WAAW,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;SACzE,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACxD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACnF,CAAC;IAEM,YAAY;QACjB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzE,OAAO,GAAG,YAAY,GAAG,CAAC;IAC5B,CAAC;IAEM,qBAAqB;QAC1B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC/C,CAAC;IAEM,mBAAmB;QACxB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;IAC3B,CAAC;IAEM,WAAW,CAAC,KAAa;QAC9B,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEM,aAAa,CAAC,OAAe;QAClC,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEM,UAAU,CAAC,IAAY;QAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,OAAe,EAAE,WAAmB,EAAE,KAAa,EAAE,KAAY;QACxF,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAC3B,2BAA2B,OAAO,IAAI,WAAW,kBAAkB,YAAY,SAAS,KAAK,CAAC,OAAO,GAAG,CACzG,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,OAAe;QAC1C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,OAAe;QAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAgB;QAC1C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAgB;QAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,WAAmB,EAAE,MAAuC;QAC1F,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,eAAe,WAAW,sDAAsD;YACtF,WAAW,EAAE,cAAc,WAAW,sBAAsB;YAC5D,MAAM,EAAE,KAAK,WAAW,kCAAkC;SAC3D,CAAC;QAEF,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;aAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB;QACjC,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,2CAA2C,CAAC,CAAC;QAEpE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC7C;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,0CAA0C;gBACnD,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,UAAe;QACnE,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC/F,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAChF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,MAAM,cAAc,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG;gBACpE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC;gBACrC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAE1B,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,cAAc,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IAC7E,CAAC;IAEM,gBAAgB,CAAC,QAAgB,EAAE,MAAW,EAAE,QAAiB;QACtE,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,mBAAmB,QAAQ,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAE3F,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACpH,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACxF,CAAC;aAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAClD,kCAAkC;QAClC,+EAA+E;QAC/E,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAEM,sBAAsB,CAAC,SAAe;QAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,OAAO,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;IAC5E,CAAC;IAEM,gBAAgB,CAAC,MAAe;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5C,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,MAAM,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;IAClF,CAAC;CACF;AAxND,8CAwNC"}