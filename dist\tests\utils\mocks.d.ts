import { CLIState, CLIConfig } from '../../src/cli-interface/types';
import { AppConfig, ErrorHandlingConfiguration } from '../../src/core/types';
export declare const createMockCLIState: (overrides?: Partial<CLIState>) => CLIState;
export declare const createMockCLIConfig: (overrides?: Partial<CLIConfig>) => CLIConfig;
export declare const createMockAppConfig: (overrides?: Partial<AppConfig>) => AppConfig;
export declare const createMockErrorHandlingConfig: (overrides?: Partial<ErrorHandlingConfiguration>) => ErrorHandlingConfiguration;
export declare const createMockConfigManager: () => {
    getConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    getErrorHandlingConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    updateErrorHandlingConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    resetErrorHandlingConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    saveConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    loadConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
};
export declare const createMockErrorHandler: () => {
    getMetrics: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    updateConfig: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    executeWithRetry: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    executeWithCircuitBreaker: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
};
export declare const createMockCLIUtils: () => {
    clearScreen: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    showBanner: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    showSuccess: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    showError: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    showWarning: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    showInfo: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    waitForKeyPress: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    prompt: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    confirm: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    select: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    multiSelect: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    input: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    number: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    password: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
};
export declare const createMockInquirer: () => {
    prompt: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
    createPromptModule: import("jest-mock").Mock<import("jest-mock").UnknownFunction>;
};
export declare class TestError extends Error {
    code?: string | undefined;
    retryable: boolean;
    constructor(message: string, code?: string | undefined, retryable?: boolean);
}
export declare class NetworkError extends Error {
    constructor(message?: string);
}
export declare class TimeoutError extends Error {
    constructor(message?: string);
}
export declare class RateLimitError extends Error {
    constructor(message?: string);
}
//# sourceMappingURL=mocks.d.ts.map