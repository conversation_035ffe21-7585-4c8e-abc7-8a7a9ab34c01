{"version": 3, "file": "ErrorHandlingConfigComponent.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/config/ErrorHandlingConfigComponent.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAgC;AAChC,2DAAwD;AAKxD,mEAAgE;AAChE,yEAAsE;AACtE,6EAA0E;AAC1E,6EAA0E;AAE1E,MAAa,4BAA6B,SAAQ,6BAAa;IACrD,aAAa,CAAgB;IAC7B,YAAY,CAA0B;IACtC,eAAe,CAAwB;IACvC,kBAAkB,CAA2B;IAC7C,aAAa,CAA6B;IAC1C,aAAa,CAA6B;IAElD,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B,EAAE,YAAqC;QACjH,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,4BAA4B;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,6CAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC/E,IAAI,CAAC,kBAAkB,GAAG,IAAI,mDAAwB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,GAAG,IAAI,uDAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,GAAG,IAAI,uDAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACpF,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,kCAAkC,EAAE,sEAAsE,CAAC,CAAC;QAElI,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QACjD,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC;QAE5C,MAAM,OAAO,GAAG;YACd;gBACE,IAAI,EAAE,qBAAqB,WAAW,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,WAAW,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE;gBACtH,KAAK,EAAE,OAAO;aACf;YACD;gBACE,IAAI,EAAE,sBAAsB,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,EAAE;gBAC/F,KAAK,EAAE,SAAS;aACjB;YACD;gBACE,IAAI,EAAE,0BAA0B;gBAChC,KAAK,EAAE,SAAS;aACjB;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,KAAK,EAAE,QAAQ;aAChB;YACD;gBACE,IAAI,EAAE,0BAA0B;gBAChC,KAAK,EAAE,SAAS;aACjB;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,KAAK,EAAE,UAAU;aAClB;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,KAAK,EAAE,OAAO;aACf;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,MAAM;aACd;SACF,CAAC;QAEF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACvC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,mCAAmC;gBAC5C,OAAO;aACR;SACF,CAAC,CAAC;QAEH,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACvC,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACrC,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM;YACR,KAAK,UAAU;gBACb,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACvC,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC7B,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC5C,OAAO;QACX,CAAC;QAED,+CAA+C;QAC/C,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;QAE3D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;QAEzE,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,oBAAoB;gBAC1B,OAAO,EAAE,gCAAgC;gBACzC,OAAO,EAAE,aAAa,CAAC,kBAAkB,IAAI,IAAI;aAClD;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,4BAA4B;gBACrC,OAAO,EAAE,aAAa,CAAC,aAAa,IAAI,IAAI;aAC7C;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,+BAA+B;gBACxC,OAAO,EAAE,aAAa,CAAC,iBAAiB,IAAI,IAAI;aACjD;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,yCAAyC;gBAClD,OAAO,EAAE,aAAa,CAAC,gBAAgB,IAAI,IAAI;aAChD;SACF,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uCAAuC,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/G,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,qCAAqC,CAAC,CAAC;QAE7D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;QAEzE,MAAM,OAAO,GAAG;YACd,EAAE,IAAI,EAAE,gCAAgC,EAAE,KAAK,EAAE,oBAAoB,EAAE;YACvE,EAAE,IAAI,EAAE,gCAAgC,EAAE,KAAK,EAAE,sBAAsB,EAAE;YACzE,EAAE,IAAI,EAAE,0BAA0B,EAAE,KAAK,EAAE,SAAS,EAAE;YACtD,EAAE,IAAI,EAAE,0BAA0B,EAAE,KAAK,EAAE,gBAAgB,EAAE;YAC7D,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,MAAM,EAAE;SAChD,CAAC;QAEF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACvC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,qDAAqD;gBAC9D,OAAO;aACR;SACF,CAAC,CAAC;QAEH,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,oBAAoB;gBACvB,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACxC,MAAM;YACR,KAAK,sBAAsB;gBACzB,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBAC1C,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,MAAM;YACR,KAAK,gBAAgB;gBACnB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,MAAM;YACR,KAAK,MAAM;gBACT,OAAO;QACX,CAAC;QAED,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;QAE9D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,iBAAiB,IAAI,EAAE,CAAC;QAE5F,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,uEAAuE,CAAC,CAAC;QAC7F,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,MAAM,UAAU,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QAC3F,MAAM,eAAe,GAAQ,EAAE,CAAC;QAEhC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAE/C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAE3H,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;gBACpC;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,yBAAyB;oBAClC,OAAO,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;oBACjC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,0BAA0B;iBAC5E;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,kBAAkB;oBAC3B,OAAO,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;oBAClC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,GAAG,IAAI,wBAAwB;iBAC9D;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,qBAAqB;oBAC9B,OAAO,EAAE,OAAO,CAAC,iBAAiB,IAAI,CAAC;oBACvC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,oBAAoB;iBACxD;aACF,CAAC,CAAC;YAEH,eAAe,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACvC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC;gBACjD,iBAAiB,EAAE,eAAe;aACnC,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,0CAA0C,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/G,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,4BAA4B;QACxC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAC;QAExD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,sEAAsE,CAAC,CAAC;QAC5F,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,qFAAqF,CAAC,CAAC;QAE9G,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,2EAA2E,CAAC,CAAC;QAE7H,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,oCAAoC;QACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,uCAAuC,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,uFAAuF,CAAC,CAAC;QAC7G,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;QAElD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC;QAEzE,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE;oBACP,EAAE,IAAI,EAAE,2BAA2B,EAAE,KAAK,EAAE,OAAO,EAAE;oBACrD,EAAE,IAAI,EAAE,kCAAkC,EAAE,KAAK,EAAE,MAAM,EAAE;oBAC3D,EAAE,IAAI,EAAE,iCAAiC,EAAE,KAAK,EAAE,MAAM,EAAE;oBAC1D,EAAE,IAAI,EAAE,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE;iBAChD;gBACD,OAAO,EAAE,MAAM;aAChB;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,qCAAqC;gBAC9C,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,gCAAgC;gBACzC,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC;gBACjD,kBAAkB,EAAE,IAAI,CAAC,oCAAoC;aAC9D,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,6CAA6C,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACrH,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;QAElD,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACpC;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,qBAAqB;gBAC3B,OAAO,EAAE,gCAAgC;gBACzC,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,8BAA8B;gBACpC,OAAO,EAAE,kCAAkC;gBAC3C,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,8BAA8B;gBACvC,OAAO,EAAE,IAAI;aACd;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,mCAAmC;gBAC5C,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,wBAAwB;aAC3D;SACF,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC;gBACjD,aAAa,EAAE,IAAI,CAAC,8BAA8B;aACnD,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,6CAA6C,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACrH,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CACxC,wEAAwE,CACzE,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE,CAAC;YAEpD,sDAAsD;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;YAC9D,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAE1C,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iDAAiD,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnH,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;CACF;AApYD,oEAoYC"}