{"version": 3, "file": "ErrorHandlingMiddleware.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorHandlingMiddleware.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAEH,+CAAyD;AACzD,qDAA+F;AAC/F,uDAAmE;AACnE,yDAAsD;AACtD,+CAAsD;AAkCtD,MAAa,uBAAuB;IAC1B,MAAM,CAA6B;IACnC,UAAU,CAAkB;IAC5B,sBAAsB,CAAyB;IAC/C,aAAa,GAA6B,IAAI,GAAG,EAAE,CAAC;IACpD,kBAAkB,GAAyB,IAAI,GAAG,EAAE,CAAC;IACrD,gBAAgB,CAAmB;IACnC,MAAM,CAAc;IAE5B,YAAY,MAAkC;QAC5C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,iCAAe,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,yBAAW,CAAC;YAC5B,KAAK,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAQ,CAAC,KAAK;YAClE,mBAAmB,EAAE,MAAM,CAAC,kBAAkB;YAC9C,uBAAuB,EAAE,IAAI;YAC7B,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,sBAAsB,GAAG,IAAI,uCAAsB,CAAC;YACvD,gBAAgB,EAAE,MAAM,CAAC,cAAc,CAAC,gBAAgB;YACxD,eAAe,EAAE,MAAM,CAAC,cAAc,CAAC,eAAe;YACtD,gBAAgB,EAAE,MAAM,CAAC,cAAc,CAAC,gBAAgB;YACxD,gBAAgB,EAAE,MAAM,CAAC,cAAc,CAAC,gBAAgB;YACxD,aAAa,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE;gBACjD,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAC1E,IAAI,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACzF,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,SAA2B,EAC3B,OAA6B,EAC7B,gBAAmC;QAEnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEtD,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;QAC1F,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/D,MAAM,cAAc,GAAG,OAAO,CAAC,WAAW;gBACxC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC7D,CAAC,CAAC,IAAI,CAAC;YAET,oDAAoD;YACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAC7C,SAAS,EACT,WAAW,EACX,cAAc,EACd,OAAO,EACP,WAAW,EACX,gBAAgB,CACjB,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAChG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE/E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,aAAa,EAAE,MAAM,CAAC,YAAY;gBAClC,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,uBAAuB,EAAE,KAAK;gBAC9B,WAAW,EAAE,kCAAkC;gBAC/C,gBAAgB,EAAE,KAAK;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,YAAY;YACZ,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAC5G,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEhF,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACxE,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,SAA2B,EAC3B,WAAwB,EACxB,cAAqC,EACrC,OAA6B,EAC7B,WAAmB,EACnB,gBAAmC;QAEnC,iEAAiE;QACjE,MAAM,kBAAkB,GAAG,KAAK,IAAgB,EAAE;YAChD,yBAAyB;YACzB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,4CAA4C;YAC5C,IAAI,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzD,OAAO,MAAM,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEF,iDAAiD;QACjD,MAAM,wBAAwB,GAAG,IAAI,yBAAW,CAAC;YAC/C,GAAG,WAAW,CAAC,SAAS,EAAE;YAC1B,WAAW,EAAE,CAAC,KAAY,EAAE,OAAe,EAAE,EAAE;gBAC7C,yBAAyB;gBACzB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,aAAa,oBAAoB,EAAE,EAAE,OAAO,EAAE,EAAE,WAAW,CAAC,CAAC;oBACnG,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,iDAAiD;gBACjD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAEvD,oBAAoB;gBACpB,IAAI,cAAc,CAAC,WAAW,EAAE,CAAC;oBAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBAC5D,IAAI,CAAC,MAAM,CAAC,eAAe,CACzB,OAAO,CAAC,aAAa,EACrB,OAAO,EACP,WAAW,CAAC,SAAS,EAAE,CAAC,WAAW,EACnC,KAAK,EACL,KAAK,EACL,WAAW,CACZ,CAAC;oBAEF,iBAAiB;oBACjB,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBAEvF,yBAAyB;oBACzB,IAAI,gBAAgB,EAAE,cAAc,EAAE,CAAC;wBACrC,gBAAgB,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBAC9F,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,aAAa,8BAA8B,EAAE;wBACpF,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;wBACjC,YAAY,EAAE,KAAK,CAAC,OAAO;wBAC3B,QAAQ,EAAE,cAAc,CAAC,QAAQ;qBAClC,EAAE,WAAW,CAAC,CAAC;gBAClB,CAAC;gBAED,OAAO,cAAc,CAAC,WAAW,CAAC;YACpC,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,MAAM,wBAAwB,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACK,WAAW,CACjB,KAAY,EACZ,OAA6B,EAC7B,QAAgB,EAChB,WAAoB;QAEpB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,qBAAqB,GAAG,KAAK,YAAY,oCAAmB,CAAC;QAEnE,YAAY;QACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,YAAY,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,EACrD,KAAK,EACL;YACE,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,OAAO;YACP,QAAQ;YACR,qBAAqB;SACtB,EACD,WAAW,CACZ,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAC/B,cAAc,CAAC,QAAQ,EACvB,OAAO,CAAC,WAAW,IAAI,SAAS,EAChC,OAAO,CAAC,aAAa,CACtB,CAAC;QAEF,iCAAiC;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAC7E,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAElF,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK;YACL,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,QAAQ;YACvB,uBAAuB,EAAE,qBAAqB;YAC9C,WAAW;YACX,gBAAgB;YAChB,eAAe,EAAE,cAAc,CAAC,eAAe;YAC/C,gBAAgB,EAAE,cAAc,CAAC,WAAW,IAAI,CAAC,qBAAqB;SACvE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,KAAY,EACZ,cAAmB,EACnB,OAA6B;QAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC;QAEjE,QAAQ,cAAc,CAAC,QAAQ,EAAE,CAAC;YAChC,KAAK,+BAAa,CAAC,aAAa;gBAC9B,OAAO,mCAAmC,SAAS,wDAAwD,CAAC;YAE9G,KAAK,+BAAa,CAAC,aAAa;gBAC9B,OAAO,6BAA6B,SAAS,2CAA2C,CAAC;YAE3F,KAAK,+BAAa,CAAC,gBAAgB;gBACjC,OAAO,2BAA2B,SAAS,6CAA6C,CAAC;YAE3F,KAAK,+BAAa,CAAC,oBAAoB;gBACrC,OAAO,6BAA6B,SAAS,sCAAsC,CAAC;YAEtF,KAAK,+BAAa,CAAC,mBAAmB;gBACpC,OAAO,qBAAqB,SAAS,8CAA8C,CAAC;YAEtF,KAAK,+BAAa,CAAC,gBAAgB;gBACjC,OAAO,qBAAqB,SAAS,+CAA+C,CAAC;YAEvF,KAAK,+BAAa,CAAC,eAAe;gBAChC,OAAO,0BAA0B,SAAS,sCAAsC,CAAC;YAEnF,KAAK,+BAAa,CAAC,YAAY;gBAC7B,OAAO,gCAAgC,SAAS,2BAA2B,CAAC;YAE9E,KAAK,+BAAa,CAAC,mBAAmB;gBACpC,OAAO,0CAA0C,SAAS,2BAA2B,CAAC;YAExF,KAAK,+BAAa,CAAC,qBAAqB;gBACtC,OAAO,oFAAoF,CAAC;YAE9F,KAAK,+BAAa,CAAC,mBAAmB;gBACpC,OAAO,2BAA2B,SAAS,+BAA+B,CAAC;YAE7E;gBACE,OAAO,uCAAuC,SAAS,wCAAwC,CAAC;QACpG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAmB;QACjC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,aAAqB;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAA2D,CAAC,CAAC;YAEnH,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,GAAG,eAAe,EAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,yBAAW,CAAC,YAAY,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,cAAc,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAEvG,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,wCAAwC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,WAAwB,EAAE,OAAe;QAClE,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA6B;QACvD,OAAO,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACtH,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,QAAgB,GAAG;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAA2C;QACtD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;QAC5C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,kDAAkD;QAC9E,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,8BAA8B;QAC9B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACvB,KAAK,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,sBAAQ,CAAC,KAAK;YAClE,mBAAmB,EAAE,MAAM,CAAC,kBAAkB;SAC/C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AAnZD,0DAmZC"}