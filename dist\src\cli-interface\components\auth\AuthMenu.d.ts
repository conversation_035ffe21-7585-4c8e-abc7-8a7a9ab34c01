import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class AuthMenu extends BaseComponent {
    private configManager;
    private providerSelector;
    private providerConfigurator;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    show(): Promise<void>;
    private displayProviderStatus;
    private createMenuOptions;
    private handleChangeDefault;
    private handleTestProvider;
    private handleRemoveProvider;
    private handlePreferences;
    private handleExportConfig;
    private handleImportConfig;
    private handleResetConfig;
}
//# sourceMappingURL=AuthMenu.d.ts.map