export interface CLIComponent {
    render(): Promise<void>;
    handleInput?(input: string): Promise<void>;
    cleanup?(): Promise<void>;
}
export interface CLIState {
    currentView: 'auth' | 'terminal' | 'config' | 'history';
    isAuthenticated: boolean;
    currentProvider: string;
    currentModel: string;
    conversationId?: string;
}
export interface TerminalSession {
    id: string;
    startTime: Date;
    messages: TerminalMessage[];
    provider: string;
    model: string;
}
export interface TerminalMessage {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    timestamp: Date;
    metadata?: {
        provider?: string;
        model?: string;
        tokens?: number;
        duration?: number;
        toolCalls?: ToolCallDisplay[];
    };
}
export interface ToolCallDisplay {
    id: string;
    name: string;
    parameters: Record<string, any>;
    status: 'pending' | 'approved' | 'denied' | 'executing' | 'completed' | 'failed';
    result?: any;
    error?: string;
    timestamp: Date;
    duration?: number;
}
export interface CLITheme {
    primary: string;
    secondary: string;
    success: string;
    error: string;
    warning: string;
    info: string;
    muted: string;
    accent: string;
    background: string;
    text: string;
}
export interface InputHandler {
    pattern: RegExp;
    handler: (input: string, matches: RegExpMatchArray) => Promise<void>;
    description: string;
}
export interface MenuOption {
    key: string;
    label: string;
    description?: string;
    action: () => Promise<void>;
    disabled?: boolean;
}
export interface ProgressIndicator {
    start(message: string): void;
    update(message: string): void;
    succeed(message?: string): void;
    fail(message?: string): void;
    stop(): void;
}
export interface TerminalConfig {
    prompt: string;
    maxHistorySize: number;
    autoSave: boolean;
    showTimestamps: boolean;
    showTokenCounts: boolean;
    streamResponses: boolean;
}
export interface UIConfig {
    animations: boolean;
    sounds: boolean;
    compactMode: boolean;
    showBanner: boolean;
}
export interface CLIConfig {
    theme: CLITheme;
    terminal: TerminalConfig;
    ui: UIConfig;
    showTimestamps: boolean;
    showTokenCounts: boolean;
}
export declare const DEFAULT_CLI_THEME: CLITheme;
export declare const DEFAULT_CLI_CONFIG: CLIConfig;
//# sourceMappingURL=index.d.ts.map