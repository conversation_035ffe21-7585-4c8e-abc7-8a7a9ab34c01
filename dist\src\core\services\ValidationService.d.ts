export interface ValidationRule<T = any> {
    name: string;
    validate: (value: T) => boolean | Promise<boolean>;
    message: string;
}
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export declare class ValidationService {
    private rules;
    addRule<T>(field: string, rule: ValidationRule<T>): void;
    addRules<T>(field: string, rules: ValidationRule<T>[]): void;
    validate(data: Record<string, any>): Promise<ValidationResult>;
    validateField<T>(field: string, value: T): Promise<ValidationResult>;
    removeRule(field: string, ruleName: string): boolean;
    removeAllRules(field: string): boolean;
    clear(): void;
    getFields(): string[];
    getRules(field: string): ValidationRule[];
    static readonly COMMON_RULES: {
        required: <T>(message?: string) => ValidationRule<T>;
        minLength: (min: number, message?: string) => ValidationRule<string>;
        maxLength: (max: number, message?: string) => ValidationRule<string>;
        email: (message?: string) => ValidationRule<string>;
        url: (message?: string) => ValidationRule<string>;
        numeric: (message?: string) => ValidationRule<any>;
        integer: (message?: string) => ValidationRule<any>;
        min: (min: number, message?: string) => ValidationRule<number>;
        max: (max: number, message?: string) => ValidationRule<number>;
        pattern: (regex: RegExp, message?: string) => ValidationRule<string>;
        oneOf: <T>(options: T[], message?: string) => ValidationRule<T>;
        custom: <T>(name: string, validator: (value: T) => boolean | Promise<boolean>, message: string) => ValidationRule<T>;
    };
}
export declare class ProviderValidationService extends ValidationService {
    constructor();
    private setupProviderRules;
}
export declare class ToolValidationService extends ValidationService {
    constructor();
    private setupToolRules;
}
//# sourceMappingURL=ValidationService.d.ts.map