/**
 * Comprehensive Test Suite for Error Handling System
 * 
 * Tests all components of the error handling system:
 * - RetryPolicy with exponential backoff and jitter
 * - CircuitBreaker with state transitions
 * - ErrorClassifier with categorization
 * - ErrorHandlingMiddleware integration
 * - MetricsCollector functionality
 * - ErrorLogger capabilities
 */

import { 
  RetryPolicy, 
  CircuitBreaker, 
  ErrorClassifier, 
  ErrorHandlingMiddleware,
  MetricsCollector,
  ErrorLogger,
  LogLevel,
  ErrorCategory,
  createErrorHandlingMiddleware,
  createErrorHandlingContext,
  isRetryableError
} from '../index';

describe('Error Handling System', () => {
  
  describe('RetryPolicy', () => {
    test('should execute operation successfully on first attempt', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 100,
        backoffMultiplier: 2,
        maxDelay: 1000
      });

      const operation = jest.fn().mockResolvedValue('success');
      const result = await policy.execute(operation, 'test-operation');

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.finalAttempt).toBe(1);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    test('should retry on failure and eventually succeed', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 10,
        backoffMultiplier: 2,
        maxDelay: 100
      });

      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success');

      const result = await policy.execute(operation, 'test-operation');

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.finalAttempt).toBe(3);
      expect(operation).toHaveBeenCalledTimes(3);
    });

    test('should fail after max attempts', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 2,
        baseDelay: 10,
        backoffMultiplier: 2,
        maxDelay: 100
      });

      const operation = jest.fn().mockRejectedValue(new Error('Persistent failure'));
      const result = await policy.execute(operation, 'test-operation');

      expect(result.success).toBe(false);
      expect(result.finalAttempt).toBe(2);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    test('should calculate exponential backoff correctly', () => {
      const policy = new RetryPolicy({
        maxAttempts: 5,
        baseDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 10000,
        enableJitter: false
      });

      expect(policy.calculateDelay(1)).toBe(1000);
      expect(policy.calculateDelay(2)).toBe(2000);
      expect(policy.calculateDelay(3)).toBe(4000);
      expect(policy.calculateDelay(4)).toBe(8000);
      expect(policy.calculateDelay(5)).toBe(10000); // Capped at maxDelay
    });
  });

  describe('CircuitBreaker', () => {
    test('should start in CLOSED state', () => {
      const breaker = new CircuitBreaker('test-service', {
        failureThreshold: 3,
        recoveryTimeout: 1000,
        successThreshold: 2
      });

      expect(breaker.getState()).toBe('CLOSED');
    });

    test('should transition to OPEN after failure threshold', async () => {
      const breaker = new CircuitBreaker('test-service', {
        failureThreshold: 2,
        recoveryTimeout: 1000,
        successThreshold: 1
      });

      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // First failure
      await expect(breaker.execute(failingOperation)).rejects.toThrow();
      expect(breaker.getState()).toBe('CLOSED');

      // Second failure - should trigger OPEN
      await expect(breaker.execute(failingOperation)).rejects.toThrow();
      expect(breaker.getState()).toBe('OPEN');

      // Third attempt should be rejected immediately
      await expect(breaker.execute(failingOperation)).rejects.toThrow('Circuit breaker is OPEN');
      expect(failingOperation).toHaveBeenCalledTimes(2); // Not called on third attempt
    });

    test('should transition to HALF_OPEN after recovery timeout', async () => {
      const breaker = new CircuitBreaker('test-service', {
        failureThreshold: 1,
        recoveryTimeout: 50, // Short timeout for testing
        successThreshold: 1
      });

      const failingOperation = jest.fn().mockRejectedValue(new Error('Service failure'));

      // Trigger OPEN state
      await expect(breaker.execute(failingOperation)).rejects.toThrow();
      expect(breaker.getState()).toBe('OPEN');

      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 60));

      // Next call should transition to HALF_OPEN
      const successOperation = jest.fn().mockResolvedValue('success');
      const result = await breaker.execute(successOperation);

      expect(result).toBe('success');
      expect(breaker.getState()).toBe('CLOSED'); // Should transition back to CLOSED after success
    });
  });

  describe('ErrorClassifier', () => {
    const classifier = new ErrorClassifier();

    test('should classify network errors as retryable', () => {
      const networkError = new Error('ECONNREFUSED');
      const classification = classifier.classify(networkError);

      expect(classification.category).toBe(ErrorCategory.NETWORK_ERROR);
      expect(classification.shouldRetry).toBe(true);
    });

    test('should classify timeout errors as retryable', () => {
      const timeoutError = new Error('Request timeout');
      const classification = classifier.classify(timeoutError);

      expect(classification.category).toBe(ErrorCategory.TIMEOUT_ERROR);
      expect(classification.shouldRetry).toBe(true);
    });

    test('should classify authentication errors as non-retryable', () => {
      const authError = new Error('Unauthorized');
      const classification = classifier.classify(authError);

      expect(classification.category).toBe(ErrorCategory.AUTHENTICATION_ERROR);
      expect(classification.shouldRetry).toBe(false);
    });

    test('should classify validation errors as non-retryable', () => {
      const validationError = new Error('Invalid input');
      const classification = classifier.classify(validationError);

      expect(classification.category).toBe(ErrorCategory.VALIDATION_ERROR);
      expect(classification.shouldRetry).toBe(false);
    });
  });

  describe('ErrorHandlingMiddleware', () => {
    test('should execute operation successfully', async () => {
      const middleware = createErrorHandlingMiddleware();
      const context = createErrorHandlingContext('general', 'test-operation');
      
      const operation = jest.fn().mockResolvedValue('success');
      const result = await middleware.executeWithErrorHandling(operation, context);

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.retryAttempts).toBe(1);
    });

    test('should retry on retryable errors', async () => {
      const middleware = createErrorHandlingMiddleware({
        retry: {
          maxAttempts: 3,
          baseDelay: 10,
          backoffMultiplier: 2,
          maxDelay: 100,
          enableJitter: false,
          jitterType: 'full'
        }
      });
      
      const context = createErrorHandlingContext('general', 'test-operation');
      
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('ECONNREFUSED'))
        .mockResolvedValue('success');

      const result = await middleware.executeWithErrorHandling(operation, context);

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.retryAttempts).toBe(2);
    });

    test('should not retry on non-retryable errors', async () => {
      const middleware = createErrorHandlingMiddleware();
      const context = createErrorHandlingContext('general', 'test-operation');
      
      const operation = jest.fn().mockRejectedValue(new Error('Unauthorized'));
      const result = await middleware.executeWithErrorHandling(operation, context);

      expect(result.success).toBe(false);
      expect(result.canRetryManually).toBe(false);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    test('should collect metrics during execution', async () => {
      const middleware = createErrorHandlingMiddleware();
      const context = createErrorHandlingContext('general', 'test-operation');
      
      const operation = jest.fn().mockResolvedValue('success');
      await middleware.executeWithErrorHandling(operation, context);

      const metrics = middleware.getMetricsSnapshot();
      expect(metrics.performance.throughput).toBeGreaterThan(0);
    });

    test('should log operations', async () => {
      const middleware = createErrorHandlingMiddleware();
      const context = createErrorHandlingContext('general', 'test-operation');
      
      const operation = jest.fn().mockResolvedValue('success');
      await middleware.executeWithErrorHandling(operation, context);

      const logs = middleware.getRecentLogs(10);
      expect(logs.length).toBeGreaterThan(0);
      expect(logs.some(log => log.category === 'OPERATION_START')).toBe(true);
      expect(logs.some(log => log.category === 'OPERATION_COMPLETE')).toBe(true);
    });
  });

  describe('MetricsCollector', () => {
    test('should record retry attempts', () => {
      const collector = new MetricsCollector();
      
      collector.recordRetryAttempt('test-op', 1, 1000, false);
      collector.recordRetryAttempt('test-op', 2, 2000, true);

      const snapshot = collector.getMetricsSnapshot();
      expect(snapshot.retry.totalAttempts).toBe(2);
      expect(snapshot.retry.successfulRetries).toBe(1);
      expect(snapshot.retry.failedRetries).toBe(1);
    });

    test('should record circuit breaker state changes', () => {
      const collector = new MetricsCollector();
      
      collector.recordCircuitBreakerStateChange('test-service', 'CLOSED', 'OPEN');
      collector.recordCircuitBreakerStateChange('test-service', 'OPEN', 'HALF_OPEN');

      const snapshot = collector.getMetricsSnapshot();
      expect(snapshot.circuitBreakers.length).toBe(1);
      expect(snapshot.circuitBreakers[0].serviceName).toBe('test-service');
      expect(snapshot.circuitBreakers[0].totalStateChanges).toBe(2);
    });

    test('should record errors', () => {
      const collector = new MetricsCollector();
      
      collector.recordError('NETWORK_ERROR', 'api-service', 'fetch-data');
      collector.recordError('TIMEOUT_ERROR', 'api-service', 'fetch-data');

      const snapshot = collector.getMetricsSnapshot();
      expect(snapshot.errors.totalErrors).toBe(2);
      expect(snapshot.errors.errorsByCategory['NETWORK_ERROR']).toBe(1);
      expect(snapshot.errors.errorsByCategory['TIMEOUT_ERROR']).toBe(1);
    });
  });

  describe('ErrorLogger', () => {
    test('should log messages at different levels', () => {
      const logger = new ErrorLogger({
        level: LogLevel.DEBUG,
        enableConsoleOutput: false
      });

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');

      const logs = logger.getRecentLogs(10);
      expect(logs.length).toBe(4);
      expect(logs.map(l => l.level)).toEqual([
        LogLevel.DEBUG,
        LogLevel.INFO,
        LogLevel.WARN,
        LogLevel.ERROR
      ]);
    });

    test('should filter logs by level', () => {
      const logger = new ErrorLogger({
        level: LogLevel.WARN,
        enableConsoleOutput: false
      });

      logger.debug('Debug message'); // Should be filtered out
      logger.info('Info message');   // Should be filtered out
      logger.warn('Warning message');
      logger.error('Error message');

      const logs = logger.getRecentLogs(10);
      expect(logs.length).toBe(2);
      expect(logs.every(l => l.level >= LogLevel.WARN)).toBe(true);
    });

    test('should search logs by criteria', () => {
      const logger = new ErrorLogger({
        level: LogLevel.DEBUG,
        enableConsoleOutput: false
      });

      logger.info('Test message', { category: 'test' }, 'op-1');
      logger.error('Error message', new Error('Test error'), { category: 'error' }, 'op-2');

      const errorLogs = logger.searchLogs({ level: LogLevel.ERROR });
      expect(errorLogs.length).toBe(1);
      expect(errorLogs[0].message).toBe('Error message');

      const opLogs = logger.searchLogs({ operationId: 'op-1' });
      expect(opLogs.length).toBe(1);
      expect(opLogs[0].operationId).toBe('op-1');
    });
  });

  describe('Utility Functions', () => {
    test('isRetryableError should correctly identify retryable errors', () => {
      expect(isRetryableError(new Error('ECONNREFUSED'))).toBe(true);
      expect(isRetryableError(new Error('timeout'))).toBe(true);
      expect(isRetryableError(new Error('Unauthorized'))).toBe(false);
      expect(isRetryableError(new Error('Invalid input'))).toBe(false);
    });

    test('createErrorHandlingContext should create proper context', () => {
      const context = createErrorHandlingContext('toolExecution', 'test-tool', {
        serviceName: 'tool-service',
        userId: 'user-123',
        metadata: { param: 'value' }
      });

      expect(context.operationType).toBe('toolExecution');
      expect(context.operationName).toBe('test-tool');
      expect(context.serviceName).toBe('tool-service');
      expect(context.userId).toBe('user-123');
      expect(context.metadata).toEqual({ param: 'value' });
    });
  });

  describe('Integration Tests', () => {
    test('should handle complex error scenarios with full middleware', async () => {
      const middleware = createErrorHandlingMiddleware({
        retry: {
          maxAttempts: 3,
          baseDelay: 10,
          backoffMultiplier: 2,
          maxDelay: 100,
          enableJitter: false,
          jitterType: 'full'
        },
        circuitBreaker: {
          failureThreshold: 2,
          recoveryTimeout: 100,
          successThreshold: 1,
          monitoringWindow: 1000,
          enabled: true
        }
      });

      const context = createErrorHandlingContext('providerCall', 'api-call', {
        serviceName: 'external-api'
      });

      // Simulate intermittent failures
      let callCount = 0;
      const operation = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          throw new Error('ECONNREFUSED'); // Retryable error
        }
        return Promise.resolve('success');
      });

      const result = await middleware.executeWithErrorHandling(operation, context);

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.retryAttempts).toBe(3);

      // Check metrics
      const metrics = middleware.getMetricsSnapshot();
      expect(metrics.retry.totalAttempts).toBeGreaterThan(0);
      expect(metrics.performance.throughput).toBeGreaterThan(0);

      // Check logs
      const logs = middleware.getRecentLogs(20);
      expect(logs.some(log => log.category === 'RETRY')).toBe(true);
      expect(logs.some(log => log.category === 'OPERATION_COMPLETE')).toBe(true);
    });
  });
});
