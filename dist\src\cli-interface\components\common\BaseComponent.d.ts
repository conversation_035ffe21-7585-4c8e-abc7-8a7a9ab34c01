import { C<PERSON><PERSON>omponent, CLIState, CLIConfig } from '../../types';
import { CLIUtils } from '../../utils/CLIUtils';
export declare abstract class BaseComponent implements CLIComponent {
    protected state: CLIState;
    protected config: CLIConfig;
    protected utils: CLIUtils;
    constructor(state: CLIState, config: CLIConfig);
    abstract render(): Promise<void>;
    handleInput(input: string): Promise<void>;
    cleanup(): Promise<void>;
    protected updateState(updates: Partial<CLIState>): void;
    protected showError(message: string): Promise<void>;
    protected showSuccess(message: string): Promise<void>;
    protected confirmAction(message: string): Promise<boolean>;
}
//# sourceMappingURL=BaseComponent.d.ts.map