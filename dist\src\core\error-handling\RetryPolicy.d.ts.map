{"version": 3, "file": "RetryPolicy.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/RetryPolicy.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,MAAM,WAAW,iBAAiB;IAChC,oDAAoD;IACpD,WAAW,EAAE,MAAM,CAAC;IAEpB,iDAAiD;IACjD,SAAS,EAAE,MAAM,CAAC;IAElB,sCAAsC;IACtC,iBAAiB,EAAE,MAAM,CAAC;IAE1B,yDAAyD;IACzD,QAAQ,EAAE,MAAM,CAAC;IAEjB,oFAAoF;IACpF,eAAe,CAAC,EAAE,aAAa,GAAG,QAAQ,GAAG,OAAO,CAAC;IAErD,+DAA+D;IAC/D,YAAY,EAAE,OAAO,CAAC;IAEtB,6DAA6D;IAC7D,UAAU,EAAE,MAAM,GAAG,cAAc,CAAC;IAEpC,sCAAsC;IACtC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC;IAEzD,0DAA0D;IAC1D,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,WAAW,CAAC,CAAC;IAC5B,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,QAAQ,EAAE,YAAY,EAAE,CAAC;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,MAAM,CAAoB;IAClC,OAAO,CAAC,SAAS,CAAa;gBAElB,MAAM,GAAE,OAAO,CAAC,iBAAiB,CAAM;IAanD;;OAEG;IACG,OAAO,CAAC,CAAC,EACb,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,aAAa,CAAC,EAAE,MAAM,GACrB,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAiE1B;;OAEG;IACH,OAAO,CAAC,cAAc;IAYtB;;OAEG;IACH,OAAO,CAAC,WAAW;IAmBnB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAUxB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAqBxB;;OAEG;IACH,OAAO,CAAC,WAAW;IAkBnB;;OAEG;IACH,OAAO,CAAC,KAAK;IAIb;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI;IAIvD;;OAEG;IACH,SAAS,IAAI,iBAAiB;IAI9B;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,MAAM,GAAE,OAAO,CAAC,iBAAiB,CAAM,GAAG,WAAW;IAInE;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO;QACrB,sCAAsC;;QAQtC,yCAAyC;;QAQzC,+CAA+C;;QAQ/C,kDAAkD;;MAOlD;CACH"}