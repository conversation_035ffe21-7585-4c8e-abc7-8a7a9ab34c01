{"extends": "../../config/api-extractor.json", "mainEntryPointFilePath": "<projectFolder>/dist/src/server/index.d.ts", "apiReport": {"reportFileName": "<unscopedPackageName>-server.api.md"}, "dtsRollup": {"enabled": true, "untrimmedFilePath": "<projectFolder>/dist/server/server.d.ts"}, "docModel": {"enabled": true, "apiJsonFilePath": "<projectFolder>/temp/server/<unscopedPackageName>-server.api.json"}}