{"version": 3, "file": "BaseProvider.js", "sourceRoot": "", "sources": ["../../../src/core/providers/BaseProvider.ts"], "names": [], "mappings": ";;;AAmCA,MAAsB,YAAY;IACtB,MAAM,CAAiB;IAGjC,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEM,YAAY;QACjB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC;IAcS,cAAc,CAAC,QAAuB;QAC9C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;IAES,WAAW,CAAC,KAAkB;QACtC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAES,iBAAiB,CACzB,OAAe,EACf,OAAoB,WAAW,EAC/B,QAAc;QAEd,sDAAsD;QACtD,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,EAAE,GAAG,QAAQ,IAAI,EAAE,CAAC;QAEtD,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,CAAC;YAC/B,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;SACxE,CAAC;IACJ,CAAC;IAES,UAAU;QAClB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAES,WAAW,CAAC,KAAU,EAAE,OAAe;QAC/C,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,oBAAoB,OAAO,IAAI,EAAE,KAAK,CAAC,CAAC;QAElE,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YACrC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;YAErE,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,8CAA8C,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9E,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,gCAAgC,IAAI,CAAC,IAAI,kBAAkB,CAAC,CAAC;gBAChF,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,2BAA2B,CAAC,CAAC;gBACpF,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,qCAAqC,CAAC,CAAC;gBACtE;oBACE,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,eAAe,MAAM,MAAM,OAAO,EAAE,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAChE,OAAO,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,IAAI,uCAAuC,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,oBAAoB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC,CAAC;IAC7E,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAES,iBAAiB,CAAC,OAAyB;QACnD,OAAO;YACL,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;YACjD,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;YAC9D,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;YACnE,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK;SACjC,CAAC;IACJ,CAAC;IAEM,YAAY,CAAC,OAAgC;QAClD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;IAC/C,CAAC;IAEM,SAAS;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,WAAW,GAAgB;gBAC/B,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,6CAA6C;QAC7C,4CAA4C;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AApJD,oCAoJC"}