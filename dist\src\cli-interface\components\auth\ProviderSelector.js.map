{"version": 3, "file": "ProviderSelector.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/auth/ProviderSelector.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAgC;AAChC,2DAAwD;AAExD,+CAA0D;AAG1D,MAAa,gBAAiB,SAAQ,6BAAa;IACzC,aAAa,CAAgB;IAErC,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B;QAC1E,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,6DAA6D;IAC/D,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,oBAA6B,KAAK;QAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QAExE,IAAI,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAAmB,CAAC,CAAC;QAE5D,IAAI,iBAAiB,EAAE,CAAC;YACtB,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAC5C,QAAQ,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CACzD,CAAC;QACJ,CAAC;QAED,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAClD,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACtF,KAAK,EAAE,QAAQ,CAAC,IAAI;SACrB,CAAC,CAAC,CAAC;QAEJ,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACjD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,wBAAwB;gBACjC,OAAO;aACR;SACF,CAAC,CAAC;QAEH,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QAExE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACrD,MAAM,QAAQ,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC;YAElF,OAAO;gBACL,IAAI,EAAE,GAAG,QAAQ,CAAC,WAAW,KAAK,MAAM,CAAC,YAAY,IAAI,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE;gBACxF,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACjD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,oBAAoB;gBAC7B,OAAO;aACR;SACF,CAAC,CAAC;QAEH,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,YAAoB;QAC3C,MAAM,QAAQ,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEzE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YAC9C;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,sBAAsB,QAAQ,CAAC,WAAW,GAAG;gBACtD,OAAO,EAAE,QAAQ,CAAC,MAAM;gBACxB,OAAO,EAAE,aAAa,EAAE,YAAY,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;aAC3D;SACF,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,oBAAoB,CAAC,QAAa,EAAE,YAAqB;QAC/D,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACxC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACvC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAExD,OAAO,GAAG,MAAM,IAAI,QAAQ,CAAC,WAAW,KAAK,MAAM,GAAG,CAAC;IACzD,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,YAAoB,EAAE,UAAkB;QACzE,MAAM,WAAW,GAAG,2BAAmB,CAAC,YAAY,CAAC,EAAE,WAAW,IAAI,YAAY,CAAC;QACnF,MAAM,SAAS,GAAG,2BAAmB,CAAC,UAAU,CAAC,EAAE,WAAW,IAAI,UAAU,CAAC;QAE7E,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CACnC,eAAe,WAAW,OAAO,SAAS,GAAG,CAC9C,CAAC;IACJ,CAAC;CACF;AAlHD,4CAkHC"}