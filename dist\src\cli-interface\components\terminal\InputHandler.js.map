{"version": 3, "file": "InputHandler.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/terminal/InputHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAAgC;AAChC,2DAAwD;AAGxD,MAAa,YAAa,SAAQ,6BAAa;IACrC,aAAa,GAAoB,EAAE,CAAC;IAE5C,YAAY,KAAe,EAAE,MAAiB;QAC5C,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,6DAA6D;IAC/D,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAa;QACtC,0DAA0D;QAC1D,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEM,KAAK,CAAC,QAAQ;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;YACtC;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;aACxC;SACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,0DAA0D,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAErH,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC;gBACrC;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;iBAC3D;aACF,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC1B,MAAM;YACR,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,eAAwB,KAAK;QACzE,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAC/D,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,OAAe,EACf,OAA0C,EAC1C,YAAgB;QAEhB,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAEM,eAAe,CAAC,OAAsB;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAEM,kBAAkB,CAAC,OAAe;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3F,CAAC;IAEO,oBAAoB;QAC1B,sCAAsC;QACtC,IAAI,CAAC,eAAe,CAAC;YACnB,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC7C,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YACD,WAAW,EAAE,4BAA4B;SAC1C,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,eAAe,CAAC;YACnB,OAAO,EAAE,kBAAkB;YAC3B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;gBAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACrD,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,WAAW,EAAE,0BAA0B;SACxC,CAAC,CAAC;QAEH,6CAA6C;QAC7C,IAAI,CAAC,eAAe,CAAC;YACnB,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAClD,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,WAAW,EAAE,sBAAsB;SACpC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzE,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,mCAAmC,CAAC;QAC7C,CAAC;QAED,mCAAmC;QACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACzC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,CAAC,iCAAiC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,wDAAa,UAAU,GAAC,CAAC;YACpC,MAAM,IAAI,GAAG,wDAAa,MAAM,GAAC,CAAC;YAElC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uBAAuB,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAEpH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,2DAA2D;YAC3D,+BAA+B;YAC/B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,yEAAyE,CAAC,CAAC;YAC/F,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAC7D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC3C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC;oBACH,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACtC,OAAO,IAAI,CAAC,CAAC,8BAA8B;gBAC7C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;oBAChD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa;QAClB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACrH,CAAC;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;QACrG,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,+BAA+B,CAAC,CAAC;QAC7G,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;QAC3G,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;QACnG,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;CACF;AA3MD,oCA2MC"}