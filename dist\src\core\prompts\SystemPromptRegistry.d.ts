import { SystemPrompt } from '../types';
import { ConfigManager } from '../config/ConfigManager';
export interface SystemPromptRegistryOptions {
    configManager: ConfigManager;
}
export declare class SystemPromptRegistry {
    private configManager;
    private prompts;
    private activePromptId?;
    private defaultPromptId?;
    private categories;
    constructor(options: SystemPromptRegistryOptions);
    private initializeDefaultCategories;
    initialize(): Promise<void>;
    private loadFromConfig;
    private saveToConfig;
    registerPrompt(prompt: Omit<SystemPrompt, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
    updatePrompt(id: string, updates: Partial<Omit<SystemPrompt, 'id' | 'createdAt'>>): Promise<boolean>;
    removePrompt(id: string): Promise<boolean>;
    getPrompt(id: string): SystemPrompt | undefined;
    getAllPrompts(): SystemPrompt[];
    getPromptsByCategory(category: string): SystemPrompt[];
    searchPrompts(query: string): SystemPrompt[];
    getActivePrompt(): SystemPrompt | undefined;
    getDefaultPrompt(): SystemPrompt | undefined;
    setActivePrompt(id: string): Promise<boolean>;
    setDefaultPrompt(id: string): Promise<boolean>;
    getCategories(): string[];
    addCategory(category: string): Promise<void>;
    private generatePromptId;
    getPromptCount(): number;
    hasPrompts(): boolean;
    private initializeDefaultPrompts;
}
//# sourceMappingURL=SystemPromptRegistry.d.ts.map