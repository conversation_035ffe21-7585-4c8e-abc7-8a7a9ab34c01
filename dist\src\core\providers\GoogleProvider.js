"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleProvider = void 0;
const generative_ai_1 = require("@google/generative-ai");
const BaseProvider_1 = require("./BaseProvider");
class GoogleProvider extends BaseProvider_1.BaseProvider {
    name = 'google';
    client;
    model = null;
    constructor(config) {
        super(config);
        this.validateConfig();
        this.client = new generative_ai_1.GoogleGenerativeAI(this.config.apiKey);
    }
    async sendMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const model = this.getModel(requestOptions.model, tools);
            const formattedMessages = this.formatMessages(messages);
            const lastMessage = formattedMessages[formattedMessages.length - 1];
            let result;
            if (formattedMessages.length === 1) {
                // Single message
                result = await model.generateContent(lastMessage.parts);
            }
            else {
                // Multi-turn conversation
                const chat = model.startChat({
                    history: formattedMessages.slice(0, -1),
                    generationConfig: {
                        temperature: requestOptions.temperature,
                        maxOutputTokens: requestOptions.maxTokens,
                    },
                });
                result = await chat.sendMessage(lastMessage.parts);
            }
            return this.processResponse(result, requestOptions.model);
        }
        catch (error) {
            throw this.handleError(error, 'sendMessage');
        }
    }
    async *streamMessage(messages, tools, options) {
        try {
            const requestOptions = this.getRequestOptions(options);
            const model = this.getModel(requestOptions.model, tools);
            const formattedMessages = this.formatMessages(messages);
            const lastMessage = formattedMessages[formattedMessages.length - 1];
            let result;
            if (formattedMessages.length === 1) {
                // Single message
                result = await model.generateContentStream(lastMessage.parts);
            }
            else {
                // Multi-turn conversation
                const chat = model.startChat({
                    history: formattedMessages.slice(0, -1),
                    generationConfig: {
                        temperature: requestOptions.temperature,
                        maxOutputTokens: requestOptions.maxTokens,
                    },
                });
                result = await chat.sendMessageStream(lastMessage.parts);
            }
            let fullContent = '';
            let toolCalls = [];
            for await (const chunk of result.stream) {
                const text = chunk.text();
                if (text) {
                    fullContent += text;
                    yield text;
                }
                // Handle function calls
                const functionCalls = chunk.functionCalls();
                if (functionCalls) {
                    toolCalls.push(...functionCalls);
                }
            }
            const finalResult = await result.response;
            return this.createChatMessage(fullContent, 'assistant', {
                provider: this.name,
                model: requestOptions.model,
                usage: finalResult.usageMetadata ? {
                    promptTokens: finalResult.usageMetadata.promptTokenCount,
                    completionTokens: finalResult.usageMetadata.candidatesTokenCount,
                    totalTokens: finalResult.usageMetadata.totalTokenCount,
                } : undefined,
                toolCalls: toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined,
            });
        }
        catch (error) {
            throw this.handleError(error, 'streamMessage');
        }
    }
    getModel(modelName, tools) {
        const modelConfig = {
            model: modelName,
        };
        if (this.config.systemPrompt) {
            modelConfig.systemInstruction = this.config.systemPrompt;
        }
        if (tools && tools.length > 0) {
            modelConfig.tools = [{
                    functionDeclarations: this.formatTools(tools),
                }];
        }
        return this.client.getGenerativeModel(modelConfig);
    }
    processResponse(result, model) {
        const response = result.response;
        const text = response.text() || '';
        let toolCalls = [];
        const functionCalls = response.functionCalls();
        if (functionCalls) {
            toolCalls = this.formatToolCalls(functionCalls);
        }
        return this.createChatMessage(text, 'assistant', {
            provider: this.name,
            model,
            usage: response.usageMetadata ? {
                promptTokens: response.usageMetadata.promptTokenCount,
                completionTokens: response.usageMetadata.candidatesTokenCount,
                totalTokens: response.usageMetadata.totalTokenCount,
            } : undefined,
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
            finishReason: response.candidates?.[0]?.finishReason,
        });
    }
    formatToolCalls(functionCalls) {
        return functionCalls.map(call => ({
            id: this.generateId(),
            name: call.name,
            parameters: call.args,
        }));
    }
    formatMessages(messages) {
        const formatted = [];
        for (const msg of messages) {
            if (msg.role === 'system') {
                // System messages are handled in model configuration
                continue;
            }
            const role = msg.role === 'assistant' ? 'model' : 'user';
            const parts = [{ text: msg.content }];
            // Handle tool calls in assistant messages
            if (msg.role === 'assistant' && msg.toolCalls) {
                for (const call of msg.toolCalls) {
                    parts.push({
                        functionCall: {
                            name: call.name,
                            args: call.parameters,
                        },
                    });
                }
            }
            // Handle tool responses
            if (msg.role === 'tool') {
                parts[0] = {
                    functionResponse: {
                        name: msg.metadata?.toolCallId || 'unknown_tool',
                        response: {
                            content: msg.content,
                        },
                    },
                };
            }
            formatted.push({ role, parts });
        }
        return formatted;
    }
    formatTools(tools) {
        if (!tools || tools.length === 0) {
            return [];
        }
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: {
                type: 'object',
                properties: tool.parameters.properties || {},
                required: tool.parameters.required || [],
            },
        }));
    }
    async testConnection() {
        try {
            const model = this.client.getGenerativeModel({ model: this.config.defaultModel });
            const result = await model.generateContent('Hello');
            return !!result.response.text();
        }
        catch (error) {
            console.error('Google connection test failed:', error);
            return false;
        }
    }
    async getAvailableModels() {
        try {
            // Google AI SDK doesn't expose listModels in the current version
            // Return known available models
            return ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash'];
        }
        catch (error) {
            console.error('Failed to fetch Google models:', error);
            return ['gemini-pro', 'gemini-pro-vision']; // Fallback to known models
        }
    }
    validateConfig() {
        super.validateConfig();
        // Google API keys are typically 39 characters long
        if (this.config.apiKey.length < 30) {
            throw new Error('Google API key appears to be invalid (too short)');
        }
    }
}
exports.GoogleProvider = GoogleProvider;
//# sourceMappingURL=GoogleProvider.js.map