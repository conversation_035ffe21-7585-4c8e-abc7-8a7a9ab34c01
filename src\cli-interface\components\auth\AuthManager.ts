import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { ProviderSelector } from './ProviderSelector';
import { ProviderConfigurator } from './ProviderConfigurator';
import { AuthMenu } from './AuthMenu';
import { CLIState, CLIConfig } from '../../types';
import { ErrorHandlingMiddleware, ProgressCallback } from '../../../core/error-handling/ErrorHandlingMiddleware';
import { createErrorHandlingMiddleware } from '../../../core/error-handling';
import { CLIProgressIndicator } from '../common/ProgressIndicator';

export class AuthManager extends BaseComponent {
  private configManager: ConfigManager;
  private providerSelector: ProviderSelector;
  private providerConfigurator: ProviderConfigurator;
  private authMenu: AuthMenu;
  private errorHandler: ErrorHandlingMiddleware;
  private progressIndicator: CLIProgressIndicator;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
    this.providerSelector = new ProviderSelector(state, config, configManager);
    this.providerConfigurator = new ProviderConfigurator(state, config, configManager);
    this.authMenu = new AuthMenu(state, config, configManager);

    // Initialize error handling for authentication operations
    this.errorHandler = createErrorHandlingMiddleware({
      showRetryProgress: true,
      allowRetryCancel: true,
      enableErrorLogging: true,
      enableMetrics: true
    });

    this.progressIndicator = new CLIProgressIndicator(config.theme);
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🤖 AI CLI Terminal', 'Configure your AI providers to get started');

    const configuredProviders = this.configManager.getConfiguredProviders();

    if (configuredProviders.length === 0) {
      await this.setupFirstProvider();
    } else {
      await this.showMainMenu();
    }
  }

  private async setupFirstProvider(): Promise<void> {
    this.utils.showWarning('No AI providers configured. Let\'s set one up!');
    console.log();

    const selectedProvider = await this.providerSelector.selectProvider();
    if (selectedProvider) {
      await this.providerConfigurator.configureProvider(selectedProvider);
      
      // Mark as authenticated if configuration was successful
      if (this.configManager.isProviderConfigured(selectedProvider)) {
        this.updateState({ 
          isAuthenticated: true,
          currentProvider: selectedProvider,
          currentModel: this.configManager.getProviderConfig(selectedProvider).defaultModel
        });
      }
    }
  }

  private async showMainMenu(): Promise<void> {
    await this.authMenu.show();
    
    // Check if user chose to start terminal
    if (this.state.currentView === 'terminal') {
      const config = this.configManager.getConfig();
      this.updateState({
        isAuthenticated: true,
        currentProvider: config.defaultProvider,
        currentModel: this.configManager.getProviderConfig().defaultModel
      });
    }
  }

  public async handleProviderConfiguration(providerName: string): Promise<void> {
    await this.providerConfigurator.configureProvider(providerName);
  }

  public async handleProviderSelection(): Promise<string | null> {
    return await this.providerSelector.selectProvider();
  }

  public isAuthenticated(): boolean {
    return this.configManager.getConfiguredProviders().length > 0;
  }

  public getCurrentProvider(): string | null {
    const config = this.configManager.getConfig();
    return config.defaultProvider;
  }

  public async switchProvider(providerName: string): Promise<void> {
    if (!this.configManager.isProviderConfigured(providerName)) {
      throw new Error(`Provider ${providerName} is not configured`);
    }

    await this.configManager.setDefaultProvider(providerName);
    const providerConfig = this.configManager.getProviderConfig(providerName);

    this.updateState({
      currentProvider: providerName,
      currentModel: providerConfig.defaultModel
    });

    this.utils.showSuccess(`Switched to ${providerName}`);
  }

  /**
   * Test provider connection with comprehensive error handling
   */
  public async testProviderConnection(providerType: string): Promise<boolean> {
    const progressCallback: ProgressCallback = {
      onRetryAttempt: (attempt: number, maxAttempts: number, delay: number, error: Error) => {
        this.progressIndicator.update(
          `Connection test failed (attempt ${attempt}/${maxAttempts}). Retrying in ${Math.round(delay / 1000)}s...`
        );
      },
      onCircuitBreakerOpen: (serviceName: string) => {
        this.progressIndicator.fail(`${serviceName} service is temporarily unavailable.`);
      },
      onCircuitBreakerClosed: (serviceName: string) => {
        this.progressIndicator.succeed(`${serviceName} service connection restored.`);
      }
    };

    try {
      this.progressIndicator.start(`Testing ${providerType} connection...`);

      const result = await this.errorHandler.executeWithErrorHandling(
        async () => {
          // Get the provider instance and test connection
          const provider = this.configManager.getProvider(providerType as any);
          if (!provider) {
            throw new Error(`Provider ${providerType} not found or not configured`);
          }
          return await provider.testConnection();
        },
        {
          operationName: 'provider_connection_test',
          operationType: 'providerCall',
          serviceName: providerType
        },
        progressCallback
      );

      if (result.success) {
        this.progressIndicator.succeed(`✅ ${providerType} connection successful!`);
        return true;
      } else {
        this.progressIndicator.fail(`❌ ${providerType} connection failed: ${result.userMessage}`);

        // Show helpful error messages based on error type
        if (result.userMessage.includes('authentication') || result.userMessage.includes('API key')) {
          this.utils.showWarning('💡 Tip: Check your API key configuration in the provider settings.');
        } else if (result.userMessage.includes('network') || result.userMessage.includes('timeout')) {
          this.utils.showWarning('💡 Tip: Check your internet connection and try again.');
        } else if (result.userMessage.includes('rate limit')) {
          this.utils.showWarning('💡 Tip: You may have exceeded the rate limit. Please wait before trying again.');
        }

        return false;
      }
    } catch (error) {
      this.progressIndicator.fail(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }

  /**
   * Configure provider with error handling and validation
   */
  public async configureProviderWithValidation(providerType: string, config: any): Promise<boolean> {
    try {
      this.progressIndicator.start(`Configuring ${providerType}...`);

      // Save configuration
      await this.configManager.setProviderConfig(providerType as any, config);

      // Test the connection to validate the configuration
      const connectionSuccess = await this.testProviderConnection(providerType);

      if (connectionSuccess) {
        this.progressIndicator.succeed(`✅ ${providerType} configured and validated successfully!`);
        return true;
      } else {
        this.progressIndicator.fail(`❌ ${providerType} configuration failed validation.`);
        return false;
      }
    } catch (error) {
      this.progressIndicator.fail(`❌ Configuration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    }
  }
}
