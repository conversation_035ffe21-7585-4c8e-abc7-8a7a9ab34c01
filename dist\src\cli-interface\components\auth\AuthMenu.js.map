{"version": 3, "file": "AuthMenu.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/auth/AuthMenu.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AACxD,yCAAsC;AAEtC,yDAAsD;AACtD,iEAA8D;AAC9D,+CAA0D;AAG1D,MAAa,QAAS,SAAQ,6BAAa;IACjC,aAAa,CAAgB;IAC7B,gBAAgB,CAAmB;IACnC,oBAAoB,CAAuB;IAEnD,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B;QAC1E,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC3E,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACrF,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,uBAAuB,CAAC,CAAC;QAC5F,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAEO,qBAAqB;QAC3B,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE9C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAE/D,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBACzC,MAAM,QAAQ,GAAG,2BAAmB,CAAC,YAAY,CAAC,CAAC;gBACnD,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAC1E,MAAM,SAAS,GAAG,YAAY,KAAK,MAAM,CAAC,eAAe,CAAC;gBAE1D,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACrC,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBAEpF,OAAO,CAAC,GAAG,CACT,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC,WAAW,KAAK,cAAc,CAAC,YAAY,GAAG,EAAE,WAAW,CAAC;oBACxG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAC9E,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QACxE,MAAM,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,2BAAmB,CAAC,CAAC,MAAM,CACnE,CAAC,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CACtC,CAAC;QAEF,MAAM,OAAO,GAAiB;YAC5B;gBACE,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,mBAAmB;gBAC1B,WAAW,EAAE,uBAAuB;gBACpC,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACrC,MAAM,IAAI,CAAC,SAAS,CAAC,uDAAuD,CAAC,CAAC;wBAC9E,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBACpB,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC;oBAChD,CAAC;gBACH,CAAC;gBACD,QAAQ,EAAE,mBAAmB,CAAC,MAAM,KAAK,CAAC;aAC3C;SACF,CAAC;QAEF,kCAAkC;QAClC,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC;gBACX,GAAG,EAAE,KAAK;gBACV,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,6BAA6B;gBAC1C,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAClE,IAAI,QAAQ,EAAE,CAAC;wBACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAC9D,CAAC;oBACD,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CACV;gBACE,GAAG,EAAE,WAAW;gBAChB,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,mCAAmC;gBAChD,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;oBACpE,IAAI,QAAQ,EAAE,CAAC;wBACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAC9D,CAAC;oBACD,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;aACF,EACD;gBACE,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,sCAAsC;gBACnD,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;aACF,EACD;gBACE,GAAG,EAAE,MAAM;gBACX,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,+BAA+B;gBAC5C,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;aACF,EACD;gBACE,GAAG,EAAE,QAAQ;gBACb,KAAK,EAAE,qBAAqB;gBAC5B,WAAW,EAAE,8BAA8B;gBAC3C,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAClC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;aACF,CACF,CAAC;QACJ,CAAC;QAED,sBAAsB;QACtB,OAAO,CAAC,IAAI,CACV;YACE,GAAG,EAAE,aAAa;YAClB,KAAK,EAAE,gBAAgB;YACvB,WAAW,EAAE,mCAAmC;YAChD,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;SACF,EACD;YACE,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,kBAAkB;YACzB,WAAW,EAAE,8BAA8B;YAC3C,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;SACF,EACD;YACE,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,kBAAkB;YACzB,WAAW,EAAE,gCAAgC;YAC7C,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;SACF,EACD;YACE,GAAG,EAAE,OAAO;YACZ,KAAK,EAAE,wBAAwB;YAC/B,WAAW,EAAE,gCAAgC;YAC7C,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;SACF,EACD;YACE,GAAG,EAAE,MAAM;YACX,KAAK,EAAE,QAAQ;YACf,WAAW,EAAE,sBAAsB;YACnC,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;SACF,CACF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;QACpE,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACtD,MAAM,IAAI,CAAC,WAAW,CAAC,2BAA2B,2BAAmB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;QACpE,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;QACpE,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CACxC,mCAAmC,2BAAmB,CAAC,QAAQ,CAAC,CAAC,WAAW,GAAG,CAChF,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAElC,8CAA8C;YAC9C,IAAI,MAAM,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;gBACxC,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACzD,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClC,MAAM,CAAC,eAAe,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,2BAAmB,CAAC,QAAQ,CAAC,CAAC,WAAW,uBAAuB,CAAC,CAAC;QAC9F,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,4CAA4C;QAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,0CAA0C,CAAC,CAAC;QAChE,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,gCAAgC;QAChC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;QACpD,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,gCAAgC;QAChC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;QACpD,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CACxC,0EAA0E,CAC3E,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,WAAW,CAAC,kCAAkC,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;AA7PD,4BA6PC"}