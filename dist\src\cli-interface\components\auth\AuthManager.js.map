{"version": 3, "file": "AuthManager.js", "sourceRoot": "", "sources": ["../../../../../src/cli-interface/components/auth/AuthManager.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AAExD,yDAAsD;AACtD,iEAA8D;AAC9D,yCAAsC;AAGtC,iEAA6E;AAC7E,mEAAmE;AAEnE,MAAa,WAAY,SAAQ,6BAAa;IACpC,aAAa,CAAgB;IAC7B,gBAAgB,CAAmB;IACnC,oBAAoB,CAAuB;IAC3C,QAAQ,CAAW;IACnB,YAAY,CAA0B;IACtC,iBAAiB,CAAuB;IAEhD,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B;QAC1E,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC3E,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QACnF,IAAI,CAAC,QAAQ,GAAG,IAAI,mBAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAE3D,0DAA0D;QAC1D,IAAI,CAAC,YAAY,GAAG,IAAA,8CAA6B,EAAC;YAChD,iBAAiB,EAAE,IAAI;YACvB,gBAAgB,EAAE,IAAI;YACtB,kBAAkB,EAAE,IAAI;YACxB,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,wCAAoB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,4CAA4C,CAAC,CAAC;QAE1F,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QAExE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gDAAgD,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;QACtE,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAEpE,wDAAwD;YACxD,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC9D,IAAI,CAAC,WAAW,CAAC;oBACf,eAAe,EAAE,IAAI;oBACrB,eAAe,EAAE,gBAAgB;oBACjC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,YAAY;iBAClF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAE3B,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC;gBACf,eAAe,EAAE,IAAI;gBACrB,eAAe,EAAE,MAAM,CAAC,eAAe;gBACvC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,YAAY;aAClE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,2BAA2B,CAAC,YAAoB;QAC3D,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAClE,CAAC;IAEM,KAAK,CAAC,uBAAuB;QAClC,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;IACtD,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;IAChE,CAAC;IAEM,kBAAkB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,OAAO,MAAM,CAAC,eAAe,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,YAAoB;QAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,oBAAoB,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAE1E,IAAI,CAAC,WAAW,CAAC;YACf,eAAe,EAAE,YAAY;YAC7B,YAAY,EAAE,cAAc,CAAC,YAAY;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,YAAY,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CAAC,YAAoB;QACtD,MAAM,gBAAgB,GAAqB;YACzC,cAAc,EAAE,CAAC,OAAe,EAAE,WAAmB,EAAE,KAAa,EAAE,KAAY,EAAE,EAAE;gBACpF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAC3B,mCAAmC,OAAO,IAAI,WAAW,kBAAkB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAC1G,CAAC;YACJ,CAAC;YACD,oBAAoB,EAAE,CAAC,WAAmB,EAAE,EAAE;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,sCAAsC,CAAC,CAAC;YACpF,CAAC;YACD,sBAAsB,EAAE,CAAC,WAAmB,EAAE,EAAE;gBAC9C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,WAAW,+BAA+B,CAAC,CAAC;YAChF,CAAC;SACF,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,YAAY,gBAAgB,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAC7D,KAAK,IAAI,EAAE;gBACT,gDAAgD;gBAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAmB,CAAC,CAAC;gBACrE,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,8BAA8B,CAAC,CAAC;gBAC1E,CAAC;gBACD,OAAO,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;YACzC,CAAC,EACD;gBACE,aAAa,EAAE,0BAA0B;gBACzC,aAAa,EAAE,cAAc;gBAC7B,WAAW,EAAE,YAAY;aAC1B,EACD,gBAAgB,CACjB,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,YAAY,yBAAyB,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,YAAY,uBAAuB,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;gBAE1F,kDAAkD;gBAClD,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC5F,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,oEAAoE,CAAC,CAAC;gBAC/F,CAAC;qBAAM,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC5F,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,uDAAuD,CAAC,CAAC;gBAClF,CAAC;qBAAM,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACrD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gFAAgF,CAAC,CAAC;gBAC3G,CAAC;gBAED,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACrH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,+BAA+B,CAAC,YAAoB,EAAE,MAAW;QAC5E,IAAI,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,eAAe,YAAY,KAAK,CAAC,CAAC;YAE/D,qBAAqB;YACrB,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,oDAAoD;YACpD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAE1E,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,YAAY,yCAAyC,CAAC,CAAC;gBAC3F,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,YAAY,mCAAmC,CAAC,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YACnH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA/LD,kCA+LC"}