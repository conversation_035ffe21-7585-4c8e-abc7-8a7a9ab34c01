import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class AuthManager extends BaseComponent {
    private configManager;
    private providerSelector;
    private providerConfigurator;
    private authMenu;
    private errorHandler;
    private progressIndicator;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private setupFirstProvider;
    private showMainMenu;
    handleProviderConfiguration(providerName: string): Promise<void>;
    handleProviderSelection(): Promise<string | null>;
    isAuthenticated(): boolean;
    getCurrentProvider(): string | null;
    switchProvider(providerName: string): Promise<void>;
    /**
     * Test provider connection with comprehensive error handling
     */
    testProviderConnection(providerType: string): Promise<boolean>;
    /**
     * Configure provider with error handling and validation
     */
    configureProviderWithValidation(providerType: string, config: any): Promise<boolean>;
}
//# sourceMappingURL=AuthManager.d.ts.map