import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class ProviderSelector extends BaseComponent {
    private configManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    selectProvider(excludeConfigured?: boolean): Promise<string | null>;
    selectFromConfigured(): Promise<string | null>;
    selectModel(providerName: string): Promise<string | null>;
    private formatProviderChoice;
    confirmProviderSwitch(fromProvider: string, toProvider: string): Promise<boolean>;
}
//# sourceMappingURL=ProviderSelector.d.ts.map