/**
 * Retry Policy System with Exponential Backoff and Jitter
 *
 * Provides configurable retry mechanisms with:
 * - Exponential backoff with customizable base delay and multiplier
 * - Jitter implementation (full jitter and decorrelated jitter)
 * - Maximum retry attempts and delay caps
 * - Conditional retry logic based on error types
 */
export interface RetryPolicyConfig {
    /** Maximum number of retry attempts (default: 3) */
    maxAttempts: number;
    /** Base delay in milliseconds (default: 1000) */
    baseDelay: number;
    /** Backoff multiplier (default: 2) */
    backoffMultiplier: number;
    /** Maximum delay cap in milliseconds (default: 60000) */
    maxDelay: number;
    /** Backoff strategy: 'exponential' | 'linear' | 'fixed' (default: 'exponential') */
    backoffStrategy?: 'exponential' | 'linear' | 'fixed';
    /** Enable jitter to prevent thundering herd (default: true) */
    enableJitter: boolean;
    /** Jitter type: 'full' | 'decorrelated' (default: 'full') */
    jitterType: 'full' | 'decorrelated';
    /** Custom retry condition function */
    shouldRetry?: (error: Error, attempt: number) => boolean;
    /** Timeout for each individual attempt in milliseconds */
    attemptTimeout?: number;
}
export interface RetryAttempt {
    attempt: number;
    delay: number;
    error?: Error;
    timestamp: Date;
    duration?: number;
}
export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: Error;
    attempts: RetryAttempt[];
    totalDuration: number;
    finalAttempt: number;
}
export declare class RetryPolicy {
    private config;
    private lastDelay;
    constructor(config?: Partial<RetryPolicyConfig>);
    /**
     * Execute a function with retry logic
     */
    execute<T>(operation: () => Promise<T>, operationName?: string): Promise<RetryResult<T>>;
    /**
     * Calculate delay with exponential backoff and jitter
     */
    private calculateDelay;
    /**
     * Apply jitter to delay
     */
    private applyJitter;
    /**
     * Determine if an error should trigger a retry
     */
    private shouldRetryError;
    /**
     * Basic transient error detection (will be enhanced by ErrorClassifier)
     */
    private isTransientError;
    /**
     * Add timeout to a promise
     */
    private withTimeout;
    /**
     * Simple delay utility
     */
    private delay;
    /**
     * Update retry policy configuration
     */
    updateConfig(updates: Partial<RetryPolicyConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): RetryPolicyConfig;
    /**
     * Create a new RetryPolicy with different configuration
     */
    static create(config?: Partial<RetryPolicyConfig>): RetryPolicy;
    /**
     * Predefined retry policies for common scenarios
     */
    static readonly presets: {
        /** Fast retry for quick operations */
        fast: RetryPolicy;
        /** Standard retry for most operations */
        standard: RetryPolicy;
        /** Aggressive retry for critical operations */
        aggressive: RetryPolicy;
        /** Conservative retry for expensive operations */
        conservative: RetryPolicy;
    };
}
//# sourceMappingURL=RetryPolicy.d.ts.map