import { RetryPolicy, RetryPolicyConfig } from '../../../src/core/error-handling/RetryPolicy';

describe('RetryPolicy', () => {
  describe('constructor', () => {
    it('should create RetryPolicy with default configuration', () => {
      const policy = new RetryPolicy();
      expect(policy).toBeInstanceOf(RetryPolicy);
      expect(policy.getConfig().maxAttempts).toBe(3);
      expect(policy.getConfig().baseDelay).toBe(1000);
      expect(policy.getConfig().backoffMultiplier).toBe(2);
    });

    it('should create RetryPolicy with custom configuration', () => {
      const config: Partial<RetryPolicyConfig> = {
        maxAttempts: 5,
        baseDelay: 500,
        enableJitter: false
      };
      
      const policy = new RetryPolicy(config);
      expect(policy).toBeInstanceOf(RetryPolicy);
      expect(policy.getConfig().maxAttempts).toBe(5);
      expect(policy.getConfig().baseDelay).toBe(500);
      expect(policy.getConfig().enableJitter).toBe(false);
    });
  });

  describe('execute method - success cases', () => {
    it('should execute operation successfully on first attempt', async () => {
      const policy = new RetryPolicy({ enableJitter: false });
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.finalAttempt).toBe(1);
      expect(result.attempts).toHaveLength(1);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on transient failure and eventually succeed', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 1, // Very fast for testing
        enableJitter: false
      });
      
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('Network connection failed'))
        .mockRejectedValueOnce(new Error('Timeout occurred'))
        .mockResolvedValue('success');
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.finalAttempt).toBe(3);
      expect(result.attempts).toHaveLength(3);
      expect(operation).toHaveBeenCalledTimes(3);
    });
  });

  describe('execute method - failure cases', () => {
    it('should fail after max attempts with transient errors', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 2,
        baseDelay: 1,
        enableJitter: false
      });
      
      const operation = jest.fn().mockRejectedValue(new Error('Network connection failed'));
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Network connection failed');
      expect(result.finalAttempt).toBe(2);
      expect(result.attempts).toHaveLength(2);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry non-transient errors', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 1,
        enableJitter: false
      });
      
      const operation = jest.fn().mockRejectedValue(new Error('Invalid input'));
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Invalid input');
      expect(result.finalAttempt).toBe(1);
      expect(result.attempts).toHaveLength(1);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should respect custom shouldRetry function', async () => {
      const customShouldRetry = jest.fn().mockReturnValue(false);
      const policy = new RetryPolicy({
        maxAttempts: 3,
        shouldRetry: customShouldRetry
      });
      
      const operation = jest.fn().mockRejectedValue(new Error('Network connection failed'));
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(false);
      expect(result.finalAttempt).toBe(1);
      expect(customShouldRetry).toHaveBeenCalledWith(expect.any(Error), 1);
      expect(operation).toHaveBeenCalledTimes(1);
    });
  });

  describe('configuration management', () => {
    it('should update configuration', () => {
      const policy = new RetryPolicy({ maxAttempts: 3 });
      
      policy.updateConfig({ maxAttempts: 5, baseDelay: 2000 });
      
      const config = policy.getConfig();
      expect(config.maxAttempts).toBe(5);
      expect(config.baseDelay).toBe(2000);
      expect(config.backoffMultiplier).toBe(2); // Unchanged
    });

    it('should return copy of configuration', () => {
      const policy = new RetryPolicy({ maxAttempts: 3 });
      
      const config1 = policy.getConfig();
      const config2 = policy.getConfig();
      
      expect(config1).toEqual(config2);
      expect(config1).not.toBe(config2); // Different objects
    });
  });

  describe('static methods', () => {
    it('should create policy with static create method', () => {
      const policy = RetryPolicy.create({ maxAttempts: 5 });
      
      expect(policy).toBeInstanceOf(RetryPolicy);
      expect(policy.getConfig().maxAttempts).toBe(5);
    });

    it('should provide predefined presets', () => {
      expect(RetryPolicy.presets.fast).toBeInstanceOf(RetryPolicy);
      expect(RetryPolicy.presets.standard).toBeInstanceOf(RetryPolicy);
      expect(RetryPolicy.presets.aggressive).toBeInstanceOf(RetryPolicy);
      expect(RetryPolicy.presets.conservative).toBeInstanceOf(RetryPolicy);
      
      expect(RetryPolicy.presets.fast.getConfig().baseDelay).toBe(500);
      expect(RetryPolicy.presets.aggressive.getConfig().maxAttempts).toBe(5);
    });
  });

  describe('error handling', () => {
    it('should handle non-Error objects', async () => {
      const policy = new RetryPolicy({ maxAttempts: 1 });
      const operation = jest.fn().mockRejectedValue('string error');
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
      expect(result.error?.message).toBe('string error');
    });

    it('should track attempt information', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 2,
        baseDelay: 1,
        enableJitter: false
      });
      
      const operation = jest.fn().mockRejectedValue(new Error('Network timeout'));
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.attempts).toHaveLength(2);
      expect(result.attempts[0].attempt).toBe(1);
      expect(result.attempts[0].error?.message).toBe('Network timeout');
      expect(result.attempts[0].timestamp).toBeInstanceOf(Date);
      expect(typeof result.attempts[0].duration).toBe('number');
      
      expect(result.attempts[1].attempt).toBe(2);
      expect(result.attempts[1].delay).toBe(0); // Last attempt has no delay
    });

    it('should handle timeout for individual attempts', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 1,
        attemptTimeout: 50 // Very short timeout
      });
      
      const operation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100)) // Takes longer than timeout
      );
      
      const result = await policy.execute(operation, 'test-operation');
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timed out');
    });
  });

  describe('transient error detection', () => {
    it('should retry network-related errors', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 2,
        baseDelay: 1,
        enableJitter: false
      });
      
      const networkErrors = [
        'Network connection failed',
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'Socket hang up',
        'Timeout occurred'
      ];
      
      for (const errorMessage of networkErrors) {
        const operation = jest.fn().mockRejectedValue(new Error(errorMessage));
        const result = await policy.execute(operation, 'test-operation');
        
        expect(result.attempts.length).toBeGreaterThan(1);
        expect(operation).toHaveBeenCalledTimes(2);
        
        // Reset for next iteration
        operation.mockClear();
      }
    });

    it('should not retry non-transient errors', async () => {
      const policy = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 1,
        enableJitter: false
      });
      
      const nonTransientErrors = [
        'Invalid input',
        'Authentication failed',
        'Permission denied',
        'Not found'
      ];
      
      for (const errorMessage of nonTransientErrors) {
        const operation = jest.fn().mockRejectedValue(new Error(errorMessage));
        const result = await policy.execute(operation, 'test-operation');
        
        expect(result.attempts).toHaveLength(1);
        expect(operation).toHaveBeenCalledTimes(1);
        
        // Reset for next iteration
        operation.mockClear();
      }
    });
  });
});
