"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadFileTool = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const BaseTool_1 = require("../base/BaseTool");
class ReadFileTool extends BaseTool_1.BaseTool {
    name = 'read_file';
    description = 'Reads content from files with support for text, binary, and large files';
    requiresConfirmation = false;
    parameters = {
        type: 'object',
        properties: {
            file_path: {
                type: 'string',
                description: 'Path to the file to read (absolute or relative)',
            },
            encoding: {
                type: 'string',
                enum: ['utf8', 'ascii', 'base64', 'hex', 'binary'],
                description: 'File encoding to use for reading',
                default: 'utf8',
            },
            start_line: {
                type: 'number',
                description: 'Starting line number for partial reading (1-based)',
                minimum: 1,
            },
            end_line: {
                type: 'number',
                description: 'Ending line number for partial reading (1-based)',
                minimum: 1,
            },
            max_size: {
                type: 'number',
                description: 'Maximum file size to read in bytes',
                default: 1048576, // 1MB
            },
            preview_only: {
                type: 'boolean',
                description: 'Only read first few lines for preview',
                default: false,
            },
            preview_lines: {
                type: 'number',
                description: 'Number of lines to preview',
                default: 50,
                minimum: 1,
                maximum: 1000,
            },
        },
        required: ['file_path'],
    };
    constructor() {
        super({
            category: 'filesystem',
            tags: ['file', 'read', 'content'],
            version: '1.0.0',
            dangerous: false,
            requiresConfirmation: false,
        });
    }
    async validate(params) {
        const errors = [];
        const warnings = [];
        // Validate required parameters
        const requiredValidation = this.validateRequiredParams(params, ['file_path']);
        if (!requiredValidation.valid) {
            return requiredValidation;
        }
        // Validate file path
        const pathErrors = this.validateStringParam(params.file_path, 'file_path');
        errors.push(...pathErrors);
        // Validate optional parameters
        if (params.encoding !== undefined) {
            const validEncodings = ['utf8', 'ascii', 'base64', 'hex', 'binary'];
            if (!validEncodings.includes(params.encoding)) {
                errors.push(`Parameter 'encoding' must be one of: ${validEncodings.join(', ')}`);
            }
        }
        if (params.start_line !== undefined) {
            errors.push(...this.validateNumberParam(params.start_line, 'start_line', {
                min: 1,
                integer: true,
            }));
        }
        if (params.end_line !== undefined) {
            errors.push(...this.validateNumberParam(params.end_line, 'end_line', {
                min: 1,
                integer: true,
            }));
            if (params.start_line !== undefined && params.end_line < params.start_line) {
                errors.push('Parameter end_line must be greater than or equal to start_line');
            }
        }
        if (params.max_size !== undefined) {
            errors.push(...this.validateNumberParam(params.max_size, 'max_size', {
                min: 1,
                max: 100 * 1024 * 1024, // 100MB max
                integer: true,
            }));
        }
        if (params.preview_only !== undefined) {
            errors.push(...this.validateBooleanParam(params.preview_only, 'preview_only'));
        }
        if (params.preview_lines !== undefined) {
            errors.push(...this.validateNumberParam(params.preview_lines, 'preview_lines', {
                min: 1,
                max: 1000,
                integer: true,
            }));
        }
        // Check if file exists and is accessible
        try {
            const resolvedPath = path.resolve(params.file_path);
            const stats = await fs.stat(resolvedPath);
            if (!stats.isFile()) {
                errors.push(`Path '${params.file_path}' is not a file`);
            }
            else {
                // Check file size
                const maxSize = params.max_size || 1048576; // 1MB default
                if (stats.size > maxSize && !params.preview_only) {
                    warnings.push(`File size (${this.formatFileSize(stats.size)}) exceeds max_size (${this.formatFileSize(maxSize)}). Consider using preview_only or increasing max_size.`);
                }
                // Warn about binary files
                if (params.encoding === 'utf8' && await this.isBinaryFile(resolvedPath)) {
                    warnings.push('File appears to be binary. Consider using a different encoding.');
                }
            }
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                errors.push(`File '${params.file_path}' does not exist`);
            }
            else if (error.code === 'EACCES') {
                errors.push(`Permission denied reading '${params.file_path}'`);
            }
            else {
                errors.push(`Cannot access file '${params.file_path}': ${error}`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    async execute(params) {
        try {
            const { file_path, encoding = 'utf8', start_line, end_line, max_size = 1048576, preview_only = false, preview_lines = 50, } = params;
            const resolvedPath = path.resolve(file_path);
            const stats = await fs.stat(resolvedPath);
            // Check file size
            if (stats.size > max_size && !preview_only) {
                return this.createErrorResult(`File size (${this.formatFileSize(stats.size)}) exceeds maximum allowed size (${this.formatFileSize(max_size)})`);
            }
            let content;
            let metadata = {
                path: resolvedPath,
                size: stats.size,
                modified: stats.mtime,
                encoding,
            };
            if (preview_only) {
                content = await this.readFilePreview(resolvedPath, preview_lines, encoding);
                metadata.preview = true;
                metadata.previewLines = preview_lines;
            }
            else if (start_line !== undefined || end_line !== undefined) {
                content = await this.readFileLines(resolvedPath, start_line, end_line, encoding);
                metadata.partial = true;
                metadata.startLine = start_line;
                metadata.endLine = end_line;
            }
            else {
                content = await fs.readFile(resolvedPath, encoding);
            }
            const displayContent = this.createDisplayContent(content, file_path, metadata);
            return this.createSuccessResult(content, displayContent, metadata);
        }
        catch (error) {
            return this.createErrorResult(`Failed to read file: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async readFilePreview(filePath, previewLines, encoding) {
        const stream = fs.createReadStream(filePath, { encoding: encoding });
        const lines = [];
        let buffer = '';
        return new Promise((resolve, reject) => {
            stream.on('data', (chunk) => {
                buffer += chunk.toString();
                const newLines = buffer.split('\n');
                buffer = newLines.pop() || '';
                lines.push(...newLines);
                if (lines.length >= previewLines) {
                    stream.destroy();
                    resolve(lines.slice(0, previewLines).join('\n'));
                }
            });
            stream.on('end', () => {
                if (buffer)
                    lines.push(buffer);
                resolve(lines.slice(0, previewLines).join('\n'));
            });
            stream.on('error', reject);
        });
    }
    async readFileLines(filePath, startLine, endLine, encoding = 'utf8') {
        const content = await fs.readFile(filePath, encoding);
        const lines = content.split('\n');
        const start = startLine ? startLine - 1 : 0;
        const end = endLine ? endLine : lines.length;
        return lines.slice(start, end).join('\n');
    }
    async isBinaryFile(filePath) {
        try {
            const buffer = await fs.readFile(filePath, { encoding: null });
            const sample = buffer.slice(0, 1024); // Check first 1KB
            // Check for null bytes (common in binary files)
            for (let i = 0; i < sample.length; i++) {
                if (sample[i] === 0) {
                    return true;
                }
            }
            // Check for high percentage of non-printable characters
            let nonPrintable = 0;
            for (let i = 0; i < sample.length; i++) {
                const byte = sample[i];
                if (byte < 32 && byte !== 9 && byte !== 10 && byte !== 13) {
                    nonPrintable++;
                }
            }
            return (nonPrintable / sample.length) > 0.3; // More than 30% non-printable
        }
        catch {
            return false;
        }
    }
    createDisplayContent(content, filePath, metadata) {
        const lines = content.split('\n');
        const lineCount = lines.length;
        const size = this.formatFileSize(metadata.size);
        let header = `File: ${filePath} (${size}, ${lineCount} lines)`;
        if (metadata.preview) {
            header += ` [Preview - first ${metadata.previewLines} lines]`;
        }
        else if (metadata.partial) {
            header += ` [Lines ${metadata.startLine || 1}-${metadata.endLine || lineCount}]`;
        }
        const separator = '─'.repeat(Math.min(80, header.length));
        return `${header}\n${separator}\n${this.truncateContent(content, 10000)}`;
    }
}
exports.ReadFileTool = ReadFileTool;
//# sourceMappingURL=ReadFileTool.js.map