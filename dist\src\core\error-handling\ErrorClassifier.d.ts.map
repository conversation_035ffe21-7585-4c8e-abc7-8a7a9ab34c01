{"version": 3, "file": "ErrorClassifier.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/ErrorClassifier.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AAEH,oBAAY,aAAa;IAEvB,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;IAC/B,gBAAgB,qBAAqB;IACrC,YAAY,iBAAiB;IAC7B,mBAAmB,wBAAwB;IAG3C,oBAAoB,yBAAyB;IAC7C,mBAAmB,wBAAwB;IAC3C,gBAAgB,qBAAqB;IACrC,eAAe,oBAAoB;IACnC,iBAAiB,sBAAsB;IAGvC,qBAAqB,0BAA0B;IAC/C,mBAAmB,wBAAwB;IAC3C,aAAa,kBAAkB;CAChC;AAED,MAAM,WAAW,uBAAuB;IACtC,QAAQ,EAAE,aAAa,CAAC;IACxB,WAAW,EAAE,OAAO,CAAC;IACrB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,QAAQ,EAAE;QACR,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;QAC3B,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;QACvB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;QACtB,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;KAC3C,CAAC;IACF,WAAW,CAAC,EAAE;QACZ,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAC3B,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,EAAE,aAAa,CAAC;IACxB,WAAW,EAAE,OAAO,CAAC;IACrB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,uBAAuB,CAAC;IACtC,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,qBAAa,eAAe;IAC1B,OAAO,CAAC,KAAK,CAAiC;;IAM9C;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,mBAAmB;IA0B3C;;OAEG;IACH,OAAO,CAAC,SAAS;IA8CjB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IA+I9B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAoB1B;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,uBAAuB,GAAG,IAAI;IAI5C;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI;IAIzC;;OAEG;IACH,QAAQ,IAAI,uBAAuB,EAAE;IAIrC;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,uBAAuB,CAAC,GAAG,IAAI;IAOpF;;OAEG;IACH,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAKlC;;OAEG;IACH,kBAAkB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAKzC;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,KAAK,GAAG,uBAAuB,CAAC,aAAa,CAAC,GAAG,SAAS;CAIjF"}