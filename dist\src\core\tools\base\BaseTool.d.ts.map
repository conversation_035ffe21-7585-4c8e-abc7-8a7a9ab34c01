{"version": 3, "file": "BaseTool.d.ts", "sourceRoot": "", "sources": ["../../../../../src/core/tools/base/BaseTool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,IAAI,SAAS,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEhE,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,YAAY,GAAG,WAAW,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;IAC5E,IAAI,EAAE,MAAM,EAAE,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,oBAAoB,EAAE,OAAO,CAAC;CAC/B;AAED,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,OAAO,CAAC;IACf,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,QAAQ,EAAE,MAAM,EAAE,CAAC;CACpB;AAED,8BAAsB,QAAS,YAAW,SAAS;IACjD,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,WAAW,EAAE,MAAM,CAAC;IACpC,SAAgB,UAAU,EAAE;QAC1B,IAAI,EAAE,QAAQ,CAAC;QACf,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAChC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;KACrB,CAAC;IACF,SAAgB,oBAAoB,EAAE,OAAO,CAAC;IAE9C,SAAS,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAErB,QAAQ,EAAE,YAAY;aAIlB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAC;aACpE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;IAE5D,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAKzE,WAAW,IAAI,YAAY;IAI3B,WAAW,IAAI,MAAM;IAIrB,OAAO,IAAI,MAAM,EAAE;IAInB,WAAW,IAAI,OAAO;IAItB,UAAU,IAAI,MAAM;IAI3B,SAAS,CAAC,mBAAmB,CAC3B,OAAO,EAAE,MAAM,EACf,cAAc,CAAC,EAAE,MAAM,EACvB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,UAAU;IASb,SAAS,CAAC,iBAAiB,CACzB,KAAK,EAAE,MAAM,EACb,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,UAAU;IASb,SAAS,CAAC,sBAAsB,CAC9B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAC3B,cAAc,EAAE,MAAM,EAAE,GACvB,oBAAoB;IAmBvB,SAAS,CAAC,mBAAmB,CAC3B,KAAK,EAAE,GAAG,EACV,SAAS,EAAE,MAAM,EACjB,OAAO,CAAC,EAAE;QACR,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,UAAU,CAAC,EAAE,OAAO,CAAC;KACtB,GACA,MAAM,EAAE;IA4BX,SAAS,CAAC,mBAAmB,CAC3B,KAAK,EAAE,GAAG,EACV,SAAS,EAAE,MAAM,EACjB,OAAO,CAAC,EAAE;QACR,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB,GACA,MAAM,EAAE;IAuBX,SAAS,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM,EAAE;IAOvE,SAAS,CAAC,kBAAkB,CAC1B,KAAK,EAAE,GAAG,EACV,SAAS,EAAE,MAAM,EACjB,OAAO,CAAC,EAAE;QACR,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;KACvD,GACA,MAAM,EAAE;IAgCX,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM,EAAE;IAoBpE,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAS5C,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM;IAa/C,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,GAAE,MAAa,GAAG,MAAM;IAQrE,QAAQ,IAAI,MAAM;CAG1B"}