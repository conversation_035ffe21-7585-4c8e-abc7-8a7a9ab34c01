{"version": 3, "file": "CircuitBreaker.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/CircuitBreaker.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AAEH,oBAAY,mBAAmB;IAC7B,MAAM,WAAW;IACjB,IAAI,SAAS;IACb,SAAS,cAAc;CACxB;AAED,MAAM,WAAW,oBAAoB;IACnC,wEAAwE;IACxE,gBAAgB,EAAE,MAAM,CAAC;IAEzB,sFAAsF;IACtF,eAAe,EAAE,MAAM,CAAC;IAExB,mFAAmF;IACnF,gBAAgB,EAAE,MAAM,CAAC;IAEzB,wEAAwE;IACxE,gBAAgB,EAAE,MAAM,CAAC;IAEzB,yEAAyE;IACzE,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;IAEtC,kCAAkC;IAClC,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,mBAAmB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,WAAW,EAAE,MAAM,KAAK,IAAI,CAAC;CAC7G;AAED,MAAM,WAAW,qBAAqB;IACpC,KAAK,EAAE,mBAAmB,CAAC;IAC3B,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,CAAC,EAAE,IAAI,CAAC;IACvB,eAAe,CAAC,EAAE,IAAI,CAAC;IACvB,cAAc,EAAE,IAAI,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;CACxB;AAED,qBAAa,mBAAoB,SAAQ,KAAK;gBAChC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,mBAAmB;CAI5D;AAED,qBAAa,cAAc;IACzB,OAAO,CAAC,MAAM,CAAuB;IACrC,OAAO,CAAC,KAAK,CAAmD;IAChE,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,eAAe,CAAC,CAAO;IAC/B,OAAO,CAAC,eAAe,CAAC,CAAO;IAC/B,OAAO,CAAC,cAAc,CAAoB;IAC1C,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,aAAa,CAAa;IAClC,OAAO,CAAC,cAAc,CAAa;IACnC,OAAO,CAAC,WAAW,CAAS;gBAEhB,WAAW,EAAE,MAAM,EAAE,MAAM,GAAE,OAAO,CAAC,oBAAoB,CAAM;IAW3E;;OAEG;IACG,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAkBzD;;OAEG;IACH,OAAO,CAAC,UAAU;IAqBlB;;OAEG;IACH,OAAO,CAAC,SAAS;IAmBjB;;OAEG;IACH,OAAO,CAAC,SAAS;IAwBjB;;OAEG;IACH,OAAO,CAAC,YAAY;IA2BpB;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAS7B;;OAEG;IACH,UAAU,IAAI,qBAAqB;IAcnC;;OAEG;IACH,QAAQ,IAAI,mBAAmB;IAI/B;;OAEG;IACH,UAAU,CAAC,KAAK,EAAE,mBAAmB,GAAG,IAAI;IAI5C;;OAEG;IACH,KAAK,IAAI,IAAI;IAYb;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,CAAC,GAAG,IAAI;IAI1D;;OAEG;IACH,SAAS,IAAI,oBAAoB;IAIjC;;OAEG;IACH,cAAc,IAAI,MAAM;CAGzB;AAED;;GAEG;AACH,qBAAa,sBAAsB;IACjC,OAAO,CAAC,QAAQ,CAAqC;IACrD,OAAO,CAAC,aAAa,CAAgC;gBAEzC,aAAa,GAAE,OAAO,CAAC,oBAAoB,CAAM;IAI7D;;OAEG;IACH,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,GAAG,cAAc;IAQvF;;OAEG;IACG,OAAO,CAAC,CAAC,EACb,WAAW,EAAE,MAAM,EACnB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,MAAM,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,GACrC,OAAO,CAAC,CAAC,CAAC;IAKb;;OAEG;IACH,cAAc,IAAI,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC;IAI7C;;OAEG;IACH,aAAa,IAAI,MAAM,CAAC,MAAM,EAAE,qBAAqB,CAAC;IAQtD;;OAEG;IACH,QAAQ,IAAI,IAAI;IAMhB;;OAEG;IACH,aAAa,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;CAG5C"}