import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class ConfigurationPresetManager extends BaseComponent {
    private configManager;
    private presets;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    private showPresetMenu;
    private viewPresets;
    private applyPreset;
    private saveCurrentAsPreset;
    private deleteCustomPreset;
    private comparePresets;
    private exportPreset;
    private importPreset;
    private displayPresetConfig;
    private displayConfig;
    private getCustomPresets;
    private saveCustomPreset;
    private deleteCustomPresetByName;
}
//# sourceMappingURL=ConfigurationPresetManager.d.ts.map