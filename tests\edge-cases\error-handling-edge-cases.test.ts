import { ErrorHandlingMiddleware } from '../../src/core/error-handling/ErrorHandlingMiddleware';
import { RetryPolicy } from '../../src/core/error-handling/RetryPolicy';
import { CircuitBreaker } from '../../src/core/error-handling/CircuitBreaker';
import { createMockErrorHandlingConfig } from '../utils/mocks';
import { TestError, NetworkError } from '../utils/mocks';

describe('Error Handling Edge Cases', () => {
  let middleware: ErrorHandlingMiddleware;
  let config: any;

  beforeEach(() => {
    config = createMockErrorHandlingConfig();
    middleware = new ErrorHandlingMiddleware(config);
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('memory and resource management', () => {
    it('should handle memory pressure during long retry sequences', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      // Create many concurrent operations that will retry
      const promises = Array.from({ length: 100 }, (_, i) => 
        middleware.execute(operation, `operation-${i}`, `service-${i % 10}`)
          .catch(() => {}) // Ignore failures for this test
      );
      
      // Advance time to trigger all retries
      jest.advanceTimersByTime(10000);
      await Promise.allSettled(promises);
      
      // Should not crash or leak memory
      const metrics = await middleware.getMetrics();
      expect(metrics.totalOperations).toBe(100);
    });

    it('should handle very large error messages', async () => {
      const largeMessage = 'x'.repeat(100000); // 100KB error message
      const operation = jest.fn().mockRejectedValue(new Error(largeMessage));
      
      await expect(middleware.execute(operation, 'test-operation', 'test-service'))
        .rejects.toThrow();
      
      const metrics = await middleware.getMetrics();
      expect(metrics.recentErrors[0].message).toBe(largeMessage);
    });

    it('should handle rapid successive operations', async () => {
      const operations = Array.from({ length: 1000 }, () => 
        jest.fn().mockResolvedValue('success')
      );
      
      const promises = operations.map((op, i) => 
        middleware.execute(op, `rapid-op-${i}`, 'rapid-service')
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(1000);
      expect(results.every(r => r === 'success')).toBe(true);
      
      const metrics = await middleware.getMetrics();
      expect(metrics.totalOperations).toBe(1000);
      expect(metrics.successfulOperations).toBe(1000);
    });
  });

  describe('timing and concurrency edge cases', () => {
    it('should handle operations that complete exactly at timeout', async () => {
      const operation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve('success'), 30000))
      );
      
      const promise = middleware.execute(operation, 'timeout-test', 'test-service');
      
      // Advance to exactly the timeout
      jest.advanceTimersByTime(30000);
      
      const result = await promise;
      expect(result).toBe('success');
    });

    it('should handle concurrent circuit breaker state changes', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      // Start multiple operations that will fail concurrently
      const promises = Array.from({ length: 10 }, () => 
        middleware.execute(failingOperation, 'concurrent-fail', 'concurrent-service')
          .catch(() => {}) // Ignore failures
      );
      
      await Promise.allSettled(promises);
      
      // Circuit should be open
      const metrics = await middleware.getMetrics();
      expect(metrics.circuitBreakerMetrics.perServiceStatus['concurrent-service']).toBe('OPEN');
    });

    it('should handle timer precision issues', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      const promise = middleware.execute(operation, 'timer-precision', 'test-service');
      
      // Advance by slightly less than the expected delay
      jest.advanceTimersByTime(999);
      await testUtils.flushPromises();
      
      // Should not have retried yet
      expect(operation).toHaveBeenCalledTimes(1);
      
      // Advance by the remaining time
      jest.advanceTimersByTime(1);
      await testUtils.flushPromises();
      
      const result = await promise;
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });
  });

  describe('configuration edge cases', () => {
    it('should handle zero delays gracefully', async () => {
      const zeroDelayConfig = {
        ...config,
        retry: { ...config.retry, baseDelay: 0, maxDelay: 0 }
      };
      const zeroDelayMiddleware = new ErrorHandlingMiddleware(zeroDelayConfig);
      
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      const result = await zeroDelayMiddleware.execute(operation, 'zero-delay', 'test-service');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should handle maximum possible delays', async () => {
      const maxDelayConfig = {
        ...config,
        retry: { 
          ...config.retry, 
          baseDelay: Number.MAX_SAFE_INTEGER,
          maxDelay: Number.MAX_SAFE_INTEGER 
        }
      };
      const maxDelayMiddleware = new ErrorHandlingMiddleware(maxDelayConfig);
      
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      // Should not crash with overflow
      await expect(maxDelayMiddleware.execute(operation, 'max-delay', 'test-service'))
        .rejects.toThrow();
    });

    it('should handle invalid configuration values', async () => {
      const invalidConfig = {
        ...config,
        retry: { 
          ...config.retry, 
          maxAttempts: -1,
          baseDelay: -1000,
          backoffMultiplier: 0
        }
      };
      
      // Should either throw during construction or handle gracefully
      expect(() => new ErrorHandlingMiddleware(invalidConfig)).not.toThrow();
    });
  });

  describe('error object edge cases', () => {
    it('should handle null and undefined errors', async () => {
      const nullOperation = jest.fn().mockRejectedValue(null);
      const undefinedOperation = jest.fn().mockRejectedValue(undefined);
      
      await expect(middleware.execute(nullOperation, 'null-error', 'test-service'))
        .rejects.toBe(null);
      
      await expect(middleware.execute(undefinedOperation, 'undefined-error', 'test-service'))
        .rejects.toBe(undefined);
    });

    it('should handle circular reference errors', async () => {
      const circularError: any = new Error('Circular error');
      circularError.self = circularError;
      
      const operation = jest.fn().mockRejectedValue(circularError);
      
      await expect(middleware.execute(operation, 'circular-error', 'test-service'))
        .rejects.toThrow('Circular error');
    });

    it('should handle errors with non-standard properties', async () => {
      const weirdError = new Error('Weird error');
      (weirdError as any).status = { nested: { value: 500 } };
      (weirdError as any).code = ['ARRAY', 'OF', 'CODES'];
      (weirdError as any).retryable = 'maybe';
      
      const operation = jest.fn().mockRejectedValue(weirdError);
      
      await expect(middleware.execute(operation, 'weird-error', 'test-service'))
        .rejects.toThrow('Weird error');
    });

    it('should handle errors that modify themselves during handling', async () => {
      class MutatingError extends Error {
        private callCount = 0;
        
        get message() {
          this.callCount++;
          return `Mutating error (call ${this.callCount})`;
        }
        
        get retryable() {
          return this.callCount < 3;
        }
      }
      
      const operation = jest.fn().mockRejectedValue(new MutatingError());
      
      await expect(middleware.execute(operation, 'mutating-error', 'test-service'))
        .rejects.toBeInstanceOf(MutatingError);
    });
  });

  describe('system resource edge cases', () => {
    it('should handle system clock changes during retries', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      const promise = middleware.execute(operation, 'clock-change', 'test-service');
      
      // Simulate system clock jumping forward
      const originalNow = Date.now;
      Date.now = jest.fn().mockReturnValue(originalNow() + 1000000);
      
      jest.advanceTimersByTime(1000);
      await testUtils.flushPromises();
      
      Date.now = originalNow;
      
      const result = await promise;
      expect(result).toBe('success');
    });

    it('should handle process termination signals gracefully', async () => {
      const longRunningOperation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 60000))
      );
      
      const promise = middleware.execute(longRunningOperation, 'long-running', 'test-service');
      
      // Simulate process termination
      process.emit('SIGTERM');
      
      // Should not crash
      jest.advanceTimersByTime(1000);
      await testUtils.flushPromises();
      
      // Clean up
      jest.advanceTimersByTime(60000);
      await promise;
    });
  });

  describe('boundary value testing', () => {
    it('should handle exactly at failure threshold', async () => {
      const breaker = new CircuitBreaker('boundary-test', config.circuitBreaker);
      
      // Fail exactly at threshold (5 failures)
      for (let i = 0; i < 4; i++) {
        breaker.recordFailure(new NetworkError('Connection failed'));
        expect(breaker.getState()).toBe('CLOSED');
      }
      
      // 5th failure should open circuit
      breaker.recordFailure(new NetworkError('Connection failed'));
      expect(breaker.getState()).toBe('OPEN');
    });

    it('should handle exactly at success threshold in HALF_OPEN', async () => {
      const breaker = new CircuitBreaker('boundary-test', config.circuitBreaker);
      
      // Open circuit
      for (let i = 0; i < 5; i++) {
        breaker.recordFailure(new NetworkError('Connection failed'));
      }
      
      // Transition to HALF_OPEN
      jest.advanceTimersByTime(30001);
      expect(breaker.getState()).toBe('HALF_OPEN');
      
      // Succeed exactly at threshold (2 successes)
      breaker.recordSuccess();
      expect(breaker.getState()).toBe('HALF_OPEN');
      
      breaker.recordSuccess();
      expect(breaker.getState()).toBe('CLOSED');
    });

    it('should handle maximum retry attempts boundary', async () => {
      const policy = new RetryPolicy(config.retry);
      const error = new NetworkError('Connection failed');
      
      // Should retry up to maxAttempts - 1
      expect(policy.shouldRetry(error, 1)).toBe(true);
      expect(policy.shouldRetry(error, 2)).toBe(true);
      expect(policy.shouldRetry(error, 3)).toBe(false); // maxAttempts = 3
    });
  });
});
