import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';
export declare class AnthropicProvider extends BaseProvider {
    name: string;
    private client;
    constructor(config: ProviderConfig);
    sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    streamMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    private processResponse;
    private formatToolCalls;
    protected formatMessages(messages: ChatMessage[]): {
        systemMessage?: string;
        formattedMessages: any[];
    };
    protected formatTools(tools?: BaseTool[]): any[];
    testConnection(): Promise<boolean>;
    protected validateConfig(): void;
}
//# sourceMappingURL=AnthropicProvider.d.ts.map