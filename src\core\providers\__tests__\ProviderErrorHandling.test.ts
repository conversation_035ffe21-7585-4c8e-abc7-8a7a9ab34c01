/**
 * Test Suite for Provider Error Handling
 * 
 * Tests provider-specific error handling configurations, error pattern matching,
 * and integration with the error handling middleware.
 */

import {
  getProviderErrorConfig,
  mergeProviderErrorConfig,
  matchesProviderErrorPattern,
  getProviderRateLimitDelay,
  PROVIDER_ERROR_CONFIGS,
  PROVIDER_ERROR_PATTERNS
} from '../ProviderErrorHandling';

describe('Provider Error Handling', () => {
  
  describe('getProviderErrorConfig', () => {
    test('should return OpenAI configuration for openai provider', () => {
      const config = getProviderErrorConfig('openai');
      
      expect(config.retry?.maxAttempts).toBe(3);
      expect(config.retry?.baseDelay).toBe(1000);
      expect(config.retry?.backoffMultiplier).toBe(2);
      expect(config.circuitBreaker?.failureThreshold).toBe(5);
    });

    test('should return Anthropic configuration for anthropic provider', () => {
      const config = getProviderErrorConfig('anthropic');
      
      expect(config.retry?.maxAttempts).toBe(3);
      expect(config.retry?.baseDelay).toBe(1500);
      expect(config.retry?.attemptTimeout).toBe(90000);
      expect(config.circuitBreaker?.failureThreshold).toBe(4);
    });

    test('should return Google configuration for google provider', () => {
      const config = getProviderErrorConfig('google');
      
      expect(config.retry?.maxAttempts).toBe(4);
      expect(config.retry?.baseDelay).toBe(800);
      expect(config.retry?.backoffMultiplier).toBe(1.8);
      expect(config.circuitBreaker?.failureThreshold).toBe(6);
    });

    test('should return default (OpenAI) configuration for unknown provider', () => {
      const config = getProviderErrorConfig('unknown-provider');
      
      expect(config.retry?.maxAttempts).toBe(3);
      expect(config.retry?.baseDelay).toBe(1000);
      expect(config.retry?.backoffMultiplier).toBe(2);
    });

    test('should handle case-insensitive provider names', () => {
      const config1 = getProviderErrorConfig('OPENAI');
      const config2 = getProviderErrorConfig('OpenAI');
      const config3 = getProviderErrorConfig('openai');
      
      expect(config1).toEqual(config2);
      expect(config2).toEqual(config3);
    });
  });

  describe('mergeProviderErrorConfig', () => {
    test('should return provider defaults when no user config provided', () => {
      const config = mergeProviderErrorConfig('openai');
      
      expect(config.retry.maxAttempts).toBe(3);
      expect(config.retry.baseDelay).toBe(1000);
      expect(config.enableErrorLogging).toBe(true);
      expect(config.enableMetrics).toBe(true);
    });

    test('should merge user config with provider defaults', () => {
      const userConfig = {
        retry: {
          maxAttempts: 5,
          baseDelay: 2000
        },
        enableErrorLogging: false
      };

      const config = mergeProviderErrorConfig('openai', userConfig);
      
      expect(config.retry.maxAttempts).toBe(5); // User override
      expect(config.retry.baseDelay).toBe(2000); // User override
      expect(config.retry.backoffMultiplier).toBe(2); // Provider default
      expect(config.enableErrorLogging).toBe(false); // User override
      expect(config.enableMetrics).toBe(true); // Provider default
    });

    test('should deep merge operation policies', () => {
      const userConfig = {
        operationPolicies: {
          providerCalls: {
            maxAttempts: 5
          }
        }
      };

      const config = mergeProviderErrorConfig('anthropic', userConfig);
      
      expect(config.operationPolicies.providerCalls.maxAttempts).toBe(5); // User override
      expect(config.operationPolicies.providerCalls.baseDelay).toBe(1500); // Provider default
    });
  });

  describe('matchesProviderErrorPattern', () => {
    test('should match OpenAI rate limit errors', () => {
      const rateLimitError = new Error('Rate limit exceeded. Please try again later.');
      const quotaError = new Error('You have exceeded your quota for this month');
      const tooManyError = new Error('Too many requests in a short period');
      
      expect(matchesProviderErrorPattern('openai', rateLimitError, 'rateLimitErrors')).toBe(true);
      expect(matchesProviderErrorPattern('openai', quotaError, 'rateLimitErrors')).toBe(true);
      expect(matchesProviderErrorPattern('openai', tooManyError, 'rateLimitErrors')).toBe(true);
    });

    test('should match OpenAI authentication errors', () => {
      const authError = new Error('Invalid API key provided');
      const unauthorizedError = new Error('Unauthorized access - 401');
      
      expect(matchesProviderErrorPattern('openai', authError, 'authenticationErrors')).toBe(true);
      expect(matchesProviderErrorPattern('openai', unauthorizedError, 'authenticationErrors')).toBe(true);
    });

    test('should match Anthropic specific error patterns', () => {
      const rateLimitError = new Error('rate_limit_error: Too many requests');
      const authError = new Error('authentication_error: Invalid API key');
      const serviceError = new Error('overloaded_error: Service temporarily unavailable');
      
      expect(matchesProviderErrorPattern('anthropic', rateLimitError, 'rateLimitErrors')).toBe(true);
      expect(matchesProviderErrorPattern('anthropic', authError, 'authenticationErrors')).toBe(true);
      expect(matchesProviderErrorPattern('anthropic', serviceError, 'serviceUnavailableErrors')).toBe(true);
    });

    test('should match Google specific error patterns', () => {
      const quotaError = new Error('RATE_LIMIT_EXCEEDED: Quota exceeded');
      const authError = new Error('UNAUTHENTICATED: Invalid credentials');
      const serviceError = new Error('UNAVAILABLE: Service temporarily unavailable');
      
      expect(matchesProviderErrorPattern('google', quotaError, 'rateLimitErrors')).toBe(true);
      expect(matchesProviderErrorPattern('google', authError, 'authenticationErrors')).toBe(true);
      expect(matchesProviderErrorPattern('google', serviceError, 'serviceUnavailableErrors')).toBe(true);
    });

    test('should match network errors for all providers', () => {
      const connectionError = new Error('ECONNREFUSED: Connection refused');
      const dnsError = new Error('ENOTFOUND: DNS lookup failed');
      const timeoutError = new Error('ETIMEDOUT: Request timeout');
      
      ['openai', 'anthropic', 'google', 'deepseek'].forEach(provider => {
        expect(matchesProviderErrorPattern(provider, connectionError, 'networkErrors')).toBe(true);
        expect(matchesProviderErrorPattern(provider, dnsError, 'networkErrors')).toBe(true);
        expect(matchesProviderErrorPattern(provider, timeoutError, 'networkErrors')).toBe(true);
      });
    });

    test('should not match unrelated errors', () => {
      const validationError = new Error('Invalid input parameters');
      const unknownError = new Error('Something went wrong');
      
      expect(matchesProviderErrorPattern('openai', validationError, 'rateLimitErrors')).toBe(false);
      expect(matchesProviderErrorPattern('openai', unknownError, 'authenticationErrors')).toBe(false);
    });

    test('should return false for unknown provider', () => {
      const error = new Error('Rate limit exceeded');
      
      expect(matchesProviderErrorPattern('unknown-provider', error, 'rateLimitErrors')).toBe(false);
    });
  });

  describe('getProviderRateLimitDelay', () => {
    test('should extract retry-after from error message', () => {
      const error = new Error('Rate limit exceeded. Retry after 120 seconds.');
      
      const delay = getProviderRateLimitDelay('openai', error);
      expect(delay).toBe(120000); // 120 seconds in milliseconds
    });

    test('should return provider-specific default delays', () => {
      const error = new Error('Rate limit exceeded');
      
      expect(getProviderRateLimitDelay('openai', error)).toBe(60000);
      expect(getProviderRateLimitDelay('anthropic', error)).toBe(90000);
      expect(getProviderRateLimitDelay('google', error)).toBe(45000);
      expect(getProviderRateLimitDelay('deepseek', error)).toBe(75000);
    });

    test('should return default delay for unknown provider', () => {
      const error = new Error('Rate limit exceeded');
      
      expect(getProviderRateLimitDelay('unknown-provider', error)).toBe(60000);
    });

    test('should handle case-insensitive provider names', () => {
      const error = new Error('Rate limit exceeded');
      
      expect(getProviderRateLimitDelay('OPENAI', error)).toBe(60000);
      expect(getProviderRateLimitDelay('OpenAI', error)).toBe(60000);
    });
  });

  describe('PROVIDER_ERROR_CONFIGS', () => {
    test('should have configurations for all supported providers', () => {
      const expectedProviders = ['openai', 'anthropic', 'google', 'deepseek'];
      
      expectedProviders.forEach(provider => {
        expect(PROVIDER_ERROR_CONFIGS[provider]).toBeDefined();
        expect(PROVIDER_ERROR_CONFIGS[provider].retry).toBeDefined();
        expect(PROVIDER_ERROR_CONFIGS[provider].circuitBreaker).toBeDefined();
      });
    });

    test('should have valid retry configurations', () => {
      Object.values(PROVIDER_ERROR_CONFIGS).forEach(config => {
        expect(config.retry?.maxAttempts).toBeGreaterThan(0);
        expect(config.retry?.baseDelay).toBeGreaterThan(0);
        expect(config.retry?.backoffMultiplier).toBeGreaterThan(1);
        expect(config.retry?.maxDelay).toBeGreaterThan(config.retry?.baseDelay!);
      });
    });

    test('should have valid circuit breaker configurations', () => {
      Object.values(PROVIDER_ERROR_CONFIGS).forEach(config => {
        expect(config.circuitBreaker?.failureThreshold).toBeGreaterThan(0);
        expect(config.circuitBreaker?.recoveryTimeout).toBeGreaterThan(0);
        expect(config.circuitBreaker?.successThreshold).toBeGreaterThan(0);
        expect(config.circuitBreaker?.enabled).toBe(true);
      });
    });
  });

  describe('PROVIDER_ERROR_PATTERNS', () => {
    test('should have error patterns for all supported providers', () => {
      const expectedProviders = ['openai', 'anthropic', 'google', 'deepseek'];
      const expectedPatternTypes = ['rateLimitErrors', 'authenticationErrors', 'serviceUnavailableErrors', 'networkErrors'];
      
      expectedProviders.forEach(provider => {
        expect(PROVIDER_ERROR_PATTERNS[provider]).toBeDefined();
        
        expectedPatternTypes.forEach(patternType => {
          expect(PROVIDER_ERROR_PATTERNS[provider][patternType]).toBeDefined();
          expect(Array.isArray(PROVIDER_ERROR_PATTERNS[provider][patternType])).toBe(true);
          expect(PROVIDER_ERROR_PATTERNS[provider][patternType].length).toBeGreaterThan(0);
        });
      });
    });

    test('should have valid regex patterns', () => {
      Object.values(PROVIDER_ERROR_PATTERNS).forEach(providerPatterns => {
        Object.values(providerPatterns).forEach(patterns => {
          patterns.forEach(pattern => {
            expect(pattern).toBeInstanceOf(RegExp);
          });
        });
      });
    });
  });

  describe('Integration Tests', () => {
    test('should provide complete error handling configuration for each provider', () => {
      const providers = ['openai', 'anthropic', 'google', 'deepseek'];
      
      providers.forEach(provider => {
        const config = mergeProviderErrorConfig(provider);
        
        // Verify all required configuration is present
        expect(config.retry).toBeDefined();
        expect(config.circuitBreaker).toBeDefined();
        expect(config.operationPolicies).toBeDefined();
        expect(config.operationPolicies.providerCalls).toBeDefined();
        
        // Verify configuration values are reasonable
        expect(config.retry.maxAttempts).toBeGreaterThan(0);
        expect(config.retry.baseDelay).toBeGreaterThan(0);
        expect(config.circuitBreaker.failureThreshold).toBeGreaterThan(0);
        
        // Verify boolean flags are set
        expect(typeof config.enableErrorLogging).toBe('boolean');
        expect(typeof config.enableMetrics).toBe('boolean');
        expect(typeof config.showRetryProgress).toBe('boolean');
        expect(typeof config.allowRetryCancel).toBe('boolean');
      });
    });

    test('should handle provider-specific error scenarios', () => {
      const testCases = [
        { provider: 'openai', error: new Error('Rate limit exceeded'), pattern: 'rateLimitErrors' },
        { provider: 'anthropic', error: new Error('rate_limit_error'), pattern: 'rateLimitErrors' },
        { provider: 'google', error: new Error('RATE_LIMIT_EXCEEDED'), pattern: 'rateLimitErrors' },
        { provider: 'deepseek', error: new Error('quota exceeded'), pattern: 'rateLimitErrors' }
      ];

      testCases.forEach(({ provider, error, pattern }) => {
        expect(matchesProviderErrorPattern(provider, error, pattern as any)).toBe(true);
        
        const delay = getProviderRateLimitDelay(provider, error);
        expect(delay).toBeGreaterThan(0);
        
        const config = getProviderErrorConfig(provider);
        expect(config.retry?.maxAttempts).toBeGreaterThan(0);
      });
    });
  });
});
