"use strict";
/**
 * Circuit Breaker Pattern Implementation
 *
 * Provides fault tolerance by monitoring failures and preventing calls to failing services:
 * - CLOSED: Normal operation, calls pass through
 * - OPEN: Service is failing, calls are rejected immediately
 * - HALF_OPEN: Testing if service has recovered
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreakerRegistry = exports.CircuitBreaker = exports.CircuitBreakerError = exports.CircuitBreakerState = void 0;
var CircuitBreakerState;
(function (CircuitBreakerState) {
    CircuitBreakerState["CLOSED"] = "CLOSED";
    CircuitBreakerState["OPEN"] = "OPEN";
    CircuitBreakerState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitBreakerState || (exports.CircuitBreakerState = CircuitBreakerState = {}));
class CircuitBreakerError extends Error {
    constructor(serviceName, state) {
        super(`Circuit breaker is ${state} for service: ${serviceName}`);
        this.name = 'CircuitBreakerError';
    }
}
exports.CircuitBreakerError = CircuitBreakerError;
class CircuitBreaker {
    config;
    state = CircuitBreakerState.CLOSED;
    failureCount = 0;
    successCount = 0;
    lastFailureTime;
    lastSuccessTime;
    stateChangedAt = new Date();
    totalCalls = 0;
    totalFailures = 0;
    totalSuccesses = 0;
    serviceName;
    constructor(serviceName, config = {}) {
        this.serviceName = serviceName;
        this.config = {
            failureThreshold: 5,
            recoveryTimeout: 30000,
            successThreshold: 2,
            monitoringWindow: 60000,
            ...config
        };
    }
    /**
     * Execute a function with circuit breaker protection
     */
    async execute(operation) {
        this.totalCalls++;
        // Check if circuit breaker should allow the call
        if (!this.canExecute()) {
            throw new CircuitBreakerError(this.serviceName, this.state);
        }
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        }
        catch (error) {
            this.onFailure(error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    /**
     * Check if the circuit breaker allows execution
     */
    canExecute() {
        switch (this.state) {
            case CircuitBreakerState.CLOSED:
                return true;
            case CircuitBreakerState.OPEN:
                // Check if recovery timeout has passed
                if (this.shouldAttemptRecovery()) {
                    this.transitionTo(CircuitBreakerState.HALF_OPEN);
                    return true;
                }
                return false;
            case CircuitBreakerState.HALF_OPEN:
                return true;
            default:
                return false;
        }
    }
    /**
     * Handle successful operation
     */
    onSuccess() {
        this.successCount++;
        this.totalSuccesses++;
        this.lastSuccessTime = new Date();
        switch (this.state) {
            case CircuitBreakerState.HALF_OPEN:
                if (this.successCount >= this.config.successThreshold) {
                    this.transitionTo(CircuitBreakerState.CLOSED);
                }
                break;
            case CircuitBreakerState.CLOSED:
                // Reset failure count on success in closed state
                this.failureCount = 0;
                break;
        }
    }
    /**
     * Handle failed operation
     */
    onFailure(error) {
        // Check if this error should count as a failure
        if (this.config.isFailure && !this.config.isFailure(error)) {
            return;
        }
        this.failureCount++;
        this.totalFailures++;
        this.lastFailureTime = new Date();
        switch (this.state) {
            case CircuitBreakerState.CLOSED:
                if (this.failureCount >= this.config.failureThreshold) {
                    this.transitionTo(CircuitBreakerState.OPEN);
                }
                break;
            case CircuitBreakerState.HALF_OPEN:
                // Any failure in half-open state transitions back to open
                this.transitionTo(CircuitBreakerState.OPEN);
                break;
        }
    }
    /**
     * Transition to a new state
     */
    transitionTo(newState) {
        const oldState = this.state;
        this.state = newState;
        this.stateChangedAt = new Date();
        // Reset counters based on new state
        switch (newState) {
            case CircuitBreakerState.CLOSED:
                this.failureCount = 0;
                this.successCount = 0;
                break;
            case CircuitBreakerState.OPEN:
                this.successCount = 0;
                break;
            case CircuitBreakerState.HALF_OPEN:
                this.successCount = 0;
                break;
        }
        // Notify state change
        if (this.config.onStateChange) {
            this.config.onStateChange(oldState, newState, this.serviceName);
        }
    }
    /**
     * Check if we should attempt recovery from OPEN state
     */
    shouldAttemptRecovery() {
        if (!this.lastFailureTime) {
            return true;
        }
        const timeSinceLastFailure = Date.now() - this.lastFailureTime.getTime();
        return timeSinceLastFailure >= this.config.recoveryTimeout;
    }
    /**
     * Get current metrics
     */
    getMetrics() {
        return {
            state: this.state,
            failureCount: this.failureCount,
            successCount: this.successCount,
            lastFailureTime: this.lastFailureTime,
            lastSuccessTime: this.lastSuccessTime,
            stateChangedAt: this.stateChangedAt,
            totalCalls: this.totalCalls,
            totalFailures: this.totalFailures,
            totalSuccesses: this.totalSuccesses
        };
    }
    /**
     * Get current state
     */
    getState() {
        return this.state;
    }
    /**
     * Force state transition (for testing or manual intervention)
     */
    forceState(state) {
        this.transitionTo(state);
    }
    /**
     * Reset circuit breaker to initial state
     */
    reset() {
        this.state = CircuitBreakerState.CLOSED;
        this.failureCount = 0;
        this.successCount = 0;
        this.lastFailureTime = undefined;
        this.lastSuccessTime = undefined;
        this.stateChangedAt = new Date();
        this.totalCalls = 0;
        this.totalFailures = 0;
        this.totalSuccesses = 0;
    }
    /**
     * Update configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get service name
     */
    getServiceName() {
        return this.serviceName;
    }
}
exports.CircuitBreaker = CircuitBreaker;
/**
 * Circuit Breaker Registry for managing multiple circuit breakers
 */
class CircuitBreakerRegistry {
    breakers = new Map();
    defaultConfig;
    constructor(defaultConfig = {}) {
        this.defaultConfig = defaultConfig;
    }
    /**
     * Get or create a circuit breaker for a service
     */
    getBreaker(serviceName, config) {
        if (!this.breakers.has(serviceName)) {
            const breakerConfig = { ...this.defaultConfig, ...config };
            this.breakers.set(serviceName, new CircuitBreaker(serviceName, breakerConfig));
        }
        return this.breakers.get(serviceName);
    }
    /**
     * Execute operation with circuit breaker protection
     */
    async execute(serviceName, operation, config) {
        const breaker = this.getBreaker(serviceName, config);
        return breaker.execute(operation);
    }
    /**
     * Get all circuit breakers
     */
    getAllBreakers() {
        return new Map(this.breakers);
    }
    /**
     * Get metrics for all circuit breakers
     */
    getAllMetrics() {
        const metrics = {};
        for (const [serviceName, breaker] of this.breakers) {
            metrics[serviceName] = breaker.getMetrics();
        }
        return metrics;
    }
    /**
     * Reset all circuit breakers
     */
    resetAll() {
        for (const breaker of this.breakers.values()) {
            breaker.reset();
        }
    }
    /**
     * Remove a circuit breaker
     */
    removeBreaker(serviceName) {
        return this.breakers.delete(serviceName);
    }
}
exports.CircuitBreakerRegistry = CircuitBreakerRegistry;
//# sourceMappingURL=CircuitBreaker.js.map