{"version": 3, "file": "RetryPolicy.js", "sourceRoot": "", "sources": ["../../../../src/core/error-handling/RetryPolicy.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;AAgDH,MAAa,WAAW;IACd,MAAM,CAAoB;IAC1B,SAAS,GAAW,CAAC,CAAC,CAAC,0BAA0B;IAEzD,YAAY,SAAqC,EAAE;QACjD,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,aAAa;YAC9B,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,MAAM;YAClB,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,SAA2B,EAC3B,aAAsB;QAEtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAmB,EAAE,CAAC;QACpC,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;YACpE,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,MAAM,WAAW,GAAiB;gBAChC,OAAO;gBACP,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAClC,CAAC;YAEF,IAAI,CAAC;gBACH,mCAAmC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;oBACvC,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;oBACjE,CAAC,CAAC,MAAM,SAAS,EAAE,CAAC;gBAEtB,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAE3B,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM;oBACN,QAAQ;oBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBACrC,YAAY,EAAE,OAAO;iBACtB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;gBAC9B,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC;gBAEjD,sCAAsC;gBACtC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC;oBAC/C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,2CAA2C;gBAC3C,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACxC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,mCAAmC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3C,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;gBAC1B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAE3B,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,SAAS;YAChB,QAAQ;YACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YACrC,YAAY,EAAE,QAAQ,CAAC,MAAM;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe;QACpC,qCAAqC;QACrC,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QACtG,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAa;QAC/B,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,KAAK,MAAM;gBACT,gCAAgC;gBAChC,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;YAE/B,KAAK,cAAc;gBACjB,wDAAwD;gBACxD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBACxD,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;gBAC/B,OAAO,aAAa,CAAC;YAEvB;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAY,EAAE,OAAe;QACpD,yCAAyC;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,4DAA4D;QAC5D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAY;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,iBAAiB,GAAG;YACxB,SAAS;YACT,SAAS;YACT,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,cAAc;YACd,gBAAgB;YAChB,YAAY;YACZ,mBAAmB;YACnB,qBAAqB;YACrB,uBAAuB;YACvB,aAAa;YACb,iBAAiB;SAClB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,WAAW,CAAI,OAAmB,EAAE,SAAiB;QAC3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,SAAS,IAAI,CAAC,CAAC,CAAC;YAChE,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,OAAO;iBACJ,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,OAAmC;QAC9C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,SAAqC,EAAE;QACnD,OAAO,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM,CAAU,OAAO,GAAG;QACxB,sCAAsC;QACtC,IAAI,EAAE,IAAI,WAAW,CAAC;YACpB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,iBAAiB,EAAE,GAAG;YACtB,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,yCAAyC;QACzC,QAAQ,EAAE,IAAI,WAAW,CAAC;YACxB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,+CAA+C;QAC/C,UAAU,EAAE,IAAI,WAAW,CAAC;YAC1B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,kDAAkD;QAClD,YAAY,EAAE,IAAI,WAAW,CAAC;YAC5B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,KAAK;SAChB,CAAC;KACH,CAAC;;AAtPJ,kCAuPC"}