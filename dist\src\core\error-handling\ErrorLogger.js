"use strict";
/**
 * Comprehensive Error Logging System
 *
 * Provides structured logging for:
 * - Error events with context
 * - Retry attempts and outcomes
 * - Circuit breaker state changes
 * - Performance monitoring
 * - Debug information
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorLogger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["FATAL"] = 4] = "FATAL";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class ErrorLogger {
    config;
    logEntries = [];
    logHandlers = [];
    constructor(config = {}) {
        this.config = {
            level: LogLevel.INFO,
            enableConsoleOutput: true,
            enableFileOutput: false,
            enableStructuredLogging: true,
            maxLogEntries: 1000,
            includeStackTrace: true,
            includeTimestamp: true,
            dateFormat: 'ISO',
            ...config
        };
        if (this.config.enableConsoleOutput) {
            this.addLogHandler(this.consoleLogHandler.bind(this));
        }
    }
    /**
     * Log debug information
     */
    debug(message, context, operationId) {
        this.log(LogLevel.DEBUG, 'DEBUG', message, context, undefined, operationId);
    }
    /**
     * Log informational messages
     */
    info(message, context, operationId) {
        this.log(LogLevel.INFO, 'INFO', message, context, undefined, operationId);
    }
    /**
     * Log warnings
     */
    warn(message, context, operationId) {
        this.log(LogLevel.WARN, 'WARNING', message, context, undefined, operationId);
    }
    /**
     * Log errors
     */
    error(message, error, context, operationId) {
        this.log(LogLevel.ERROR, 'ERROR', message, context, error, operationId);
    }
    /**
     * Log fatal errors
     */
    fatal(message, error, context, operationId) {
        this.log(LogLevel.FATAL, 'FATAL', message, context, error, operationId);
    }
    /**
     * Log retry attempt
     */
    logRetryAttempt(operation, attempt, maxAttempts, delay, error, operationId) {
        this.log(LogLevel.WARN, 'RETRY', `Retry attempt ${attempt}/${maxAttempts} for ${operation} after ${delay}ms delay`, {
            operation,
            attempt,
            maxAttempts,
            delay,
            errorMessage: error.message,
            errorType: error.constructor.name
        }, error, operationId);
    }
    /**
     * Log circuit breaker state change
     */
    logCircuitBreakerStateChange(serviceName, oldState, newState, reason, operationId) {
        this.log(LogLevel.WARN, 'CIRCUIT_BREAKER', `Circuit breaker for ${serviceName}: ${oldState} -> ${newState}`, {
            serviceName,
            oldState,
            newState,
            reason
        }, undefined, operationId);
    }
    /**
     * Log operation start
     */
    logOperationStart(operation, parameters, operationId) {
        this.log(LogLevel.DEBUG, 'OPERATION_START', `Starting operation: ${operation}`, {
            operation,
            parameters
        }, undefined, operationId);
    }
    /**
     * Log operation completion
     */
    logOperationComplete(operation, success, duration, result, operationId) {
        this.log(success ? LogLevel.INFO : LogLevel.ERROR, 'OPERATION_COMPLETE', `Operation ${operation} ${success ? 'completed' : 'failed'} in ${duration}ms`, {
            operation,
            success,
            duration,
            result: success ? 'success' : result
        }, undefined, operationId);
    }
    /**
     * Log performance metrics
     */
    logPerformanceMetrics(operation, metrics, operationId) {
        this.log(LogLevel.DEBUG, 'PERFORMANCE', `Performance metrics for ${operation}`, {
            operation,
            ...metrics
        }, undefined, operationId);
    }
    /**
     * Core logging method
     */
    log(level, category, message, context, error, operationId) {
        if (level < this.config.level) {
            return; // Skip logs below configured level
        }
        const entry = {
            timestamp: new Date(),
            level,
            category,
            message,
            context,
            error,
            stackTrace: error && this.config.includeStackTrace ? error.stack : undefined,
            operationId
        };
        // Add to internal log storage
        this.logEntries.push(entry);
        // Maintain max log entries
        if (this.logEntries.length > this.config.maxLogEntries) {
            this.logEntries = this.logEntries.slice(-this.config.maxLogEntries);
        }
        // Send to all log handlers
        for (const handler of this.logHandlers) {
            try {
                handler(entry);
            }
            catch (handlerError) {
                console.error('Log handler error:', handlerError);
            }
        }
    }
    /**
     * Console log handler
     */
    consoleLogHandler(entry) {
        const timestamp = this.config.includeTimestamp
            ? `[${this.formatTimestamp(entry.timestamp)}] `
            : '';
        const levelName = LogLevel[entry.level];
        const prefix = `${timestamp}${levelName} [${entry.category}]`;
        let logMessage = `${prefix} ${entry.message}`;
        if (entry.context && Object.keys(entry.context).length > 0) {
            logMessage += `\nContext: ${JSON.stringify(entry.context, null, 2)}`;
        }
        if (entry.error) {
            logMessage += `\nError: ${entry.error.message}`;
            if (entry.stackTrace) {
                logMessage += `\nStack: ${entry.stackTrace}`;
            }
        }
        // Use appropriate console method based on log level
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(logMessage);
                break;
            case LogLevel.INFO:
                console.info(logMessage);
                break;
            case LogLevel.WARN:
                console.warn(logMessage);
                break;
            case LogLevel.ERROR:
            case LogLevel.FATAL:
                console.error(logMessage);
                break;
        }
    }
    /**
     * Format timestamp according to configuration
     */
    formatTimestamp(timestamp) {
        switch (this.config.dateFormat) {
            case 'ISO':
                return timestamp.toISOString();
            case 'LOCAL':
                return timestamp.toLocaleString();
            case 'UNIX':
                return timestamp.getTime().toString();
            default:
                return timestamp.toISOString();
        }
    }
    /**
     * Add custom log handler
     */
    addLogHandler(handler) {
        this.logHandlers.push(handler);
    }
    /**
     * Remove log handler
     */
    removeLogHandler(handler) {
        const index = this.logHandlers.indexOf(handler);
        if (index > -1) {
            this.logHandlers.splice(index, 1);
        }
    }
    /**
     * Get recent log entries
     */
    getRecentLogs(count = 100, level) {
        let logs = this.logEntries.slice(-count);
        if (level !== undefined) {
            logs = logs.filter(entry => entry.level >= level);
        }
        return logs;
    }
    /**
     * Search logs by criteria
     */
    searchLogs(criteria) {
        return this.logEntries.filter(entry => {
            if (criteria.category && entry.category !== criteria.category)
                return false;
            if (criteria.operationId && entry.operationId !== criteria.operationId)
                return false;
            if (criteria.level !== undefined && entry.level < criteria.level)
                return false;
            if (criteria.startTime && entry.timestamp < criteria.startTime)
                return false;
            if (criteria.endTime && entry.timestamp > criteria.endTime)
                return false;
            if (criteria.messageContains && !entry.message.includes(criteria.messageContains))
                return false;
            return true;
        });
    }
    /**
     * Export logs as JSON
     */
    exportLogs(criteria) {
        const logs = criteria ? this.searchLogs(criteria) : this.logEntries;
        return JSON.stringify(logs, null, 2);
    }
    /**
     * Clear all logs
     */
    clearLogs() {
        this.logEntries = [];
    }
    /**
     * Update logger configuration
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get log statistics
     */
    getLogStatistics() {
        const logsByLevel = {};
        const logsByCategory = {};
        for (const entry of this.logEntries) {
            const levelName = LogLevel[entry.level];
            logsByLevel[levelName] = (logsByLevel[levelName] || 0) + 1;
            logsByCategory[entry.category] = (logsByCategory[entry.category] || 0) + 1;
        }
        return {
            totalLogs: this.logEntries.length,
            logsByLevel,
            logsByCategory,
            oldestLog: this.logEntries.length > 0 ? this.logEntries[0].timestamp : undefined,
            newestLog: this.logEntries.length > 0 ? this.logEntries[this.logEntries.length - 1].timestamp : undefined
        };
    }
}
exports.ErrorLogger = ErrorLogger;
//# sourceMappingURL=ErrorLogger.js.map