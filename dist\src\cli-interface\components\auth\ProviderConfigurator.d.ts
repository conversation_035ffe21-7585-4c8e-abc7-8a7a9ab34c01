import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';
export declare class ProviderConfigurator extends BaseComponent {
    private configManager;
    constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager);
    render(): Promise<void>;
    configureProvider(providerName: string): Promise<boolean>;
    private gatherProviderConfiguration;
    private gatherAdvancedConfiguration;
    private saveProviderConfiguration;
    private handleFirstProviderSetup;
    testProviderConnection(providerName: string): Promise<boolean>;
    private formatModelChoice;
    private isValidUrl;
}
//# sourceMappingURL=ProviderConfigurator.d.ts.map