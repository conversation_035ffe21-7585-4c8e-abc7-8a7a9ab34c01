"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandlingMetricsViewer = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
class ErrorHandlingMetricsViewer extends BaseComponent_1.BaseComponent {
    errorHandler;
    constructor(state, config, errorHandler) {
        super(state, config);
        this.errorHandler = errorHandler;
    }
    async render() {
        this.utils.clearScreen();
        this.utils.showBanner('📊 Error Handling Metrics & Status', 'View system performance and error handling statistics');
        await this.showMetricsMenu();
    }
    async showMetricsMenu() {
        const choices = [
            { name: '📈 Overall Statistics', value: 'overall' },
            { name: '🔄 Retry Metrics', value: 'retry' },
            { name: '⚡ Circuit Breaker Status', value: 'circuit' },
            { name: '🎯 Per-Service Metrics', value: 'per_service' },
            { name: '⏱️ Performance Metrics', value: 'performance' },
            { name: '🔍 Recent Errors', value: 'recent_errors' },
            { name: '📊 Export Metrics', value: 'export' },
            { name: '🔄 Refresh Data', value: 'refresh' },
            { name: '🔙 Back to Error Handling Config', value: 'back' },
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'What would you like to view?',
                choices,
            },
        ]);
        switch (action) {
            case 'overall':
                await this.showOverallStatistics();
                break;
            case 'retry':
                await this.showRetryMetrics();
                break;
            case 'circuit':
                await this.showCircuitBreakerStatus();
                break;
            case 'per_service':
                await this.showPerServiceMetrics();
                break;
            case 'performance':
                await this.showPerformanceMetrics();
                break;
            case 'recent_errors':
                await this.showRecentErrors();
                break;
            case 'export':
                await this.exportMetrics();
                break;
            case 'refresh':
                this.utils.showInfo('Metrics refreshed!');
                await this.utils.waitForKeyPress();
                break;
            case 'back':
                return;
        }
        await this.showMetricsMenu();
    }
    async showOverallStatistics() {
        this.utils.clearScreen();
        this.utils.showBanner('📈 Overall Error Handling Statistics');
        try {
            const metrics = await this.errorHandler.getMetrics();
            this.utils.showInfo('System Overview:');
            console.log();
            console.log(`📊 Total Operations: ${metrics.totalOperations || 0}`);
            console.log(`✅ Successful Operations: ${metrics.successfulOperations || 0}`);
            console.log(`❌ Failed Operations: ${metrics.failedOperations || 0}`);
            console.log(`🔄 Operations with Retries: ${metrics.operationsWithRetries || 0}`);
            console.log();
            const successRate = metrics.totalOperations > 0
                ? ((metrics.successfulOperations || 0) / metrics.totalOperations * 100).toFixed(1)
                : '0.0';
            const retryRate = metrics.totalOperations > 0
                ? ((metrics.operationsWithRetries || 0) / metrics.totalOperations * 100).toFixed(1)
                : '0.0';
            console.log(`📈 Success Rate: ${successRate}%`);
            console.log(`🔄 Retry Rate: ${retryRate}%`);
            console.log();
            // Show status indicators
            if (parseFloat(successRate) >= 95) {
                this.utils.showSuccess('✅ System operating normally');
            }
            else if (parseFloat(successRate) >= 85) {
                this.utils.showWarning('⚠️ System experiencing some issues');
            }
            else {
                this.utils.showError('🚨 System experiencing significant issues');
            }
            console.log();
            console.log(`🕒 Last Updated: ${new Date().toLocaleString()}`);
        }
        catch (error) {
            this.utils.showError('Failed to retrieve metrics');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async showRetryMetrics() {
        this.utils.clearScreen();
        this.utils.showBanner('🔄 Retry Metrics');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const retryMetrics = metrics.retryMetrics || {};
            this.utils.showInfo('Retry Statistics:');
            console.log();
            console.log(`🔄 Total Retry Attempts: ${retryMetrics.totalRetryAttempts || 0}`);
            console.log(`✅ Successful Retries: ${retryMetrics.successfulRetries || 0}`);
            console.log(`❌ Failed Retries: ${retryMetrics.failedRetries || 0}`);
            console.log(`⏱️ Average Retry Delay: ${retryMetrics.averageRetryDelay || 0}ms`);
            console.log();
            // Retry attempt distribution
            if (retryMetrics.retryAttemptDistribution) {
                console.log('📊 Retry Attempt Distribution:');
                Object.entries(retryMetrics.retryAttemptDistribution).forEach(([attempts, count]) => {
                    const percentage = retryMetrics.totalRetryAttempts > 0
                        ? (count / retryMetrics.totalRetryAttempts * 100).toFixed(1)
                        : '0.0';
                    console.log(`  • ${attempts} attempts: ${count} operations (${percentage}%)`);
                });
                console.log();
            }
            // Recent retry trends
            if (retryMetrics.recentTrends) {
                console.log('📈 Recent Trends (last hour):');
                console.log(`  • Retry rate: ${retryMetrics.recentTrends.retryRate || 0}%`);
                console.log(`  • Average delay: ${retryMetrics.recentTrends.averageDelay || 0}ms`);
                console.log(`  • Peak retry time: ${retryMetrics.recentTrends.peakRetryTime || 'N/A'}`);
            }
        }
        catch (error) {
            this.utils.showError('Failed to retrieve retry metrics');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async showCircuitBreakerStatus() {
        this.utils.clearScreen();
        this.utils.showBanner('⚡ Circuit Breaker Status');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const circuitMetrics = metrics.circuitBreakerMetrics || {};
            this.utils.showInfo('Circuit Breaker Overview:');
            console.log();
            // Global circuit breaker status
            const globalStatus = circuitMetrics.globalStatus || 'CLOSED';
            const statusIcon = globalStatus === 'CLOSED' ? '🟢' : globalStatus === 'OPEN' ? '🔴' : '🟡';
            console.log(`${statusIcon} Global Circuit Breaker: ${globalStatus}`);
            console.log();
            // Per-service circuit breaker status
            if (circuitMetrics.perServiceStatus) {
                console.log('🎛️ Per-Service Circuit Breaker Status:');
                Object.entries(circuitMetrics.perServiceStatus).forEach(([service, status]) => {
                    const serviceIcon = status === 'CLOSED' ? '🟢' : status === 'OPEN' ? '🔴' : '🟡';
                    console.log(`  ${serviceIcon} ${service}: ${status}`);
                });
                console.log();
            }
            // Circuit breaker statistics
            console.log('📊 Circuit Breaker Statistics:');
            console.log(`  • Total Trips: ${circuitMetrics.totalTrips || 0}`);
            console.log(`  • Total Recoveries: ${circuitMetrics.totalRecoveries || 0}`);
            console.log(`  • Current Open Circuits: ${circuitMetrics.currentOpenCircuits || 0}`);
            console.log(`  • Average Recovery Time: ${circuitMetrics.averageRecoveryTime || 0}ms`);
            console.log();
            // Recent circuit breaker events
            if (circuitMetrics.recentEvents && circuitMetrics.recentEvents.length > 0) {
                console.log('🕒 Recent Circuit Breaker Events:');
                circuitMetrics.recentEvents.slice(0, 5).forEach((event) => {
                    const eventIcon = event.type === 'OPENED' ? '🔴' : event.type === 'CLOSED' ? '🟢' : '🟡';
                    console.log(`  ${eventIcon} ${event.timestamp}: ${event.service} ${event.type}`);
                });
            }
        }
        catch (error) {
            this.utils.showError('Failed to retrieve circuit breaker status');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async showPerServiceMetrics() {
        this.utils.clearScreen();
        this.utils.showBanner('🎯 Per-Service Metrics');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const serviceMetrics = metrics.perServiceMetrics || {};
            if (Object.keys(serviceMetrics).length === 0) {
                this.utils.showInfo('No per-service metrics available yet.');
                await this.utils.waitForKeyPress();
                return;
            }
            this.utils.showInfo('Service Performance Overview:');
            console.log();
            Object.entries(serviceMetrics).forEach(([service, data]) => {
                console.log(`🔧 ${service.toUpperCase()}:`);
                console.log(`  • Total Requests: ${data.totalRequests || 0}`);
                console.log(`  • Success Rate: ${data.successRate || 0}%`);
                console.log(`  • Average Response Time: ${data.averageResponseTime || 0}ms`);
                console.log(`  • Error Rate: ${data.errorRate || 0}%`);
                console.log(`  • Circuit Breaker Status: ${data.circuitBreakerStatus || 'CLOSED'}`);
                if (data.lastError) {
                    console.log(`  • Last Error: ${data.lastError.timestamp} - ${data.lastError.message}`);
                }
                console.log();
            });
            // Service health summary
            const healthyServices = Object.entries(serviceMetrics).filter(([_, data]) => (data.successRate || 0) >= 95 && data.circuitBreakerStatus === 'CLOSED').length;
            const totalServices = Object.keys(serviceMetrics).length;
            console.log(`📊 Service Health Summary:`);
            console.log(`  • Healthy Services: ${healthyServices}/${totalServices}`);
            if (healthyServices === totalServices) {
                this.utils.showSuccess('✅ All services operating normally');
            }
            else if (healthyServices >= totalServices * 0.8) {
                this.utils.showWarning('⚠️ Some services experiencing issues');
            }
            else {
                this.utils.showError('🚨 Multiple services experiencing issues');
            }
        }
        catch (error) {
            this.utils.showError('Failed to retrieve per-service metrics');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async showPerformanceMetrics() {
        this.utils.clearScreen();
        this.utils.showBanner('⏱️ Performance Metrics');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const performanceMetrics = metrics.performanceMetrics || {};
            this.utils.showInfo('System Performance Overview:');
            console.log();
            console.log(`⚡ Average Operation Time: ${performanceMetrics.averageOperationTime || 0}ms`);
            console.log(`🐌 Slowest Operation: ${performanceMetrics.slowestOperationTime || 0}ms`);
            console.log(`🚀 Fastest Operation: ${performanceMetrics.fastestOperationTime || 0}ms`);
            console.log(`📊 95th Percentile: ${performanceMetrics.p95ResponseTime || 0}ms`);
            console.log(`📊 99th Percentile: ${performanceMetrics.p99ResponseTime || 0}ms`);
            console.log();
            // Performance trends
            if (performanceMetrics.trends) {
                console.log('📈 Performance Trends:');
                console.log(`  • Response time trend: ${performanceMetrics.trends.responseTimeTrend || 'stable'}`);
                console.log(`  • Error rate trend: ${performanceMetrics.trends.errorRateTrend || 'stable'}`);
                console.log(`  • Throughput trend: ${performanceMetrics.trends.throughputTrend || 'stable'}`);
                console.log();
            }
            // Resource utilization
            if (performanceMetrics.resourceUtilization) {
                console.log('💾 Resource Utilization:');
                console.log(`  • Memory usage: ${performanceMetrics.resourceUtilization.memoryUsage || 0}MB`);
                console.log(`  • CPU usage: ${performanceMetrics.resourceUtilization.cpuUsage || 0}%`);
                console.log(`  • Active connections: ${performanceMetrics.resourceUtilization.activeConnections || 0}`);
            }
        }
        catch (error) {
            this.utils.showError('Failed to retrieve performance metrics');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async showRecentErrors() {
        this.utils.clearScreen();
        this.utils.showBanner('🔍 Recent Errors');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const recentErrors = metrics.recentErrors || [];
            if (recentErrors.length === 0) {
                this.utils.showSuccess('✅ No recent errors found!');
                await this.utils.waitForKeyPress();
                return;
            }
            this.utils.showInfo(`Showing last ${Math.min(recentErrors.length, 10)} errors:`);
            console.log();
            recentErrors.slice(0, 10).forEach((error, index) => {
                console.log(`${index + 1}. ${error.timestamp}`);
                console.log(`   Service: ${error.service || 'unknown'}`);
                console.log(`   Operation: ${error.operation || 'unknown'}`);
                console.log(`   Error: ${error.message || 'No message'}`);
                console.log(`   Retry Attempts: ${error.retryAttempts || 0}`);
                if (error.circuitBreakerTripped) {
                    console.log(`   🔴 Circuit Breaker Tripped`);
                }
                console.log();
            });
            // Error pattern analysis
            const errorTypes = recentErrors.reduce((acc, error) => {
                const type = error.type || 'unknown';
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});
            if (Object.keys(errorTypes).length > 0) {
                console.log('📊 Error Type Distribution:');
                Object.entries(errorTypes).forEach(([type, count]) => {
                    console.log(`  • ${type}: ${count} occurrences`);
                });
            }
        }
        catch (error) {
            this.utils.showError('Failed to retrieve recent errors');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
    async exportMetrics() {
        this.utils.clearScreen();
        this.utils.showBanner('📊 Export Metrics');
        try {
            const metrics = await this.errorHandler.getMetrics();
            const { format } = await inquirer_1.default.prompt([
                {
                    type: 'list',
                    name: 'format',
                    message: 'Select export format:',
                    choices: [
                        { name: 'JSON - Machine readable format', value: 'json' },
                        { name: 'CSV - Spreadsheet format', value: 'csv' },
                        { name: 'Text - Human readable format', value: 'text' },
                    ],
                },
            ]);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `error-handling-metrics-${timestamp}.${format}`;
            // In a real implementation, you would write to file
            this.utils.showInfo(`Metrics would be exported to: ${filename}`);
            console.log();
            console.log('Export would include:');
            console.log('• Overall statistics');
            console.log('• Retry metrics');
            console.log('• Circuit breaker status');
            console.log('• Per-service metrics');
            console.log('• Performance data');
            console.log('• Recent error logs');
            this.utils.showSuccess('✅ Metrics export prepared successfully!');
            this.utils.showInfo('💡 In a real implementation, this would save the file to disk.');
        }
        catch (error) {
            this.utils.showError('Failed to export metrics');
            console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        }
        console.log();
        await this.utils.waitForKeyPress();
    }
}
exports.ErrorHandlingMetricsViewer = ErrorHandlingMetricsViewer;
//# sourceMappingURL=ErrorHandlingMetricsViewer.js.map