"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Menu = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
class Menu {
    options;
    theme;
    title;
    constructor(options, theme, title) {
        this.options = options;
        this.theme = theme;
        this.title = title;
    }
    async show() {
        if (this.title) {
            console.log(chalk_1.default.cyan(this.title));
            console.log();
        }
        const choices = this.options
            .filter(option => !option.disabled)
            .map(option => ({
            name: `${option.label}${option.description ? chalk_1.default.gray(` - ${option.description}`) : ''}`,
            value: option.key,
        }));
        const { selectedKey } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'selected<PERSON><PERSON>',
                message: 'Choose an option:',
                choices,
            },
        ]);
        const selectedOption = this.options.find(option => option.key === selectedKey);
        if (selectedOption) {
            await selectedOption.action();
        }
    }
    addOption(option) {
        this.options.push(option);
    }
    removeOption(key) {
        this.options = this.options.filter(option => option.key !== key);
    }
    enableOption(key) {
        const option = this.options.find(option => option.key === key);
        if (option) {
            option.disabled = false;
        }
    }
    disableOption(key) {
        const option = this.options.find(option => option.key === key);
        if (option) {
            option.disabled = true;
        }
    }
}
exports.Menu = Menu;
//# sourceMappingURL=Menu.js.map