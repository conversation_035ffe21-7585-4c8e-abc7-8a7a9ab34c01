"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderSelector = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
const types_1 = require("../../../core/types");
class ProviderSelector extends BaseComponent_1.BaseComponent {
    configManager;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
    }
    async render() {
        // This component is used inline, no standalone render needed
    }
    async selectProvider(excludeConfigured = false) {
        const configuredProviders = this.configManager.getConfiguredProviders();
        let availableProviders = Object.values(types_1.SUPPORTED_PROVIDERS);
        if (excludeConfigured) {
            availableProviders = availableProviders.filter(provider => !configuredProviders.includes(provider.name));
        }
        if (availableProviders.length === 0) {
            this.utils.showWarning('No providers available for selection');
            return null;
        }
        const choices = availableProviders.map(provider => ({
            name: this.formatProviderChoice(provider, configuredProviders.includes(provider.name)),
            value: provider.name,
        }));
        const { selectedProvider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'selectedProvider',
                message: 'Choose an AI provider:',
                choices,
            },
        ]);
        return selectedProvider;
    }
    async selectFromConfigured() {
        const configuredProviders = this.configManager.getConfiguredProviders();
        if (configuredProviders.length === 0) {
            this.utils.showError('No providers are configured');
            return null;
        }
        const choices = configuredProviders.map(providerName => {
            const provider = types_1.SUPPORTED_PROVIDERS[providerName];
            const config = this.configManager.getProviderConfig(providerName);
            const isDefault = providerName === this.configManager.getConfig().defaultProvider;
            return {
                name: `${provider.displayName} (${config.defaultModel})${isDefault ? ' - default' : ''}`,
                value: providerName,
            };
        });
        const { selectedProvider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'selectedProvider',
                message: 'Choose a provider:',
                choices,
            },
        ]);
        return selectedProvider;
    }
    async selectModel(providerName) {
        const provider = types_1.SUPPORTED_PROVIDERS[providerName];
        if (!provider) {
            this.utils.showError(`Unknown provider: ${providerName}`);
            return null;
        }
        const currentConfig = this.configManager.getProviderConfig(providerName);
        const { selectedModel } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'selectedModel',
                message: `Choose a model for ${provider.displayName}:`,
                choices: provider.models,
                default: currentConfig?.defaultModel || provider.models[0],
            },
        ]);
        return selectedModel;
    }
    formatProviderChoice(provider, isConfigured) {
        const status = isConfigured ? '✅' : '⚪';
        const models = provider.models.slice(0, 2).join(', ') +
            (provider.models.length > 2 ? '...' : '');
        return `${status} ${provider.displayName} (${models})`;
    }
    async confirmProviderSwitch(fromProvider, toProvider) {
        const fromDisplay = types_1.SUPPORTED_PROVIDERS[fromProvider]?.displayName || fromProvider;
        const toDisplay = types_1.SUPPORTED_PROVIDERS[toProvider]?.displayName || toProvider;
        return await this.utils.confirmAction(`Switch from ${fromDisplay} to ${toDisplay}?`);
    }
}
exports.ProviderSelector = ProviderSelector;
//# sourceMappingURL=ProviderSelector.js.map