"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderFactory = void 0;
const OpenAIProvider_1 = require("./OpenAIProvider");
const AnthropicProvider_1 = require("./AnthropicProvider");
const GoogleProvider_1 = require("./GoogleProvider");
const DeepseekProvider_1 = require("./DeepseekProvider");
const types_1 = require("../types");
class ProviderFactory {
    static providers = new Map();
    static createProvider(providerName, config) {
        if (!types_1.SUPPORTED_PROVIDERS[providerName]) {
            throw new Error(`Unsupported provider: ${providerName}`);
        }
        // Check if provider instance already exists and config hasn't changed
        const existingProvider = this.providers.get(providerName);
        if (existingProvider && this.configMatches(existingProvider.getConfig(), config)) {
            return existingProvider;
        }
        let provider;
        switch (providerName) {
            case 'openai':
                provider = new OpenAIProvider_1.OpenAIProvider(config);
                break;
            case 'anthropic':
                provider = new AnthropicProvider_1.AnthropicProvider(config);
                break;
            case 'google':
                provider = new GoogleProvider_1.GoogleProvider(config);
                break;
            case 'deepseek':
                provider = new DeepseekProvider_1.DeepseekProvider(config);
                break;
            default:
                throw new Error(`Provider implementation not found: ${providerName}`);
        }
        this.providers.set(providerName, provider);
        return provider;
    }
    static getProvider(providerName) {
        return this.providers.get(providerName) || null;
    }
    static hasProvider(providerName) {
        return this.providers.has(providerName);
    }
    static removeProvider(providerName) {
        this.providers.delete(providerName);
    }
    static clearProviders() {
        this.providers.clear();
    }
    static getLoadedProviders() {
        return Array.from(this.providers.keys());
    }
    static async testProvider(providerName, config) {
        try {
            const provider = this.createProvider(providerName, config);
            return await provider.testConnection();
        }
        catch (error) {
            console.error(`Failed to test provider ${providerName}:`, error);
            return false;
        }
    }
    static validateProviderConfig(providerName, config) {
        const errors = [];
        const providerInfo = types_1.SUPPORTED_PROVIDERS[providerName];
        if (!providerInfo) {
            errors.push(`Unknown provider: ${providerName}`);
            return errors;
        }
        if (!config.apiKey) {
            errors.push('API key is required');
        }
        else {
            // Provider-specific API key validation
            switch (providerName) {
                case 'openai':
                    if (!config.apiKey.startsWith('sk-')) {
                        errors.push('OpenAI API key should start with "sk-"');
                    }
                    break;
                case 'anthropic':
                    if (!config.apiKey.startsWith('sk-ant-')) {
                        errors.push('Anthropic API key should start with "sk-ant-"');
                    }
                    break;
                case 'google':
                    if (config.apiKey.length < 30) {
                        errors.push('Google API key appears to be invalid (too short)');
                    }
                    break;
                case 'deepseek':
                    // Deepseek doesn't have a specific format requirement
                    break;
            }
        }
        if (!config.defaultModel) {
            errors.push('Default model is required');
        }
        else if (!providerInfo.models.includes(config.defaultModel)) {
            errors.push(`Model "${config.defaultModel}" is not supported by ${providerInfo.displayName}`);
        }
        if (config.baseUrl && !this.isValidUrl(config.baseUrl)) {
            errors.push('Base URL must be a valid URL');
        }
        if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 100000)) {
            errors.push('Max tokens must be between 1 and 100000');
        }
        if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
            errors.push('Temperature must be between 0 and 2');
        }
        return errors;
    }
    static getProviderCapabilities(providerName) {
        const providerInfo = types_1.SUPPORTED_PROVIDERS[providerName];
        if (!providerInfo) {
            throw new Error(`Unknown provider: ${providerName}`);
        }
        const capabilities = {
            supportsStreaming: true, // All providers support streaming
            supportsTools: true, // All providers support tools
            supportsVision: false,
            maxTokens: 4000,
            supportedModels: providerInfo.models,
        };
        switch (providerName) {
            case 'openai':
                capabilities.supportsVision = true;
                capabilities.maxTokens = 128000; // GPT-4 Turbo
                break;
            case 'anthropic':
                capabilities.supportsVision = true;
                capabilities.maxTokens = 200000; // Claude-3
                break;
            case 'google':
                capabilities.supportsVision = providerInfo.models.includes('gemini-pro-vision');
                capabilities.maxTokens = 32000; // Gemini Pro
                break;
            case 'deepseek':
                capabilities.maxTokens = 32000; // Deepseek models
                break;
        }
        return capabilities;
    }
    static configMatches(config1, config2) {
        return (config1.apiKey === config2.apiKey &&
            config1.baseUrl === config2.baseUrl &&
            config1.defaultModel === config2.defaultModel &&
            config1.maxTokens === config2.maxTokens &&
            config1.temperature === config2.temperature &&
            config1.systemPrompt === config2.systemPrompt);
    }
    static isValidUrl(url) {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    }
    static getSupportedProviders() {
        return Object.keys(types_1.SUPPORTED_PROVIDERS);
    }
    static getProviderInfo(providerName) {
        return types_1.SUPPORTED_PROVIDERS[providerName];
    }
    static async getAvailableModels(providerName, config) {
        try {
            const provider = this.createProvider(providerName, config);
            // Check if provider has getAvailableModels method
            if ('getAvailableModels' in provider && typeof provider.getAvailableModels === 'function') {
                return await provider.getAvailableModels();
            }
            // Fallback to static model list
            return types_1.SUPPORTED_PROVIDERS[providerName].models;
        }
        catch (error) {
            console.error(`Failed to get models for ${providerName}:`, error);
            return types_1.SUPPORTED_PROVIDERS[providerName].models;
        }
    }
}
exports.ProviderFactory = ProviderFactory;
//# sourceMappingURL=ProviderFactory.js.map