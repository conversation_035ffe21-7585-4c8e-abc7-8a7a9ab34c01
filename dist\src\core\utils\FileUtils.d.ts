export interface FileInfo {
    path: string;
    name: string;
    size: number;
    isDirectory: boolean;
    isFile: boolean;
    extension: string;
    mimeType?: string;
    createdAt: Date;
    modifiedAt: Date;
    permissions: string;
}
export interface DirectoryListing {
    path: string;
    files: FileInfo[];
    directories: FileInfo[];
    totalSize: number;
    totalFiles: number;
    totalDirectories: number;
}
export declare class FileUtils {
    private static readonly MAX_FILE_SIZE;
    private static readonly SAFE_EXTENSIONS;
    static exists(filePath: string): Promise<boolean>;
    static getFileInfo(filePath: string): Promise<FileInfo>;
    static listDirectory(dirPath: string, options?: {
        recursive?: boolean;
        includeHidden?: boolean;
        sortBy?: 'name' | 'size' | 'modified';
        sortOrder?: 'asc' | 'desc';
    }): Promise<DirectoryListing>;
    static readFile(filePath: string, options?: {
        encoding?: BufferEncoding;
        maxSize?: number;
        startLine?: number;
        endLine?: number;
    }): Promise<string>;
    static writeFile(filePath: string, content: string, options?: {
        encoding?: BufferEncoding;
        createBackup?: boolean;
        ensureDir?: boolean;
        mode?: number;
    }): Promise<void>;
    static appendFile(filePath: string, content: string, options?: {
        encoding?: BufferEncoding;
        ensureDir?: boolean;
    }): Promise<void>;
    static copyFile(sourcePath: string, destPath: string, options?: {
        overwrite?: boolean;
        preserveTimestamps?: boolean;
        ensureDir?: boolean;
    }): Promise<void>;
    static moveFile(sourcePath: string, destPath: string, options?: {
        overwrite?: boolean;
        ensureDir?: boolean;
    }): Promise<void>;
    static deleteFile(filePath: string): Promise<void>;
    static createDirectory(dirPath: string, options?: {
        recursive?: boolean;
        mode?: number;
    }): Promise<void>;
    static getFileHash(filePath: string, algorithm?: string): Promise<string>;
    static findFiles(searchPath: string, pattern: string | RegExp, options?: {
        recursive?: boolean;
        includeDirectories?: boolean;
        maxDepth?: number;
    }): Promise<string[]>;
    static formatFileSize(bytes: number): string;
    static isTextFile(filePath: string): boolean;
    static sanitizePath(inputPath: string): string;
    static getRelativePath(from: string, to: string): string;
    static joinPath(...segments: string[]): string;
    static resolvePath(...segments: string[]): string;
    private static getPermissionsString;
    private static getSortFunction;
}
//# sourceMappingURL=FileUtils.d.ts.map