"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLI_AGENT_SYSTEM_PROMPT = exports.DEFAULT_SYSTEM_PROMPTS = exports.SystemPromptRegistry = void 0;
var SystemPromptRegistry_1 = require("./SystemPromptRegistry");
Object.defineProperty(exports, "SystemPromptRegistry", { enumerable: true, get: function () { return SystemPromptRegistry_1.SystemPromptRegistry; } });
var DefaultPrompts_1 = require("./DefaultPrompts");
Object.defineProperty(exports, "DEFAULT_SYSTEM_PROMPTS", { enumerable: true, get: function () { return DefaultPrompts_1.DEFAULT_SYSTEM_PROMPTS; } });
Object.defineProperty(exports, "CLI_AGENT_SYSTEM_PROMPT", { enumerable: true, get: function () { return DefaultPrompts_1.CLI_AGENT_SYSTEM_PROMPT; } });
//# sourceMappingURL=index.js.map