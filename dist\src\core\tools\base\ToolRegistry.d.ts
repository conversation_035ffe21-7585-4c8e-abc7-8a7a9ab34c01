import { BaseTool, ToolMetadata, ToolValidationResult } from './BaseTool';
import { ToolResult } from '../../types';
export interface ToolExecutionContext {
    userId?: string;
    sessionId?: string;
    workingDirectory?: string;
    environment?: Record<string, string>;
    permissions?: string[];
}
export interface ToolExecutionResult extends ToolResult {
    toolName: string;
    executionTime: number;
    context: ToolExecutionContext;
}
export interface ToolFilter {
    category?: string;
    tags?: string[];
    dangerous?: boolean;
    requiresConfirmation?: boolean;
    enabled?: boolean;
}
export declare class ToolRegistry {
    private tools;
    private enabledTools;
    private disabledTools;
    private toolExecutionHistory;
    private maxHistorySize;
    registerTool(tool: BaseTool): void;
    unregisterTool(toolName: string): boolean;
    getTool(toolName: string): BaseTool | null;
    getAllTools(): BaseTool[];
    getEnabledTools(): BaseTool[];
    getToolsByCategory(category: string): BaseTool[];
    getToolsByTag(tag: string): BaseTool[];
    filterTools(filter: ToolFilter): BaseTool[];
    enableTool(toolName: string): boolean;
    disableTool(toolName: string): boolean;
    isToolEnabled(toolName: string): boolean;
    isToolRegistered(toolName: string): boolean;
    validateToolExecution(toolName: string, parameters: Record<string, any>): Promise<ToolValidationResult>;
    executeTool(toolName: string, parameters: Record<string, any>, context?: ToolExecutionContext): Promise<ToolExecutionResult>;
    shouldConfirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
    getExecutionHistory(limit?: number): ToolExecutionResult[];
    clearExecutionHistory(): void;
    getToolStatistics(): Record<string, {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageExecutionTime: number;
        lastExecuted?: Date;
    }>;
    exportToolDefinitions(): Array<{
        name: string;
        description: string;
        parameters: Record<string, any>;
        metadata: ToolMetadata;
    }>;
    getToolCount(): number;
    getEnabledToolCount(): number;
    private addToHistory;
    setMaxHistorySize(size: number): void;
    clear(): void;
}
//# sourceMappingURL=ToolRegistry.d.ts.map