"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
class ToolRegistry {
    tools = new Map();
    enabledTools = new Set();
    disabledTools = new Set();
    toolExecutionHistory = [];
    maxHistorySize = 1000;
    registerTool(tool) {
        if (this.tools.has(tool.name)) {
            throw new Error(`Tool with name '${tool.name}' is already registered`);
        }
        this.tools.set(tool.name, tool);
        this.enabledTools.add(tool.name);
    }
    unregisterTool(toolName) {
        const removed = this.tools.delete(toolName);
        this.enabledTools.delete(toolName);
        this.disabledTools.delete(toolName);
        return removed;
    }
    getTool(toolName) {
        return this.tools.get(toolName) || null;
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getEnabledTools() {
        return Array.from(this.tools.values()).filter(tool => this.enabledTools.has(tool.name) && !this.disabledTools.has(tool.name));
    }
    getToolsByCategory(category) {
        return this.getAllTools().filter(tool => tool.getCategory() === category);
    }
    getToolsByTag(tag) {
        return this.getAllTools().filter(tool => tool.getTags().includes(tag));
    }
    filterTools(filter) {
        return this.getAllTools().filter(tool => {
            if (filter.category && tool.getCategory() !== filter.category) {
                return false;
            }
            if (filter.tags && !filter.tags.some(tag => tool.getTags().includes(tag))) {
                return false;
            }
            if (filter.dangerous !== undefined && tool.isDangerous() !== filter.dangerous) {
                return false;
            }
            if (filter.requiresConfirmation !== undefined && tool.requiresConfirmation !== filter.requiresConfirmation) {
                return false;
            }
            if (filter.enabled !== undefined) {
                const isEnabled = this.isToolEnabled(tool.name);
                if (isEnabled !== filter.enabled) {
                    return false;
                }
            }
            return true;
        });
    }
    enableTool(toolName) {
        if (!this.tools.has(toolName)) {
            return false;
        }
        this.enabledTools.add(toolName);
        this.disabledTools.delete(toolName);
        return true;
    }
    disableTool(toolName) {
        if (!this.tools.has(toolName)) {
            return false;
        }
        this.disabledTools.add(toolName);
        this.enabledTools.delete(toolName);
        return true;
    }
    isToolEnabled(toolName) {
        return this.enabledTools.has(toolName) && !this.disabledTools.has(toolName);
    }
    isToolRegistered(toolName) {
        return this.tools.has(toolName);
    }
    async validateToolExecution(toolName, parameters) {
        const tool = this.getTool(toolName);
        if (!tool) {
            return {
                valid: false,
                errors: [`Tool '${toolName}' is not registered`],
                warnings: [],
            };
        }
        if (!this.isToolEnabled(toolName)) {
            return {
                valid: false,
                errors: [`Tool '${toolName}' is disabled`],
                warnings: [],
            };
        }
        return await tool.validate(parameters);
    }
    async executeTool(toolName, parameters, context = {}) {
        const startTime = Date.now();
        try {
            // Validate tool execution
            const validation = await this.validateToolExecution(toolName, parameters);
            if (!validation.valid) {
                const result = {
                    success: false,
                    content: '',
                    error: `Validation failed: ${validation.errors.join(', ')}`,
                    toolName,
                    executionTime: Date.now() - startTime,
                    context,
                };
                this.addToHistory(result);
                return result;
            }
            const tool = this.getTool(toolName);
            // Execute the tool
            const toolResult = await tool.execute(parameters);
            const result = {
                ...toolResult,
                toolName,
                executionTime: Date.now() - startTime,
                context,
            };
            this.addToHistory(result);
            return result;
        }
        catch (error) {
            const result = {
                success: false,
                content: '',
                error: `Execution failed: ${error instanceof Error ? error.message : String(error)}`,
                toolName,
                executionTime: Date.now() - startTime,
                context,
            };
            this.addToHistory(result);
            return result;
        }
    }
    async shouldConfirmExecution(toolName, parameters) {
        const tool = this.getTool(toolName);
        if (!tool) {
            return true; // Confirm unknown tools
        }
        return await tool.shouldConfirmExecute(parameters);
    }
    getExecutionHistory(limit) {
        const history = [...this.toolExecutionHistory];
        return limit ? history.slice(-limit) : history;
    }
    clearExecutionHistory() {
        this.toolExecutionHistory = [];
    }
    getToolStatistics() {
        const stats = {};
        for (const tool of this.getAllTools()) {
            const toolHistory = this.toolExecutionHistory.filter(h => h.toolName === tool.name);
            if (toolHistory.length === 0) {
                stats[tool.name] = {
                    totalExecutions: 0,
                    successfulExecutions: 0,
                    failedExecutions: 0,
                    averageExecutionTime: 0,
                };
                continue;
            }
            const successful = toolHistory.filter(h => h.success);
            const failed = toolHistory.filter(h => !h.success);
            const totalTime = toolHistory.reduce((sum, h) => sum + h.executionTime, 0);
            const lastExecution = toolHistory[toolHistory.length - 1];
            stats[tool.name] = {
                totalExecutions: toolHistory.length,
                successfulExecutions: successful.length,
                failedExecutions: failed.length,
                averageExecutionTime: totalTime / toolHistory.length,
                lastExecuted: new Date(Date.now() - lastExecution.executionTime),
            };
        }
        return stats;
    }
    exportToolDefinitions() {
        return this.getEnabledTools().map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters,
            metadata: tool.getMetadata(),
        }));
    }
    getToolCount() {
        return this.tools.size;
    }
    getEnabledToolCount() {
        return this.getEnabledTools().length;
    }
    addToHistory(result) {
        this.toolExecutionHistory.push(result);
        // Maintain history size limit
        if (this.toolExecutionHistory.length > this.maxHistorySize) {
            this.toolExecutionHistory = this.toolExecutionHistory.slice(-this.maxHistorySize);
        }
    }
    setMaxHistorySize(size) {
        this.maxHistorySize = Math.max(0, size);
        // Trim current history if needed
        if (this.toolExecutionHistory.length > this.maxHistorySize) {
            this.toolExecutionHistory = this.toolExecutionHistory.slice(-this.maxHistorySize);
        }
    }
    clear() {
        this.tools.clear();
        this.enabledTools.clear();
        this.disabledTools.clear();
        this.toolExecutionHistory = [];
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=ToolRegistry.js.map