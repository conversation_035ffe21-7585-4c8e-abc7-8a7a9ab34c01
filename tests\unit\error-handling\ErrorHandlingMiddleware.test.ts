import { ErrorHandlingMiddleware } from '../../../src/core/error-handling/ErrorHandlingMiddleware';
import { createMockErrorHandlingConfig } from '../../utils/mocks';
import { TestError, NetworkError, TimeoutError, RateLimitError } from '../../utils/mocks';

describe('ErrorHandlingMiddleware', () => {
  let middleware: ErrorHandlingMiddleware;
  let mockConfig: any;

  beforeEach(() => {
    mockConfig = createMockErrorHandlingConfig();
    middleware = new ErrorHandlingMiddleware(mockConfig);
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('constructor', () => {
    it('should create middleware with configuration', () => {
      expect(middleware).toBeInstanceOf(ErrorHandlingMiddleware);
    });

    it('should initialize with default configuration when none provided', () => {
      const defaultMiddleware = new ErrorHandlingMiddleware();
      expect(defaultMiddleware).toBeInstanceOf(ErrorHandlingMiddleware);
    });
  });

  describe('executeWithRetry', () => {
    it('should execute operation successfully on first attempt', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await middleware.executeWithRetry(operation, 'test-operation');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockRejectedValueOnce(new TimeoutError('Request timed out'))
        .mockResolvedValue('success');
      
      const result = await middleware.executeWithRetry(operation, 'test-operation');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      const operation = jest.fn().mockRejectedValue(new TestError('Auth error', 'AUTH_ERROR', false));
      
      await expect(middleware.executeWithRetry(operation, 'test-operation'))
        .rejects.toThrow('Auth error');
      
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should respect max attempts limit', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      await expect(middleware.executeWithRetry(operation, 'test-operation'))
        .rejects.toThrow('Connection failed');
      
      expect(operation).toHaveBeenCalledTimes(3); // maxAttempts from config
    });

    it('should apply retry delays', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      const promise = middleware.executeWithRetry(operation, 'test-operation');
      
      // First call should happen immediately
      expect(operation).toHaveBeenCalledTimes(1);
      
      // Advance time to trigger retry
      jest.advanceTimersByTime(1000);
      await testUtils.flushPromises();
      
      const result = await promise;
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should use operation-specific policies when available', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      await expect(middleware.executeWithRetry(operation, 'toolExecution'))
        .rejects.toThrow('Connection failed');
      
      // Should use toolExecution policy (maxAttempts: 2)
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should handle timeout for individual attempts', async () => {
      const operation = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 35000)) // Longer than attemptTimeout
      );
      
      await expect(middleware.executeWithRetry(operation, 'test-operation'))
        .rejects.toThrow();
      
      expect(operation).toHaveBeenCalledTimes(1);
    });
  });

  describe('executeWithCircuitBreaker', () => {
    it('should execute operation when circuit is closed', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await middleware.executeWithCircuitBreaker(operation, 'test-service');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should block execution when circuit is open', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      // Open the circuit by causing failures
      for (let i = 0; i < 5; i++) {
        try {
          await middleware.executeWithCircuitBreaker(
            () => Promise.reject(new NetworkError('Connection failed')),
            'test-service'
          );
        } catch (e) {
          // Expected failures
        }
      }
      
      // Now the circuit should be open
      await expect(middleware.executeWithCircuitBreaker(operation, 'test-service'))
        .rejects.toThrow('Circuit breaker is OPEN');
      
      expect(operation).not.toHaveBeenCalled();
    });

    it('should record successes and failures', async () => {
      const successOperation = jest.fn().mockResolvedValue('success');
      const failureOperation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      await middleware.executeWithCircuitBreaker(successOperation, 'test-service');
      
      try {
        await middleware.executeWithCircuitBreaker(failureOperation, 'test-service');
      } catch (e) {
        // Expected failure
      }
      
      const metrics = await middleware.getMetrics();
      expect(metrics.circuitBreakerMetrics.perServiceStatus['test-service']).toBe('CLOSED');
    });

    it('should allow execution when circuit breaker is disabled', async () => {
      const disabledConfig = {
        ...mockConfig,
        circuitBreaker: { ...mockConfig.circuitBreaker, enabled: false }
      };
      const disabledMiddleware = new ErrorHandlingMiddleware(disabledConfig);
      
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await disabledMiddleware.executeWithCircuitBreaker(operation, 'test-service');
      
      expect(result).toBe('success');
    });
  });

  describe('execute (combined retry and circuit breaker)', () => {
    it('should combine retry and circuit breaker functionality', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      const result = await middleware.execute(operation, 'test-operation', 'test-service');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should respect circuit breaker state during retries', async () => {
      const operation = jest.fn().mockRejectedValue(new NetworkError('Connection failed'));
      
      // This should fail and eventually open the circuit
      await expect(middleware.execute(operation, 'test-operation', 'test-service'))
        .rejects.toThrow();
      
      // Subsequent calls should be blocked by circuit breaker
      const blockedOperation = jest.fn().mockResolvedValue('success');
      await expect(middleware.execute(blockedOperation, 'test-operation', 'test-service'))
        .rejects.toThrow('Circuit breaker is OPEN');
      
      expect(blockedOperation).not.toHaveBeenCalled();
    });
  });

  describe('configuration updates', () => {
    it('should update configuration dynamically', () => {
      const newConfig = {
        ...mockConfig,
        retry: { ...mockConfig.retry, maxAttempts: 5 }
      };
      
      middleware.updateConfig(newConfig);
      
      // Configuration should be updated (we can't easily test this without exposing internals)
      expect(() => middleware.updateConfig(newConfig)).not.toThrow();
    });
  });

  describe('metrics collection', () => {
    it('should collect comprehensive metrics', async () => {
      const successOperation = jest.fn().mockResolvedValue('success');
      const retryOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('Connection failed'))
        .mockResolvedValue('success');
      
      await middleware.execute(successOperation, 'test-operation', 'test-service');
      await middleware.execute(retryOperation, 'test-operation', 'test-service');
      
      const metrics = await middleware.getMetrics();
      
      expect(metrics.totalOperations).toBe(2);
      expect(metrics.successfulOperations).toBe(2);
      expect(metrics.operationsWithRetries).toBe(1);
      expect(metrics.retryMetrics.totalRetryAttempts).toBe(1);
    });

    it('should track per-service metrics', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      await middleware.execute(operation, 'test-operation', 'service-1');
      await middleware.execute(operation, 'test-operation', 'service-2');
      
      const metrics = await middleware.getMetrics();
      
      expect(metrics.perServiceMetrics['service-1']).toBeDefined();
      expect(metrics.perServiceMetrics['service-2']).toBeDefined();
      expect(metrics.perServiceMetrics['service-1'].totalRequests).toBe(1);
      expect(metrics.perServiceMetrics['service-2'].totalRequests).toBe(1);
    });

    it('should track recent errors', async () => {
      const operation = jest.fn().mockRejectedValue(new TestError('Test error', 'TEST_ERROR', false));
      
      try {
        await middleware.execute(operation, 'test-operation', 'test-service');
      } catch (e) {
        // Expected failure
      }
      
      const metrics = await middleware.getMetrics();
      
      expect(metrics.recentErrors).toHaveLength(1);
      expect(metrics.recentErrors[0].message).toBe('Test error');
      expect(metrics.recentErrors[0].service).toBe('test-service');
      expect(metrics.recentErrors[0].operation).toBe('test-operation');
    });
  });

  describe('error cases', () => {
    it('should handle operations that throw non-Error objects', async () => {
      const operation = jest.fn().mockRejectedValue('string error');
      
      await expect(middleware.execute(operation, 'test-operation', 'test-service'))
        .rejects.toBe('string error');
    });

    it('should handle operations that return undefined', async () => {
      const operation = jest.fn().mockResolvedValue(undefined);
      
      const result = await middleware.execute(operation, 'test-operation', 'test-service');
      
      expect(result).toBeUndefined();
    });

    it('should handle very long operation names gracefully', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      const longName = 'a'.repeat(1000);
      
      const result = await middleware.execute(operation, longName, 'test-service');
      
      expect(result).toBe('success');
    });
  });
});
