"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseComponent = void 0;
const CLIUtils_1 = require("../../utils/CLIUtils");
class BaseComponent {
    state;
    config;
    utils;
    constructor(state, config) {
        this.state = state;
        this.config = config;
        this.utils = new CLIUtils_1.CLIUtils(config.theme);
    }
    async handleInput(input) {
        // Default implementation - can be overridden
        console.log(`Received input: ${input}`);
    }
    async cleanup() {
        // Default cleanup - can be overridden
    }
    updateState(updates) {
        Object.assign(this.state, updates);
    }
    async showError(message) {
        this.utils.showError(message);
        await this.utils.waitForKeyPress();
    }
    async showSuccess(message) {
        this.utils.showSuccess(message);
        await this.utils.waitForKeyPress();
    }
    async confirmAction(message) {
        return await this.utils.confirmAction(message);
    }
}
exports.BaseComponent = BaseComponent;
//# sourceMappingURL=BaseComponent.js.map