import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig } from '../../types';

export class CircuitBreakerConfigMenu extends BaseComponent {
  private configManager: ConfigManager;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('⚡ Circuit Breaker Configuration', 'Configure circuit breaker thresholds and recovery settings');

    await this.showCircuitBreakerMenu();
  }

  private async showCircuitBreakerMenu(): Promise<void> {
    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    // Display current configuration
    this.utils.showInfo('Current Circuit Breaker Settings:');
    console.log(`  Enabled: ${currentConfig.enabled ? 'Yes' : 'No'}`);
    console.log(`  Failure Threshold: ${currentConfig.failureThreshold || 5}`);
    console.log(`  Recovery Timeout: ${currentConfig.recoveryTimeout || 30000}ms`);
    console.log(`  Success Threshold: ${currentConfig.successThreshold || 2}`);
    console.log(`  Monitoring Window: ${currentConfig.monitoringWindow || 60000}ms`);
    console.log();

    const choices = [
      { name: '🔧 Basic Configuration', value: 'basic' },
      { name: '⏱️ Timing Settings', value: 'timing' },
      { name: '📊 Threshold Configuration', value: 'thresholds' },
      { name: '🎛️ Per-Service Settings', value: 'per_service' },
      { name: '🧪 Test Circuit Breaker', value: 'test' },
      { name: '📋 View Configuration Details', value: 'details' },
      { name: '🔄 Reset to Defaults', value: 'reset' },
      { name: '🔙 Back to Error Handling Config', value: 'back' },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices,
      },
    ]);

    switch (action) {
      case 'basic':
        await this.configureBasicSettings();
        break;
      case 'timing':
        await this.configureTimingSettings();
        break;
      case 'thresholds':
        await this.configureThresholds();
        break;
      case 'per_service':
        await this.configurePerServiceSettings();
        break;
      case 'test':
        await this.testCircuitBreaker();
        break;
      case 'details':
        await this.showConfigurationDetails();
        break;
      case 'reset':
        await this.resetCircuitBreakerConfig();
        break;
      case 'back':
        return;
    }

    await this.showCircuitBreakerMenu();
  }

  private async configureBasicSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🔧 Basic Circuit Breaker Settings');

    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'enabled',
        message: 'Enable circuit breaker protection?',
        default: currentConfig.enabled ?? true,
      },
      {
        type: 'confirm',
        name: 'enablePerServiceBreakers',
        message: 'Enable per-service circuit breakers?',
        default: currentConfig.enablePerServiceBreakers ?? true,
      },
      {
        type: 'confirm',
        name: 'enableGlobalBreaker',
        message: 'Enable global circuit breaker?',
        default: currentConfig.enableGlobalBreaker ?? false,
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        circuitBreaker: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Basic circuit breaker settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update settings: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureTimingSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('⏱️ Circuit Breaker Timing Settings');

    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    this.utils.showInfo('Timing settings control how the circuit breaker responds to failures:');
    console.log('• Recovery Timeout: How long to wait before attempting recovery');
    console.log('• Monitoring Window: Time window for tracking failures');
    console.log('• Half-Open Timeout: Maximum time to stay in half-open state');
    console.log();

    const answers = await inquirer.prompt([
      {
        type: 'number',
        name: 'recoveryTimeout',
        message: 'Recovery timeout (ms) - time before attempting recovery:',
        default: currentConfig.recoveryTimeout || 30000,
        validate: (value) => {
          if (value < 1000 || value > 300000) {
            return 'Must be between 1000ms (1s) and 300000ms (5min)';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'monitoringWindow',
        message: 'Monitoring window (ms) - time window for failure tracking:',
        default: currentConfig.monitoringWindow || 60000,
        validate: (value) => {
          if (value < 5000 || value > 600000) {
            return 'Must be between 5000ms (5s) and 600000ms (10min)';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'halfOpenTimeout',
        message: 'Half-open timeout (ms) - max time in half-open state:',
        default: currentConfig.halfOpenTimeout || 10000,
        validate: (value) => {
          if (value < 1000 || value > 60000) {
            return 'Must be between 1000ms (1s) and 60000ms (1min)';
          }
          return true;
        },
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        circuitBreaker: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Timing settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update timing: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configureThresholds(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📊 Circuit Breaker Threshold Configuration');

    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    this.utils.showInfo('Threshold settings determine when the circuit breaker activates:');
    console.log('• Failure Threshold: Number of failures to open the circuit');
    console.log('• Success Threshold: Number of successes to close the circuit');
    console.log('• Failure Rate Threshold: Percentage of failures to open circuit');
    console.log();

    const answers = await inquirer.prompt([
      {
        type: 'number',
        name: 'failureThreshold',
        message: 'Failure threshold - failures needed to open circuit:',
        default: currentConfig.failureThreshold || 5,
        validate: (value) => {
          if (value < 1 || value > 50) {
            return 'Must be between 1 and 50';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'successThreshold',
        message: 'Success threshold - successes needed to close circuit:',
        default: currentConfig.successThreshold || 2,
        validate: (value) => {
          if (value < 1 || value > 10) {
            return 'Must be between 1 and 10';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'failureRateThreshold',
        message: 'Failure rate threshold (%) - percentage to open circuit:',
        default: currentConfig.failureRateThreshold || 50,
        validate: (value) => {
          if (value < 10 || value > 100) {
            return 'Must be between 10% and 100%';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'minimumRequests',
        message: 'Minimum requests - minimum calls before rate calculation:',
        default: currentConfig.minimumRequests || 10,
        validate: (value) => {
          if (value < 1 || value > 100) {
            return 'Must be between 1 and 100';
          }
          return true;
        },
      },
    ]);

    // Validate that successThreshold <= failureThreshold
    if (answers.successThreshold > answers.failureThreshold) {
      this.utils.showWarning('Success threshold should typically be less than or equal to failure threshold');
      const proceed = await this.confirmAction('Continue with these settings?');
      if (!proceed) {
        return;
      }
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        circuitBreaker: {
          ...currentConfig,
          ...answers,
        },
      });
      this.utils.showSuccess('Threshold settings updated successfully!');
    } catch (error) {
      this.utils.showError(`Failed to update thresholds: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }

  private async configurePerServiceSettings(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🎛️ Per-Service Circuit Breaker Settings');

    this.utils.showInfo('Configure different circuit breaker settings for different services:');
    console.log();

    const services = ['openai', 'anthropic', 'google', 'azure', 'local'];
    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker?.perServiceSettings || {};

    const { selectedService } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedService',
        message: 'Select service to configure:',
        choices: [
          ...services.map(service => ({ name: `${service} ${currentConfig[service] ? '(configured)' : '(default)'}`, value: service })),
          { name: '🔙 Back to Circuit Breaker Menu', value: 'back' },
        ],
      },
    ]);

    if (selectedService === 'back') {
      return;
    }

    const serviceConfig = currentConfig[selectedService] || {};

    const answers = await inquirer.prompt([
      {
        type: 'number',
        name: 'failureThreshold',
        message: `Failure threshold for ${selectedService}:`,
        default: serviceConfig.failureThreshold || 5,
        validate: (value) => value >= 1 && value <= 50 || 'Must be between 1 and 50',
      },
      {
        type: 'number',
        name: 'recoveryTimeout',
        message: `Recovery timeout (ms) for ${selectedService}:`,
        default: serviceConfig.recoveryTimeout || 30000,
        validate: (value) => value >= 1000 && value <= 300000 || 'Must be between 1000ms and 300000ms',
      },
      {
        type: 'number',
        name: 'successThreshold',
        message: `Success threshold for ${selectedService}:`,
        default: serviceConfig.successThreshold || 2,
        validate: (value) => value >= 1 && value <= 10 || 'Must be between 1 and 10',
      },
    ]);

    try {
      await this.configManager.updateErrorHandlingConfig({
        circuitBreaker: {
          ...this.configManager.getConfig().errorHandling?.circuitBreaker,
          perServiceSettings: {
            ...currentConfig,
            [selectedService]: answers,
          },
        },
      });
      this.utils.showSuccess(`${selectedService} circuit breaker settings updated successfully!`);
    } catch (error) {
      this.utils.showError(`Failed to update ${selectedService} settings: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
    await this.configurePerServiceSettings(); // Return to service selection
  }

  private async testCircuitBreaker(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🧪 Test Circuit Breaker Behavior');

    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    this.utils.showInfo('This simulates circuit breaker state transitions:');
    console.log();

    const failureThreshold = currentConfig.failureThreshold || 5;
    const successThreshold = currentConfig.successThreshold || 2;
    const recoveryTimeout = currentConfig.recoveryTimeout || 30000;

    console.log('Circuit Breaker State Simulation:');
    console.log();
    console.log('🟢 CLOSED State (Normal Operation):');
    console.log(`  • Allows all requests through`);
    console.log(`  • Tracks failures (threshold: ${failureThreshold})`);
    console.log(`  • Opens after ${failureThreshold} consecutive failures`);
    console.log();
    console.log('🔴 OPEN State (Circuit Tripped):');
    console.log(`  • Blocks all requests immediately`);
    console.log(`  • Returns cached error or fast-fail response`);
    console.log(`  • Waits ${recoveryTimeout}ms before attempting recovery`);
    console.log();
    console.log('🟡 HALF-OPEN State (Testing Recovery):');
    console.log(`  • Allows limited requests through`);
    console.log(`  • Closes after ${successThreshold} consecutive successes`);
    console.log(`  • Opens again on any failure`);
    console.log();

    const { simulateScenario } = await inquirer.prompt([
      {
        type: 'list',
        name: 'simulateScenario',
        message: 'Select scenario to simulate:',
        choices: [
          { name: 'Normal operation (all requests succeed)', value: 'normal' },
          { name: 'Service degradation (some failures)', value: 'degraded' },
          { name: 'Service outage (all requests fail)', value: 'outage' },
          { name: 'Service recovery (failures then successes)', value: 'recovery' },
        ],
      },
    ]);

    console.log();
    this.simulateScenario(simulateScenario, currentConfig);

    await this.utils.waitForKeyPress();
  }

  private simulateScenario(scenario: string, config: any): void {
    const failureThreshold = config.failureThreshold || 5;
    const successThreshold = config.successThreshold || 2;

    console.log(`Simulating: ${scenario}`);
    console.log('─'.repeat(50));

    switch (scenario) {
      case 'normal':
        console.log('Request 1: ✅ Success (Circuit: CLOSED)');
        console.log('Request 2: ✅ Success (Circuit: CLOSED)');
        console.log('Request 3: ✅ Success (Circuit: CLOSED)');
        console.log('→ Circuit remains CLOSED, all requests processed normally');
        break;

      case 'degraded':
        console.log('Request 1: ✅ Success (Circuit: CLOSED)');
        console.log('Request 2: ❌ Failure (Circuit: CLOSED, failures: 1)');
        console.log('Request 3: ✅ Success (Circuit: CLOSED, failures reset)');
        console.log('Request 4: ❌ Failure (Circuit: CLOSED, failures: 1)');
        console.log('→ Circuit remains CLOSED, intermittent failures handled');
        break;

      case 'outage':
        for (let i = 1; i <= failureThreshold; i++) {
          console.log(`Request ${i}: ❌ Failure (Circuit: CLOSED, failures: ${i})`);
        }
        console.log(`Request ${failureThreshold + 1}: 🚫 BLOCKED (Circuit: OPEN)`);
        console.log(`→ Circuit OPENED after ${failureThreshold} failures, blocking subsequent requests`);
        break;

      case 'recovery':
        // Show failure sequence
        for (let i = 1; i <= failureThreshold; i++) {
          console.log(`Request ${i}: ❌ Failure (Circuit: CLOSED, failures: ${i})`);
        }
        console.log(`→ Circuit OPENED, waiting ${config.recoveryTimeout || 30000}ms...`);
        console.log('→ Circuit transitions to HALF-OPEN');
        
        // Show recovery sequence
        for (let i = 1; i <= successThreshold; i++) {
          console.log(`Recovery ${i}: ✅ Success (Circuit: HALF-OPEN, successes: ${i})`);
        }
        console.log('→ Circuit CLOSED, normal operation restored');
        break;
    }
  }

  private async showConfigurationDetails(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('📋 Circuit Breaker Configuration Details');

    const currentConfig = this.configManager.getConfig().errorHandling?.circuitBreaker || {};

    this.utils.showInfo('Current Circuit Breaker Configuration:');
    console.log();
    console.log(`🔧 Basic Settings:`);
    console.log(`  • Enabled: ${currentConfig.enabled ? 'Yes' : 'No'}`);
    console.log(`  • Per-Service Breakers: ${currentConfig.enablePerServiceBreakers ? 'Yes' : 'No'}`);
    console.log(`  • Global Breaker: ${currentConfig.enableGlobalBreaker ? 'Yes' : 'No'}`);
    console.log();
    console.log(`📊 Thresholds:`);
    console.log(`  • Failure Threshold: ${currentConfig.failureThreshold || 5}`);
    console.log(`  • Success Threshold: ${currentConfig.successThreshold || 2}`);
    console.log(`  • Failure Rate Threshold: ${currentConfig.failureRateThreshold || 50}%`);
    console.log(`  • Minimum Requests: ${currentConfig.minimumRequests || 10}`);
    console.log();
    console.log(`⏱️ Timing:`);
    console.log(`  • Recovery Timeout: ${currentConfig.recoveryTimeout || 30000}ms`);
    console.log(`  • Monitoring Window: ${currentConfig.monitoringWindow || 60000}ms`);
    console.log(`  • Half-Open Timeout: ${currentConfig.halfOpenTimeout || 10000}ms`);

    if (currentConfig.perServiceSettings) {
      console.log();
      console.log(`🎛️ Per-Service Settings:`);
      Object.entries(currentConfig.perServiceSettings).forEach(([service, settings]: [string, any]) => {
        console.log(`  • ${service}: threshold=${settings.failureThreshold}, recovery=${settings.recoveryTimeout}ms`);
      });
    }

    console.log();
    await this.utils.waitForKeyPress();
  }

  private async resetCircuitBreakerConfig(): Promise<void> {
    const confirmed = await this.confirmAction(
      'This will reset all circuit breaker settings to defaults. Continue?'
    );

    if (!confirmed) {
      return;
    }

    try {
      await this.configManager.updateErrorHandlingConfig({
        circuitBreaker: {
          enabled: true,
          failureThreshold: 5,
          recoveryTimeout: 30000,
          successThreshold: 2,
          monitoringWindow: 60000,
          halfOpenTimeout: 10000,
          failureRateThreshold: 50,
          minimumRequests: 10,
          enablePerServiceBreakers: true,
          enableGlobalBreaker: false,
        },
      });
      this.utils.showSuccess('Circuit breaker configuration reset to defaults!');
    } catch (error) {
      this.utils.showError(`Failed to reset configuration: ${error instanceof Error ? error.message : String(error)}`);
    }

    await this.utils.waitForKeyPress();
  }
}
