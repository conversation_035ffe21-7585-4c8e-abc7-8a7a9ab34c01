"use strict";
/**
 * Error Handling System - Main Export File
 *
 * Comprehensive error handling system with:
 * - Retry policies with exponential backoff and jitter
 * - Circuit breaker pattern for service protection
 * - Error classification and categorization
 * - Metrics collection and monitoring
 * - Structured logging and debugging
 * - Centralized error handling middleware
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERROR_HANDLING_SYSTEM = exports.PRESET_RETRY_POLICIES = exports.DEFAULT_CIRCUIT_BREAKER_CONFIG = exports.DEFAULT_RETRY_CONFIG = exports.LogLevel = exports.ErrorLogger = exports.MetricsCollector = exports.ErrorHandlingMiddleware = exports.ErrorCategory = exports.ErrorClassifier = exports.CircuitBreakerError = exports.CircuitBreakerRegistry = exports.CircuitBreakerState = exports.CircuitBreaker = exports.JitterType = exports.RetryPolicy = void 0;
exports.createErrorHandlingMiddleware = createErrorHandlingMiddleware;
exports.createRetryPolicy = createRetryPolicy;
exports.createCircuitBreaker = createCircuitBreaker;
exports.isRetryableError = isRetryableError;
exports.getErrorCategory = getErrorCategory;
exports.createErrorHandlingContext = createErrorHandlingContext;
// Core Components
var RetryPolicy_1 = require("./RetryPolicy");
Object.defineProperty(exports, "RetryPolicy", { enumerable: true, get: function () { return RetryPolicy_1.RetryPolicy; } });
Object.defineProperty(exports, "JitterType", { enumerable: true, get: function () { return RetryPolicy_1.JitterType; } });
var CircuitBreaker_1 = require("./CircuitBreaker");
Object.defineProperty(exports, "CircuitBreaker", { enumerable: true, get: function () { return CircuitBreaker_1.CircuitBreaker; } });
Object.defineProperty(exports, "CircuitBreakerState", { enumerable: true, get: function () { return CircuitBreaker_1.CircuitBreakerState; } });
Object.defineProperty(exports, "CircuitBreakerRegistry", { enumerable: true, get: function () { return CircuitBreaker_1.CircuitBreakerRegistry; } });
Object.defineProperty(exports, "CircuitBreakerError", { enumerable: true, get: function () { return CircuitBreaker_1.CircuitBreakerError; } });
var ErrorClassifier_1 = require("./ErrorClassifier");
Object.defineProperty(exports, "ErrorClassifier", { enumerable: true, get: function () { return ErrorClassifier_1.ErrorClassifier; } });
Object.defineProperty(exports, "ErrorCategory", { enumerable: true, get: function () { return ErrorClassifier_1.ErrorCategory; } });
// Middleware
var ErrorHandlingMiddleware_1 = require("./ErrorHandlingMiddleware");
Object.defineProperty(exports, "ErrorHandlingMiddleware", { enumerable: true, get: function () { return ErrorHandlingMiddleware_1.ErrorHandlingMiddleware; } });
// Monitoring and Logging
var MetricsCollector_1 = require("./MetricsCollector");
Object.defineProperty(exports, "MetricsCollector", { enumerable: true, get: function () { return MetricsCollector_1.MetricsCollector; } });
var ErrorLogger_1 = require("./ErrorLogger");
Object.defineProperty(exports, "ErrorLogger", { enumerable: true, get: function () { return ErrorLogger_1.ErrorLogger; } });
Object.defineProperty(exports, "LogLevel", { enumerable: true, get: function () { return ErrorLogger_1.LogLevel; } });
// Utility Functions and Constants
exports.DEFAULT_RETRY_CONFIG = {
    maxAttempts: 3,
    baseDelay: 1000,
    backoffMultiplier: 2,
    maxDelay: 60000,
    backoffStrategy: 'exponential',
    enableJitter: true,
    jitterType: 'full',
    attemptTimeout: 30000
};
exports.DEFAULT_CIRCUIT_BREAKER_CONFIG = {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    successThreshold: 2,
    monitoringWindow: 60000,
    enabled: true
};
exports.PRESET_RETRY_POLICIES = {
    AGGRESSIVE: {
        maxAttempts: 5,
        baseDelay: 500,
        backoffMultiplier: 1.5,
        maxDelay: 10000,
        backoffStrategy: 'exponential',
        enableJitter: true,
        jitterType: 'full'
    },
    CONSERVATIVE: {
        maxAttempts: 2,
        baseDelay: 2000,
        backoffMultiplier: 2,
        maxDelay: 30000,
        backoffStrategy: 'exponential',
        enableJitter: true,
        jitterType: 'decorrelated'
    },
    NETWORK_OPTIMIZED: {
        maxAttempts: 3,
        baseDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 60000,
        backoffStrategy: 'exponential',
        enableJitter: true,
        jitterType: 'full',
        attemptTimeout: 15000
    },
    FILE_OPERATIONS: {
        maxAttempts: 2,
        baseDelay: 500,
        backoffMultiplier: 1.5,
        maxDelay: 5000,
        backoffStrategy: 'linear',
        enableJitter: false
    }
};
/**
 * Create a complete error handling middleware with default configuration
 */
function createErrorHandlingMiddleware(config) {
    const defaultConfig = {
        retry: exports.DEFAULT_RETRY_CONFIG,
        circuitBreaker: exports.DEFAULT_CIRCUIT_BREAKER_CONFIG,
        enableErrorLogging: true,
        enableMetrics: true,
        showRetryProgress: true,
        allowRetryCancel: true,
        operationPolicies: {
            toolExecution: exports.PRESET_RETRY_POLICIES.CONSERVATIVE,
            providerCalls: exports.PRESET_RETRY_POLICIES.NETWORK_OPTIMIZED,
            fileOperations: exports.PRESET_RETRY_POLICIES.FILE_OPERATIONS,
            networkRequests: exports.PRESET_RETRY_POLICIES.NETWORK_OPTIMIZED
        }
    };
    const mergedConfig = { ...defaultConfig, ...config };
    return new ErrorHandlingMiddleware(mergedConfig);
}
/**
 * Create a retry policy with preset configuration
 */
function createRetryPolicy(preset) {
    if (typeof preset === 'string') {
        return new RetryPolicy(exports.PRESET_RETRY_POLICIES[preset]);
    }
    return new RetryPolicy(preset);
}
/**
 * Create a circuit breaker with default configuration
 */
function createCircuitBreaker(serviceName, config) {
    const mergedConfig = { ...exports.DEFAULT_CIRCUIT_BREAKER_CONFIG, ...config };
    return new CircuitBreaker(serviceName, mergedConfig);
}
/**
 * Utility function to determine if an error is retryable
 */
function isRetryableError(error) {
    const classifier = new ErrorClassifier();
    const classification = classifier.classify(error);
    return classification.shouldRetry;
}
/**
 * Utility function to get error category
 */
function getErrorCategory(error) {
    const classifier = new ErrorClassifier();
    const classification = classifier.classify(error);
    return classification.category;
}
/**
 * Utility function to create an error handling context
 */
function createErrorHandlingContext(operationType, operationName, options) {
    return {
        operationType,
        operationName,
        serviceName: options?.serviceName,
        userId: options?.userId,
        sessionId: options?.sessionId,
        metadata: options?.metadata
    };
}
/**
 * Error handling system version and metadata
 */
exports.ERROR_HANDLING_SYSTEM = {
    version: '1.0.0',
    name: 'CLI Agent Error Handling System',
    description: 'Comprehensive error handling with retry policies, circuit breakers, and monitoring',
    features: [
        'Exponential backoff with jitter',
        'Circuit breaker pattern',
        'Error classification',
        'Metrics collection',
        'Structured logging',
        'Progress callbacks',
        'Cancellation support'
    ]
};
//# sourceMappingURL=index.js.map