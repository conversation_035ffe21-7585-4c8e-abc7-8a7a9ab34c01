/**
 * Comprehensive Error Logging System
 *
 * Provides structured logging for:
 * - Error events with context
 * - Retry attempts and outcomes
 * - Circuit breaker state changes
 * - Performance monitoring
 * - Debug information
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    FATAL = 4
}
export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    category: string;
    message: string;
    context?: Record<string, any>;
    error?: Error;
    stackTrace?: string;
    operationId?: string;
    userId?: string;
    sessionId?: string;
}
export interface LoggerConfig {
    level: LogLevel;
    enableConsoleOutput: boolean;
    enableFileOutput: boolean;
    enableStructuredLogging: boolean;
    maxLogEntries: number;
    logFilePath?: string;
    includeStackTrace: boolean;
    includeTimestamp: boolean;
    dateFormat: string;
}
export declare class ErrorLogger {
    private config;
    private logEntries;
    private logHandlers;
    constructor(config?: Partial<LoggerConfig>);
    /**
     * Log debug information
     */
    debug(message: string, context?: Record<string, any>, operationId?: string): void;
    /**
     * Log informational messages
     */
    info(message: string, context?: Record<string, any>, operationId?: string): void;
    /**
     * Log warnings
     */
    warn(message: string, context?: Record<string, any>, operationId?: string): void;
    /**
     * Log errors
     */
    error(message: string, error?: Error, context?: Record<string, any>, operationId?: string): void;
    /**
     * Log fatal errors
     */
    fatal(message: string, error?: Error, context?: Record<string, any>, operationId?: string): void;
    /**
     * Log retry attempt
     */
    logRetryAttempt(operation: string, attempt: number, maxAttempts: number, delay: number, error: Error, operationId?: string): void;
    /**
     * Log circuit breaker state change
     */
    logCircuitBreakerStateChange(serviceName: string, oldState: string, newState: string, reason?: string, operationId?: string): void;
    /**
     * Log operation start
     */
    logOperationStart(operation: string, parameters: Record<string, any>, operationId?: string): void;
    /**
     * Log operation completion
     */
    logOperationComplete(operation: string, success: boolean, duration: number, result?: any, operationId?: string): void;
    /**
     * Log performance metrics
     */
    logPerformanceMetrics(operation: string, metrics: {
        duration: number;
        memoryUsage?: number;
        cpuUsage?: number;
    }, operationId?: string): void;
    /**
     * Core logging method
     */
    private log;
    /**
     * Console log handler
     */
    private consoleLogHandler;
    /**
     * Format timestamp according to configuration
     */
    private formatTimestamp;
    /**
     * Add custom log handler
     */
    addLogHandler(handler: (entry: LogEntry) => void): void;
    /**
     * Remove log handler
     */
    removeLogHandler(handler: (entry: LogEntry) => void): void;
    /**
     * Get recent log entries
     */
    getRecentLogs(count?: number, level?: LogLevel): LogEntry[];
    /**
     * Search logs by criteria
     */
    searchLogs(criteria: {
        category?: string;
        operationId?: string;
        level?: LogLevel;
        startTime?: Date;
        endTime?: Date;
        messageContains?: string;
    }): LogEntry[];
    /**
     * Export logs as JSON
     */
    exportLogs(criteria?: {
        category?: string;
        level?: LogLevel;
        startTime?: Date;
        endTime?: Date;
    }): string;
    /**
     * Clear all logs
     */
    clearLogs(): void;
    /**
     * Update logger configuration
     */
    updateConfig(config: Partial<LoggerConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): LoggerConfig;
    /**
     * Get log statistics
     */
    getLogStatistics(): {
        totalLogs: number;
        logsByLevel: Record<string, number>;
        logsByCategory: Record<string, number>;
        oldestLog?: Date;
        newestLog?: Date;
    };
}
//# sourceMappingURL=ErrorLogger.d.ts.map