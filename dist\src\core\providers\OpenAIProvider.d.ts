import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';
export declare class OpenAIProvider extends BaseProvider {
    name: string;
    private client;
    constructor(config: ProviderConfig);
    sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    streamMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    private processResponse;
    private formatToolCalls;
    protected formatMessages(messages: ChatMessage[]): any[];
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    protected validateConfig(): void;
}
//# sourceMappingURL=OpenAIProvider.d.ts.map