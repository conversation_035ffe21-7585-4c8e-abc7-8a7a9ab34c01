{"version": 3, "file": "BaseProvider.d.ts", "sourceRoot": "", "sources": ["../../../src/core/providers/BaseProvider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAEjE,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE;QACN,YAAY,EAAE,MAAM,CAAC;QACrB,gBAAgB,EAAE,MAAM,CAAC;QACzB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,SAAS,CAAC,EAAE,KAAK,CAAC;QAChB,EAAE,EAAE,MAAM,CAAC;QACX,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACjC,CAAC,CAAC;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,8BAAsB,YAAa,YAAW,cAAc;IAC1D,SAAS,CAAC,MAAM,EAAE,cAAc,CAAC;IACjC,SAAgB,IAAI,EAAE,MAAM,CAAC;gBAEjB,MAAM,EAAE,cAAc;IAI3B,YAAY,IAAI,OAAO;aAId,WAAW,CACzB,QAAQ,EAAE,WAAW,EAAE,EACvB,KAAK,CAAC,EAAE,QAAQ,EAAE,EAClB,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC;aAEP,aAAa,CAAC,CAC5B,QAAQ,EAAE,WAAW,EAAE,EACvB,KAAK,CAAC,EAAE,QAAQ,EAAE,EAClB,OAAO,CAAC,EAAE,eAAe,GACxB,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC;IAEtC,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,GAAG,EAAE,GAAG;QAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QAAC,iBAAiB,EAAE,GAAG,EAAE,CAAA;KAAE;IAO/G,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,GAAG,GAAG,EAAE;IAehD,SAAS,CAAC,iBAAiB,CACzB,OAAO,EAAE,MAAM,EACf,IAAI,GAAE,WAAyB,EAC/B,QAAQ,CAAC,EAAE,GAAG,GACb,WAAW;IAcd,SAAS,CAAC,UAAU,IAAI,MAAM;IAK9B,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,KAAK;IA8BzD,SAAS,CAAC,cAAc,IAAI,IAAI;IAUhC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,eAAe;;;;;;IAS9C,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI;IAIpD,SAAS,IAAI,cAAc;IAIrB,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAiBlC,kBAAkB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;CAKrD"}