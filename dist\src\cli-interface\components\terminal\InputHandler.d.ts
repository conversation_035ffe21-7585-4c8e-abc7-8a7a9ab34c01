import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig, InputHandler as IInputHandler } from '../../types';
export declare class InputHandler extends BaseComponent {
    private inputHandlers;
    constructor(state: CLIState, config: CLIConfig);
    render(): Promise<void>;
    private processInput;
    getInput(): Promise<string>;
    getMultilineInput(): Promise<string>;
    getConfirmation(message: string, defaultValue?: boolean): Promise<boolean>;
    selectFromOptions<T>(message: string, options: Array<{
        name: string;
        value: T;
    }>, defaultValue?: T): Promise<T>;
    addInputHandler(handler: IInputHandler): void;
    removeInputHandler(pattern: RegExp): void;
    private setupDefaultHandlers;
    private createPrompt;
    private validateInput;
    private handleFileInput;
    private handleClipboardPaste;
    handleSpecialInput(input: string): Promise<string | null>;
    showInputHelp(): void;
}
//# sourceMappingURL=InputHandler.d.ts.map