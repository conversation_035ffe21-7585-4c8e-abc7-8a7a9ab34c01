/**
 * Error Classification System
 * 
 * Categorizes errors into different types and determines retry strategies:
 * - Transient vs Permanent errors
 * - Network, Authentication, Rate Limiting, etc.
 * - Configurable retry rules per error type
 */

export enum ErrorCategory {
  // Transient errors (should retry)
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  
  // Permanent errors (should not retry)
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  MALFORMED_REQUEST = 'MALFORMED_REQUEST',
  
  // Special cases
  CIRCUIT_BREAKER_ERROR = 'CIRCUIT_BREAKER_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ErrorClassificationRule {
  category: ErrorCategory;
  shouldRetry: boolean;
  shouldCircuitBreak: boolean;
  patterns: {
    messagePatterns?: string[];
    statusCodes?: number[];
    errorTypes?: string[];
    customMatcher?: (error: Error) => boolean;
  };
  retryConfig?: {
    maxAttempts?: number;
    baseDelay?: number;
    backoffMultiplier?: number;
    maxDelay?: number;
  };
}

export interface ErrorClassification {
  category: ErrorCategory;
  shouldRetry: boolean;
  shouldCircuitBreak: boolean;
  confidence: number; // 0-1, how confident we are in this classification
  matchedRule?: ErrorClassificationRule;
  suggestedAction?: string;
}

export class ErrorClassifier {
  private rules: ErrorClassificationRule[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * Classify an error and determine retry strategy
   */
  classify(error: Error): ErrorClassification {
    // Try each rule in order of specificity
    for (const rule of this.rules) {
      const confidence = this.matchRule(error, rule);
      if (confidence > 0) {
        return {
          category: rule.category,
          shouldRetry: rule.shouldRetry,
          shouldCircuitBreak: rule.shouldCircuitBreak,
          confidence,
          matchedRule: rule,
          suggestedAction: this.getSuggestedAction(rule.category)
        };
      }
    }

    // Default classification for unknown errors
    return {
      category: ErrorCategory.UNKNOWN_ERROR,
      shouldRetry: false,
      shouldCircuitBreak: false,
      confidence: 0.1,
      suggestedAction: 'Review error details and consider manual intervention'
    };
  }

  /**
   * Check if error matches a specific rule
   */
  private matchRule(error: Error, rule: ErrorClassificationRule): number {
    let matchScore = 0;
    let totalChecks = 0;

    const { patterns } = rule;

    // Check message patterns
    if (patterns.messagePatterns) {
      totalChecks++;
      const message = error.message.toLowerCase();
      const matches = patterns.messagePatterns.some(pattern => 
        message.includes(pattern.toLowerCase())
      );
      if (matches) matchScore++;
    }

    // Check status codes (for HTTP errors)
    if (patterns.statusCodes && 'status' in error) {
      totalChecks++;
      const status = (error as any).status;
      if (patterns.statusCodes.includes(status)) {
        matchScore++;
      }
    }

    // Check error types
    if (patterns.errorTypes) {
      totalChecks++;
      const errorType = error.constructor.name;
      if (patterns.errorTypes.includes(errorType)) {
        matchScore++;
      }
    }

    // Check custom matcher
    if (patterns.customMatcher) {
      totalChecks++;
      if (patterns.customMatcher(error)) {
        matchScore++;
      }
    }

    // Return confidence score (0-1)
    return totalChecks > 0 ? matchScore / totalChecks : 0;
  }

  /**
   * Initialize default error classification rules
   */
  private initializeDefaultRules(): void {
    this.rules = [
      // Network errors (transient)
      {
        category: ErrorCategory.NETWORK_ERROR,
        shouldRetry: true,
        shouldCircuitBreak: true,
        patterns: {
          messagePatterns: [
            'network', 'connection', 'econnreset', 'enotfound', 
            'econnrefused', 'socket hang up', 'dns', 'host'
          ],
          errorTypes: ['NetworkError', 'ConnectionError']
        },
        retryConfig: {
          maxAttempts: 3,
          baseDelay: 1000,
          backoffMultiplier: 2
        }
      },

      // Timeout errors (transient)
      {
        category: ErrorCategory.TIMEOUT_ERROR,
        shouldRetry: true,
        shouldCircuitBreak: true,
        patterns: {
          messagePatterns: ['timeout', 'timed out', 'deadline exceeded'],
          statusCodes: [408, 504]
        },
        retryConfig: {
          maxAttempts: 2,
          baseDelay: 2000,
          backoffMultiplier: 2
        }
      },

      // Rate limiting (transient with longer delays)
      {
        category: ErrorCategory.RATE_LIMIT_ERROR,
        shouldRetry: true,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: ['rate limit', 'too many requests', 'quota exceeded'],
          statusCodes: [429]
        },
        retryConfig: {
          maxAttempts: 3,
          baseDelay: 5000,
          backoffMultiplier: 2,
          maxDelay: 60000
        }
      },

      // Server errors (transient)
      {
        category: ErrorCategory.SERVER_ERROR,
        shouldRetry: true,
        shouldCircuitBreak: true,
        patterns: {
          statusCodes: [500, 502, 503, 504]
        },
        retryConfig: {
          maxAttempts: 2,
          baseDelay: 1000,
          backoffMultiplier: 2
        }
      },

      // Service unavailable (transient)
      {
        category: ErrorCategory.SERVICE_UNAVAILABLE,
        shouldRetry: true,
        shouldCircuitBreak: true,
        patterns: {
          messagePatterns: ['service unavailable', 'temporarily unavailable'],
          statusCodes: [503]
        }
      },

      // Authentication errors (permanent)
      {
        category: ErrorCategory.AUTHENTICATION_ERROR,
        shouldRetry: false,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: [
            'unauthorized', 'authentication', 'invalid api key', 
            'invalid token', 'expired token'
          ],
          statusCodes: [401]
        }
      },

      // Authorization errors (permanent)
      {
        category: ErrorCategory.AUTHORIZATION_ERROR,
        shouldRetry: false,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: ['forbidden', 'access denied', 'insufficient permissions'],
          statusCodes: [403]
        }
      },

      // Validation errors (permanent)
      {
        category: ErrorCategory.VALIDATION_ERROR,
        shouldRetry: false,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: [
            'validation', 'invalid input', 'bad request', 
            'malformed', 'invalid parameter'
          ],
          statusCodes: [400, 422]
        }
      },

      // Not found errors (permanent)
      {
        category: ErrorCategory.NOT_FOUND_ERROR,
        shouldRetry: false,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: ['not found', 'does not exist'],
          statusCodes: [404]
        }
      },

      // Circuit breaker errors (special handling)
      {
        category: ErrorCategory.CIRCUIT_BREAKER_ERROR,
        shouldRetry: false,
        shouldCircuitBreak: false,
        patterns: {
          messagePatterns: ['circuit breaker'],
          errorTypes: ['CircuitBreakerError']
        }
      }
    ];
  }

  /**
   * Get suggested action for error category
   */
  private getSuggestedAction(category: ErrorCategory): string {
    const actions: Record<ErrorCategory, string> = {
      [ErrorCategory.NETWORK_ERROR]: 'Check network connectivity and try again',
      [ErrorCategory.TIMEOUT_ERROR]: 'Increase timeout or try again later',
      [ErrorCategory.RATE_LIMIT_ERROR]: 'Wait before retrying or reduce request frequency',
      [ErrorCategory.SERVER_ERROR]: 'Server issue - try again later',
      [ErrorCategory.SERVICE_UNAVAILABLE]: 'Service is temporarily down - try again later',
      [ErrorCategory.AUTHENTICATION_ERROR]: 'Check API credentials and configuration',
      [ErrorCategory.AUTHORIZATION_ERROR]: 'Verify permissions and access rights',
      [ErrorCategory.VALIDATION_ERROR]: 'Check input parameters and format',
      [ErrorCategory.NOT_FOUND_ERROR]: 'Verify resource exists and path is correct',
      [ErrorCategory.MALFORMED_REQUEST]: 'Check request format and parameters',
      [ErrorCategory.CIRCUIT_BREAKER_ERROR]: 'Service is temporarily blocked - wait for recovery',
      [ErrorCategory.CONFIGURATION_ERROR]: 'Check application configuration',
      [ErrorCategory.UNKNOWN_ERROR]: 'Review error details and consider manual intervention'
    };

    return actions[category] || 'Unknown error - manual investigation required';
  }

  /**
   * Add custom classification rule
   */
  addRule(rule: ErrorClassificationRule): void {
    this.rules.unshift(rule); // Add to beginning for higher priority
  }

  /**
   * Remove classification rule
   */
  removeRule(category: ErrorCategory): void {
    this.rules = this.rules.filter(rule => rule.category !== category);
  }

  /**
   * Get all classification rules
   */
  getRules(): ErrorClassificationRule[] {
    return [...this.rules];
  }

  /**
   * Update existing rule
   */
  updateRule(category: ErrorCategory, updates: Partial<ErrorClassificationRule>): void {
    const ruleIndex = this.rules.findIndex(rule => rule.category === category);
    if (ruleIndex >= 0) {
      this.rules[ruleIndex] = { ...this.rules[ruleIndex], ...updates };
    }
  }

  /**
   * Check if error should be retried based on classification
   */
  shouldRetry(error: Error): boolean {
    const classification = this.classify(error);
    return classification.shouldRetry;
  }

  /**
   * Check if error should trigger circuit breaker
   */
  shouldCircuitBreak(error: Error): boolean {
    const classification = this.classify(error);
    return classification.shouldCircuitBreak;
  }

  /**
   * Get retry configuration for error
   */
  getRetryConfig(error: Error): ErrorClassificationRule['retryConfig'] | undefined {
    const classification = this.classify(error);
    return classification.matchedRule?.retryConfig;
  }
}
