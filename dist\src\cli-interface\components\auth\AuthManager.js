"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthManager = void 0;
const BaseComponent_1 = require("../common/BaseComponent");
const ProviderSelector_1 = require("./ProviderSelector");
const ProviderConfigurator_1 = require("./ProviderConfigurator");
const AuthMenu_1 = require("./AuthMenu");
const error_handling_1 = require("../../../core/error-handling");
const ProgressIndicator_1 = require("../common/ProgressIndicator");
class AuthManager extends BaseComponent_1.BaseComponent {
    configManager;
    providerSelector;
    providerConfigurator;
    authMenu;
    errorHandler;
    progressIndicator;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
        this.providerSelector = new ProviderSelector_1.ProviderSelector(state, config, configManager);
        this.providerConfigurator = new ProviderConfigurator_1.ProviderConfigurator(state, config, configManager);
        this.authMenu = new AuthMenu_1.AuthMenu(state, config, configManager);
        // Initialize error handling for authentication operations
        this.errorHandler = (0, error_handling_1.createErrorHandlingMiddleware)({
            showRetryProgress: true,
            allowRetryCancel: true,
            enableErrorLogging: true,
            enableMetrics: true
        });
        this.progressIndicator = new ProgressIndicator_1.CLIProgressIndicator(config.theme);
    }
    async render() {
        this.utils.clearScreen();
        this.utils.showBanner('🤖 AI CLI Terminal', 'Configure your AI providers to get started');
        const configuredProviders = this.configManager.getConfiguredProviders();
        if (configuredProviders.length === 0) {
            await this.setupFirstProvider();
        }
        else {
            await this.showMainMenu();
        }
    }
    async setupFirstProvider() {
        this.utils.showWarning('No AI providers configured. Let\'s set one up!');
        console.log();
        const selectedProvider = await this.providerSelector.selectProvider();
        if (selectedProvider) {
            await this.providerConfigurator.configureProvider(selectedProvider);
            // Mark as authenticated if configuration was successful
            if (this.configManager.isProviderConfigured(selectedProvider)) {
                this.updateState({
                    isAuthenticated: true,
                    currentProvider: selectedProvider,
                    currentModel: this.configManager.getProviderConfig(selectedProvider).defaultModel
                });
            }
        }
    }
    async showMainMenu() {
        await this.authMenu.show();
        // Check if user chose to start terminal
        if (this.state.currentView === 'terminal') {
            const config = this.configManager.getConfig();
            this.updateState({
                isAuthenticated: true,
                currentProvider: config.defaultProvider,
                currentModel: this.configManager.getProviderConfig().defaultModel
            });
        }
    }
    async handleProviderConfiguration(providerName) {
        await this.providerConfigurator.configureProvider(providerName);
    }
    async handleProviderSelection() {
        return await this.providerSelector.selectProvider();
    }
    isAuthenticated() {
        return this.configManager.getConfiguredProviders().length > 0;
    }
    getCurrentProvider() {
        const config = this.configManager.getConfig();
        return config.defaultProvider;
    }
    async switchProvider(providerName) {
        if (!this.configManager.isProviderConfigured(providerName)) {
            throw new Error(`Provider ${providerName} is not configured`);
        }
        await this.configManager.setDefaultProvider(providerName);
        const providerConfig = this.configManager.getProviderConfig(providerName);
        this.updateState({
            currentProvider: providerName,
            currentModel: providerConfig.defaultModel
        });
        this.utils.showSuccess(`Switched to ${providerName}`);
    }
    /**
     * Test provider connection with comprehensive error handling
     */
    async testProviderConnection(providerType) {
        const progressCallback = {
            onRetryAttempt: (attempt, maxAttempts, delay, error) => {
                this.progressIndicator.update(`Connection test failed (attempt ${attempt}/${maxAttempts}). Retrying in ${Math.round(delay / 1000)}s...`);
            },
            onCircuitBreakerOpen: (serviceName) => {
                this.progressIndicator.fail(`${serviceName} service is temporarily unavailable.`);
            },
            onCircuitBreakerClosed: (serviceName) => {
                this.progressIndicator.succeed(`${serviceName} service connection restored.`);
            }
        };
        try {
            this.progressIndicator.start(`Testing ${providerType} connection...`);
            const result = await this.errorHandler.executeWithErrorHandling(async () => {
                // Get the provider instance and test connection
                const provider = this.configManager.getProvider(providerType);
                if (!provider) {
                    throw new Error(`Provider ${providerType} not found or not configured`);
                }
                return await provider.testConnection();
            }, {
                operationName: 'provider_connection_test',
                operationType: 'providerCall',
                serviceName: providerType
            }, progressCallback);
            if (result.success) {
                this.progressIndicator.succeed(`✅ ${providerType} connection successful!`);
                return true;
            }
            else {
                this.progressIndicator.fail(`❌ ${providerType} connection failed: ${result.userMessage}`);
                // Show helpful error messages based on error type
                if (result.userMessage.includes('authentication') || result.userMessage.includes('API key')) {
                    this.utils.showWarning('💡 Tip: Check your API key configuration in the provider settings.');
                }
                else if (result.userMessage.includes('network') || result.userMessage.includes('timeout')) {
                    this.utils.showWarning('💡 Tip: Check your internet connection and try again.');
                }
                else if (result.userMessage.includes('rate limit')) {
                    this.utils.showWarning('💡 Tip: You may have exceeded the rate limit. Please wait before trying again.');
                }
                return false;
            }
        }
        catch (error) {
            this.progressIndicator.fail(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }
    /**
     * Configure provider with error handling and validation
     */
    async configureProviderWithValidation(providerType, config) {
        try {
            this.progressIndicator.start(`Configuring ${providerType}...`);
            // Save configuration
            await this.configManager.setProviderConfig(providerType, config);
            // Test the connection to validate the configuration
            const connectionSuccess = await this.testProviderConnection(providerType);
            if (connectionSuccess) {
                this.progressIndicator.succeed(`✅ ${providerType} configured and validated successfully!`);
                return true;
            }
            else {
                this.progressIndicator.fail(`❌ ${providerType} configuration failed validation.`);
                return false;
            }
        }
        catch (error) {
            this.progressIndicator.fail(`❌ Configuration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }
}
exports.AuthManager = AuthManager;
//# sourceMappingURL=AuthManager.js.map