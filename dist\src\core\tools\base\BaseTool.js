"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseTool = void 0;
class BaseTool {
    metadata;
    constructor(metadata) {
        this.metadata = metadata;
    }
    async shouldConfirmExecute(params) {
        // Default implementation - can be overridden
        return this.requiresConfirmation || this.metadata.dangerous;
    }
    getMetadata() {
        return { ...this.metadata };
    }
    getCategory() {
        return this.metadata.category;
    }
    getTags() {
        return [...this.metadata.tags];
    }
    isDangerous() {
        return this.metadata.dangerous;
    }
    getVersion() {
        return this.metadata.version;
    }
    createSuccessResult(content, displayContent, metadata) {
        return {
            success: true,
            content,
            displayContent,
            metadata,
        };
    }
    createErrorResult(error, metadata) {
        return {
            success: false,
            content: '',
            error,
            metadata,
        };
    }
    validateRequiredParams(params, requiredParams) {
        const errors = [];
        const warnings = [];
        for (const param of requiredParams) {
            if (!(param in params) || params[param] === undefined || params[param] === null) {
                errors.push(`Required parameter '${param}' is missing`);
            }
            else if (typeof params[param] === 'string' && params[param].trim() === '') {
                errors.push(`Required parameter '${param}' cannot be empty`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    validateStringParam(value, paramName, options) {
        const errors = [];
        if (typeof value !== 'string') {
            errors.push(`Parameter '${paramName}' must be a string`);
            return errors;
        }
        if (!options?.allowEmpty && value.trim() === '') {
            errors.push(`Parameter '${paramName}' cannot be empty`);
            return errors;
        }
        if (options?.minLength && value.length < options.minLength) {
            errors.push(`Parameter '${paramName}' must be at least ${options.minLength} characters`);
        }
        if (options?.maxLength && value.length > options.maxLength) {
            errors.push(`Parameter '${paramName}' must be at most ${options.maxLength} characters`);
        }
        if (options?.pattern && !options.pattern.test(value)) {
            errors.push(`Parameter '${paramName}' does not match required pattern`);
        }
        return errors;
    }
    validateNumberParam(value, paramName, options) {
        const errors = [];
        if (typeof value !== 'number' || isNaN(value)) {
            errors.push(`Parameter '${paramName}' must be a valid number`);
            return errors;
        }
        if (options?.integer && !Number.isInteger(value)) {
            errors.push(`Parameter '${paramName}' must be an integer`);
        }
        if (options?.min !== undefined && value < options.min) {
            errors.push(`Parameter '${paramName}' must be at least ${options.min}`);
        }
        if (options?.max !== undefined && value > options.max) {
            errors.push(`Parameter '${paramName}' must be at most ${options.max}`);
        }
        return errors;
    }
    validateBooleanParam(value, paramName) {
        if (typeof value !== 'boolean') {
            return [`Parameter '${paramName}' must be a boolean`];
        }
        return [];
    }
    validateArrayParam(value, paramName, options) {
        const errors = [];
        if (!Array.isArray(value)) {
            errors.push(`Parameter '${paramName}' must be an array`);
            return errors;
        }
        if (options?.minLength && value.length < options.minLength) {
            errors.push(`Parameter '${paramName}' must have at least ${options.minLength} items`);
        }
        if (options?.maxLength && value.length > options.maxLength) {
            errors.push(`Parameter '${paramName}' must have at most ${options.maxLength} items`);
        }
        if (options?.itemType) {
            for (let i = 0; i < value.length; i++) {
                const item = value[i];
                const itemType = typeof item;
                if (options.itemType === 'object' && (itemType !== 'object' || item === null)) {
                    errors.push(`Parameter '${paramName}[${i}]' must be an object`);
                }
                else if (options.itemType !== 'object' && itemType !== options.itemType) {
                    errors.push(`Parameter '${paramName}[${i}]' must be a ${options.itemType}`);
                }
            }
        }
        return errors;
    }
    validatePathParam(value, paramName) {
        const errors = this.validateStringParam(value, paramName);
        if (errors.length > 0)
            return errors;
        const path = value;
        // Check for path traversal attempts
        if (path.includes('..') || path.includes('~')) {
            errors.push(`Parameter '${paramName}' contains invalid path characters`);
        }
        // Check for absolute paths (might be restricted in some contexts)
        if (path.startsWith('/') || /^[A-Za-z]:/.test(path)) {
            // This is an absolute path - might be valid depending on context
            // Individual tools can add additional validation
        }
        return errors;
    }
    sanitizePath(path) {
        // Remove dangerous path components
        return path
            .replace(/\.\./g, '') // Remove parent directory references
            .replace(/~/g, '') // Remove home directory references
            .replace(/\/+/g, '/') // Normalize multiple slashes
            .trim();
    }
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    truncateContent(content, maxLength = 1000) {
        if (content.length <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength - 3) + '...';
    }
    toString() {
        return `${this.name} (${this.metadata.category})`;
    }
}
exports.BaseTool = BaseTool;
//# sourceMappingURL=BaseTool.js.map