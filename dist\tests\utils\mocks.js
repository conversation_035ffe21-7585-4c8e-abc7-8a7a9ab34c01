"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitError = exports.TimeoutError = exports.NetworkError = exports.TestError = exports.createMockInquirer = exports.createMockCLIUtils = exports.createMockErrorHandler = exports.createMockConfigManager = exports.createMockErrorHandlingConfig = exports.createMockAppConfig = exports.createMockCLIConfig = exports.createMockCLIState = void 0;
const globals_1 = require("@jest/globals");
// Mock CLI State
const createMockCLIState = (overrides = {}) => ({
    isAuthenticated: true,
    currentView: 'main',
    currentProvider: 'openai',
    sessionId: 'test-session-123',
    ...overrides
});
exports.createMockCLIState = createMockCLIState;
// Mock CLI Config
const createMockCLIConfig = (overrides = {}) => ({
    theme: {
        primary: 'blue',
        secondary: 'green',
        accent: 'yellow',
        background: 'black',
        text: 'white'
    },
    ...overrides
});
exports.createMockCLIConfig = createMockCLIConfig;
// Mock App Config
const createMockAppConfig = (overrides = {}) => ({
    defaultProvider: 'openai',
    configDir: '~/.ai-cli-terminal',
    providers: {},
    tools: {
        enableShellTools: true,
        enableWebTools: true,
        enableFileTools: true,
        enableMemoryTools: true,
        requireConfirmation: true,
        sandboxMode: false,
        maxFileSize: '10MB',
        allowedCommands: ['ls', 'cat', 'grep'],
        blockedCommands: ['rm', 'sudo']
    },
    security: {
        requireToolConfirmation: true,
        sandboxMode: false,
        maxFileSize: 10 * 1024 * 1024,
        allowedPaths: [],
        blockedPaths: [],
        allowedDomains: [],
        blockedDomains: []
    },
    ui: {
        theme: 'default',
        showTimestamps: true,
        showTokenCounts: true,
        maxHistorySize: 1000,
        autoSave: true,
        streamResponses: true
    },
    systemPrompts: {
        prompts: {},
        categories: ['General'],
        activePromptId: undefined,
        defaultPromptId: undefined
    },
    errorHandling: (0, exports.createMockErrorHandlingConfig)(),
    preferences: {
        requireToolConfirmation: true
    },
    ...overrides
});
exports.createMockAppConfig = createMockAppConfig;
// Mock Error Handling Configuration
const createMockErrorHandlingConfig = (overrides = {}) => ({
    retry: {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 60000,
        backoffMultiplier: 2,
        backoffStrategy: 'exponential',
        enableJitter: true,
        jitterType: 'full',
        attemptTimeout: 30000,
        ...overrides.retry
    },
    circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        recoveryTimeout: 30000,
        successThreshold: 2,
        monitoringWindow: 60000,
        halfOpenTimeout: 10000,
        failureRateThreshold: 50,
        minimumRequests: 10,
        enablePerServiceBreakers: true,
        enableGlobalBreaker: false,
        ...overrides.circuitBreaker
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: true,
    allowRetryCancel: true,
    operationPolicies: {
        toolExecution: {
            maxAttempts: 2,
            baseDelay: 1000,
            backoffMultiplier: 2
        },
        providerCalls: {
            maxAttempts: 3,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 30000
        },
        fileOperations: {
            maxAttempts: 2,
            baseDelay: 500,
            backoffMultiplier: 1.5
        },
        networkRequests: {
            maxAttempts: 3,
            baseDelay: 1000,
            backoffMultiplier: 2,
            maxDelay: 60000
        }
    },
    ...overrides
});
exports.createMockErrorHandlingConfig = createMockErrorHandlingConfig;
// Mock ConfigManager
const createMockConfigManager = () => ({
    getConfig: globals_1.jest.fn().mockReturnValue((0, exports.createMockAppConfig)()),
    getErrorHandlingConfig: globals_1.jest.fn().mockReturnValue((0, exports.createMockErrorHandlingConfig)()),
    updateErrorHandlingConfig: globals_1.jest.fn().mockResolvedValue(undefined),
    resetErrorHandlingConfig: globals_1.jest.fn().mockResolvedValue(undefined),
    saveConfig: globals_1.jest.fn().mockResolvedValue(undefined),
    loadConfig: globals_1.jest.fn().mockResolvedValue((0, exports.createMockAppConfig)())
});
exports.createMockConfigManager = createMockConfigManager;
// Mock Error Handler
const createMockErrorHandler = () => ({
    getMetrics: globals_1.jest.fn().mockResolvedValue({
        totalOperations: 100,
        successfulOperations: 95,
        failedOperations: 5,
        operationsWithRetries: 8,
        retryMetrics: {
            totalRetryAttempts: 15,
            successfulRetries: 12,
            failedRetries: 3,
            averageRetryDelay: 2000,
            retryAttemptDistribution: { '1': 5, '2': 2, '3': 1 },
            recentTrends: {
                retryRate: 8.0,
                averageDelay: 2000,
                peakRetryTime: '2024-01-15 14:30:00'
            }
        },
        circuitBreakerMetrics: {
            globalStatus: 'CLOSED',
            perServiceStatus: { openai: 'CLOSED', anthropic: 'CLOSED' },
            totalTrips: 1,
            totalRecoveries: 1,
            currentOpenCircuits: 0,
            averageRecoveryTime: 30000,
            recentEvents: []
        },
        perServiceMetrics: {
            openai: {
                totalRequests: 80,
                successRate: 96,
                averageResponseTime: 1200,
                errorRate: 4,
                circuitBreakerStatus: 'CLOSED'
            }
        },
        performanceMetrics: {
            averageOperationTime: 1200,
            slowestOperationTime: 5000,
            fastestOperationTime: 200,
            p95ResponseTime: 2500,
            p99ResponseTime: 4000,
            trends: {
                responseTimeTrend: 'stable',
                errorRateTrend: 'improving',
                throughputTrend: 'increasing'
            },
            resourceUtilization: {
                memoryUsage: 128,
                cpuUsage: 15,
                activeConnections: 3
            }
        },
        recentErrors: []
    }),
    updateConfig: globals_1.jest.fn(),
    executeWithRetry: globals_1.jest.fn(),
    executeWithCircuitBreaker: globals_1.jest.fn()
});
exports.createMockErrorHandler = createMockErrorHandler;
// Mock CLIUtils
const createMockCLIUtils = () => ({
    clearScreen: globals_1.jest.fn(),
    showBanner: globals_1.jest.fn(),
    showSuccess: globals_1.jest.fn(),
    showError: globals_1.jest.fn(),
    showWarning: globals_1.jest.fn(),
    showInfo: globals_1.jest.fn(),
    waitForKeyPress: globals_1.jest.fn().mockResolvedValue(undefined),
    prompt: globals_1.jest.fn(),
    confirm: globals_1.jest.fn().mockResolvedValue(true),
    select: globals_1.jest.fn(),
    multiSelect: globals_1.jest.fn(),
    input: globals_1.jest.fn(),
    number: globals_1.jest.fn(),
    password: globals_1.jest.fn()
});
exports.createMockCLIUtils = createMockCLIUtils;
// Mock Inquirer
const createMockInquirer = () => ({
    prompt: globals_1.jest.fn().mockResolvedValue({}),
    createPromptModule: globals_1.jest.fn()
});
exports.createMockInquirer = createMockInquirer;
// Error classes for testing
class TestError extends Error {
    code;
    retryable;
    constructor(message, code, retryable = true) {
        super(message);
        this.code = code;
        this.retryable = retryable;
        this.name = 'TestError';
    }
}
exports.TestError = TestError;
class NetworkError extends Error {
    constructor(message = 'Network error') {
        super(message);
        this.name = 'NetworkError';
    }
}
exports.NetworkError = NetworkError;
class TimeoutError extends Error {
    constructor(message = 'Operation timed out') {
        super(message);
        this.name = 'TimeoutError';
    }
}
exports.TimeoutError = TimeoutError;
class RateLimitError extends Error {
    constructor(message = 'Rate limit exceeded') {
        super(message);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
//# sourceMappingURL=mocks.js.map